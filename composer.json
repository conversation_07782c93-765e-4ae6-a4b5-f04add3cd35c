{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "ext-curl": "*", "ext-fileinfo": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-zip": "*", "alaouy/youtube": "^2.2", "altek/accountant": "^4.0", "altek/eventually": "^3.0", "archilex/filament-toggle-icon-column": "^3.0", "awcodes/filament-badgeable-column": "^2.3", "awcodes/filament-table-repeater": "^3.1", "awcodes/filament-tiptap-editor": "^3.0", "awcodes/shout": "^2.0", "bezhansalleh/filament-shield": "^3.1", "chrisreedio/socialment": "^3.4", "codewithdennis/filament-select-tree": "^3.1", "convertapi/convertapi-php": "^3.0", "cweagans/composer-patches": "^1.7", "cyrildewit/eloquent-viewable": "^7.0", "dacoto/laravel-domain-validation": "^4.0", "deeplcom/deepl-php": "^1.8", "defectivecode/laravel-sqs-extended": "^1.0", "eduardokum/laravel-mail-auto-embed": "^2.11", "eightynine/filament-approvals": "^3.0", "filament/filament": "^3.2", "filament/spatie-laravel-settings-plugin": "^3.2", "filament/spatie-laravel-tags-plugin": "^3.2", "filament/spatie-laravel-translatable-plugin": "^3.2", "flowframe/laravel-trend": "^0.2", "google/cloud-translate": "^1.20", "guzzlehttp/guzzle": "^7.8", "ibrahimbougaoua/filament-rating-star": "dev-main", "itsgoingd/clockwork": "^5.1", "jaocero/radio-deck": "^1.1", "jeffgreco13/filament-breezy": "^2.3", "jibaymcs/filament-tour": "^3.0", "joshembling/image-optimizer": "^1.2", "kclauhk/youtube-downloader": "^4.2", "laravel/cashier": "^15.0", "laravel/fortify": "^1.20", "laravel/framework": "^11.0", "laravel/horizon": "^5.30", "laravel/sanctum": "^4.0", "laravel/serializable-closure": "^1.3", "laravel/telescope": "^5.0", "laravel/tinker": "^2.9", "laravel/vapor-core": "^2.37", "league/flysystem-aws-s3-v3": "^3.0", "league/html-to-markdown": "^5.1", "leandrocfe/filament-apex-charts": "^3.1", "maartenpaauw/pennant-for-filament": "^1.3", "malzariey/filament-daterangepicker-filter": "^2.3", "mobiledetect/mobiledetectlib": "^4.8", "mrmysql/youtube-transcript": "^0.0.5", "opcodes/spike": "^3.21", "openai-php/client": "^0.10.3", "openai-php/laravel": "^0.11.0", "orangehill/iseed": "^3.0", "osiemsiedem/laravel-autolink": "^8.0", "phanan/poddle": "^1.0", "predis/predis": "~1.0", "pxlrbt/filament-activity-log": "^1.0", "pxlrbt/filament-environment-indicator": "^2.0", "ralphjsmit/laravel-filament-record-finder": "^1.2", "rickdbcn/filament-email": "^1.0", "roach-php/laravel": "^3.1", "rupadana/filament-api-service": "^3.2", "sawirricardo/filament-nouislider": "^0.0.5", "sendgrid/sendgrid": "^8.1", "sentry/sentry-laravel": "^4.9", "shuvroroy/filament-spatie-laravel-backup": "^2.1", "shuvroroy/filament-spatie-laravel-health": "^2.0", "socialiteproviders/apple": "^5.6", "socialiteproviders/facebook": "^4.1", "socialiteproviders/google": "^4.1", "socialiteproviders/linkedin": "^4.2", "socialiteproviders/microsoft": "^4.2", "socialiteproviders/twitter": "^4.1", "spatie/pdf-to-text": "^1.54", "staudenmeir/laravel-adjacency-list": "^1.21", "stechstudio/filament-impersonate": "^3.15", "stephenjude/filament-debugger": "^3.0", "tapp/filament-authentication-log": "^3.0", "torann/geoip": "^3.0", "vedmant/laravel-feed-reader": "^1.6", "willvincent/laravel-rateable": "^3.3"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.7", "phpunit/phpunit": "^11.0.1", "spatie/laravel-horizon-watcher": "^1.1"}, "autoload": {"files": ["app/helpers.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Aws\\Script\\Composer\\Composer::removeUnusedServices", "Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-install-cmd": [], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}, "aws/aws-sdk-php": ["ApiGatewayV2", "CloudFront", "CloudWatch", "DynamoDb", "ElastiCache", "ElasticLoadBalancingV2", "Lambda", "Rds", "Route53", "S3", "Ses", "Sns", "Sqs"], "dev-files": {"/": ["tests/", "docs/", ".travis.yml"], "*/*": ["*.sh", "/src/**/*.md"], "twig/twig": ["doc/"], "symfony/*": ["Tests/"]}, "patches": {"rupadana/filament-api-service": {"update package: update function": "patches/filament-api-service.diff"}, "jibaymcs/filament-tour": {"update package: update function": "patches/filament-tour.diff"}, "rappasoft/laravel-authentication-log": {"update package: update function": "patches/laravel-authentication-log.diff"}}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "cweagans/composer-patches": true, "ralphjsmit/packages": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": [{"type": "composer", "url": "https://satis.ralphjsmit.com"}, {"type": "composer", "url": "https://pennant-for-filament.composer.sh"}, {"type": "composer", "url": "https://spike.composer.sh"}]}