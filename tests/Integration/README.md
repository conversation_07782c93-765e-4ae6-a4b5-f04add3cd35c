# Apologist UI & API Automation

## Table Of Contents

1. [Project Setup](#project-setup "Setup")
2. [<PERSON> Playwright](#run-playwright-scripts "Playwright Scripts")

---

---

### Project Setup

Things you will need before running the test project:
[Nodejs](https://nodejs.org/en/download/package-manager "Node Install") : Node has instructions on the site for how to install

[VS Code](https://code.visualstudio.com/ "Code Editor"): VS Code has instructions on the site for how to install

[Git](https://git-scm.com/ "version control system"): Git has instructions on the site for how to install

When starting out with this automation you will need to have cloned this repository: Ignite

From there browse to `tests -> Integration` inside integration you will see <PERSON><PERSON> is setup. You will need to create your own `.env`

In your `.env` you should have the following structure:

```environment
API_STAGE = ""
API_PROD=''
API_DEV= ''
STAGING=0
PROD_BASE_URL='https://ignite.apologist.com/'
EMAIL=''
PASSWORD= ''
STAGE_BASE_URL=''
STAGE_EMAIL=''
DEV_BASE_URL=''
DEV_EMAIL=''
ADMIN_PROD_BASE_URL='https://admin.apologist.com'
```

If you have access to prod or a different environment site you can fill that information into your env.

Also prefixes:
API = the API url's

Admin = Admin url *Note more will be added later for now just Prod

Environment ONLY ie: Stage, Dev, Prod  = Just UI testing

After you setup your `.env` be sure to run in your terminal in the `Integration Folder`

Install :  `npm ci`   in your terminal

Do this often as things can change and new packages could be added.

---

#### Run Playwright

---

There are a few scripts that you can run in a headless mode.

```bash

    "staging": "STAGING=1 npx playwright test",
    "api:staging": "npx playwright test --workers=1 --project='API - Staging Testing' tests/api",
    "ui:e2e:prod": "npx playwright test --project='UI - Production Desktop E2E Chrome' tests/e2e",
    "ui:e2e:admin:staging": "npx playwright test --project='UI - Admin Staging Desktop E2E Chrome' tests/admin",
    "ui:e2e:admin:dev": "npx playwright test --project='UI - Admin Development Desktop E2E Chrome' tests/admin",
    "ui:e2e:admin:prod": "npx playwright test --project='UI - Admin Production Desktop E2E Chrome' tests/admin",
    "ui:prompt:dev": "npx playwright test --project='UI - Agent Prompt and Admin Development Testing' tests/agentUI",
    "ui:prompt:staging": "npx playwright test --project='UI - Agent Prompt and Admin Staging Testing' tests/agentUI",
    "ui:prompt:prod": "npx playwright test --ui --project='UI - Agent Prompt and Admin Production Testing' tests/agentUI",
  
```

`npm run api:staging` - this will run some test created currently in the staging API from your env file if you have it setup.

`npm run ui:e2e:prod` - this will run some test for End To End testing of the UI in production (this is for now in the future we should have our test set to an lower environment)

`npm run ui:e2e:admin:prod` - this will run some test for End to End testing of the UI in Admin Production site  ( this is for now in the future we should have our test set to an lower environment )
