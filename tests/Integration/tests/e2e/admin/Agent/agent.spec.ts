import { test, expect } from '@playwright/test';
// eslint-disable-next-line @typescript-eslint/no-require-imports
const fs = require('fs-extra');
import path from 'path';

// Load instructions from a file. Replace this with your own method.
const filePath = path.join(__dirname, '../../../../data/instructions.txt');

test('Agent - Agent Page', async ({ page }) => {
    await page.goto('');
    await page.getByRole('button', { name: 'Skip the Tour' }).click();
    await page.getByLabel('User menu').click();
    await page.getByRole('link', { name: 'Administration' }).click();
    await page.waitForURL('https://admin.apologist.com/');
    // Wait for the page to load and for a heading to be visible.
    await expect(page.getByRole('heading', { name: 'Dashboard' })).toBeVisible();
    await expect(page.getByText('Administration').nth(1)).toHaveText('Administration');
    await page.getByText('Administration').nth(1).click();
    await expect(page.getByText('Agents')).toBeVisible();
    await page.getByText('Agents').click();

    const heading = page.locator('h1', { hasText: 'Agents' });

    expect(await heading.innerText()).toContain('Agents');
    await page.getByRole('link', { name: 'testingagent' }).click();
    await expect(page.getByRole('heading', { name: 'testingagent' })).toBeVisible();
    await page.getByRole('tab', { name: 'Instructions' }).click();
    await expect(page.getByText('Parent Agent', { exact: true })).toBeVisible();

    const instructions = fs.readFileSync(filePath, 'utf8');

    await page.getByLabel('', { exact: true }).click();
    await page.getByLabel('', { exact: true }).fill(instructions);
    await page.getByRole('button', { name: 'Save Changes' }).click();

    const validateInstruction = await page.getByLabel('', { exact: true });

    await expect(validateInstruction).toHaveValue(instructions);
});
