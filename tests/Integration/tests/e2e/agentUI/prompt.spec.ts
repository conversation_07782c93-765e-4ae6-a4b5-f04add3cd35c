import {test , expect} from '@playwright/test';

test('should be able to see the prompt', async ({ page }) =>{
    await page.goto('https://testingagent.apologist.ai/en');


    await expect(page.getByPlaceholder('Ask a question here')).toBeVisible();

    await page.getByPlaceholder('Ask a question here').fill('How can Jesus be both God and man?');
    await page.getByRole('button', { name: 'Submit Prompt' }).click();
    await page.waitForTimeout(10000);

    expect(await page.locator('#apg-messages-list div').filter({ hasText: 'How can Jesus be both God and' })).toBeTruthy();
    await expect(page.getByText('Jesus can be both God and man')).toBeVisible();

});

test('should be able to change question with CTA button', async ({ page }) => {
    await page.goto('https://testingagent.apologist.ai/en');

    await expect(page.getByPlaceholder('Ask a question here')).toBeVisible();

    await page.getByRole('button', { name: 'hero.cta' }).waitFor({ state: 'visible' });
    await page.getByRole('button', { name: 'hero.cta' }).click();
    await page.getByRole('button', { name: 'Submit Prompt' }).click();

    expect(page.locator('#apg-messages-list div').isVisible).toBeTruthy();
});
