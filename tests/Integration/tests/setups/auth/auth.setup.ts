import { test as setup} from 'fixtures/userOptions/userOptions';
import path from 'path';

const authFile = path.join(__dirname, '../playwright/.auth/user.json');

    setup('Login Test', async ({ page, email, password}) => {
        await page.goto('https://id.apologist.com/login');

        await page.fill('input[type="email"]', email);
        await page.fill('input[type="password"]', password);
        await page.getByRole('button', { name: 'Sign in' }).click();
        await page.waitForURL('https://id.apologist.com/');

        await page.context().storageState({ path: authFile });
    });
