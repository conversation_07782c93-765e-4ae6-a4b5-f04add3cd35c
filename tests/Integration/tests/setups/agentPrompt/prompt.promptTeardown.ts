import { expect, Locator, Page, test as promptTeardown } from '@playwright/test';

promptTeardown('Agent Prompt Teardown', async ({ page }) => {
        await page.goto('', { waitUntil: 'commit' });

        await page.waitForURL('https://admin.apologist.com/');
        await page.getByText('Administration').nth(1).click();
        await page.getByText('Agents').click();
        await page.getByRole('link', { name: 'testingagent' }).click();
        await page.getByRole('tab', { name: 'Messaging' }).click();
        await page.waitForTimeout(3000);
        await expect(page.getByText('Sample Questions')).toBeVisible();

        const isActive = page.getByRole('button', { name: 'Inactive' });
        const isActiveVisible = await isActive.isVisible();
        const editButton = page.getByRole('button', { name: 'Edit', exact: true });

        if (isActiveVisible) {
            await isActive.click();
            await activateQuestion(page, editButton);
        }
        if(!isActiveVisible){
        await archiveQuestion(page, editButton);}

});

async function activateQuestion(page: Page, editButton: Locator) {
    await page.screenshot({path: './PromptTeardown.png', fullPage: true});
    await editButton.focus();
    await editButton.click({ force: true });
    await page.getByText('Close Edit Question').isVisible();
    page.locator('.fi-modal-heading').waitFor({state: 'visible'});
    await page.getByRole('switch', { name: 'Active' }).waitFor({state: 'visible'});
    await page.getByRole('switch', { name: 'Active', exact: true}).click();
    page.locator('#btn-save-relation').click();
}

async function archiveQuestion(page: Page, editButton: Locator) {
    await editButton.focus();
    await editButton.click({ force: true });
    await page.getByText('Close Edit Question').isVisible();
    await page.locator('.fi-modal-heading').waitFor({state: 'visible'});
    await page.getByRole('switch', { name: 'Active' }).waitFor({state: 'visible'});
    await page.getByRole('switch', { name: 'Active', exact: true}).click();
    page.locator('#btn-save-relation').click();
    page.locator('#relationManager0').getByRole('button', { name: 'Archive' }).click();
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForTimeout(1000);
}
