import { test as promptSetup } from '@playwright/test';

promptSetup('Agent Prompt Setup', async ({ page }) => {
    await page.goto('', { waitUntil: 'commit' });
    await page.waitForURL('https://admin.apologist.com/');

    await page.getByText('Administration').nth(1).click();
    await page.getByText('Agents').click();

    await page.getByRole('link', { name: 'testingagent' }).click();

    await page.getByRole('tab', { name: 'Messaging' }).click();


    await page
        .getByRole('button', { name: 'Add Question', exact: true })
        .click({ force: true });

    const modal = page.locator('text=Add Question');

    await modal.click();

    page.locator('.fi-modal-heading').waitFor({ state: 'visible' });

    await page
        .getByLabel('Question*')
        .fill('How can <PERSON> be both God and man?');

    await page.getByRole('switch', { name: 'Active' }).click();

    const addButton = page.getByRole('button', { name: 'Add Question' }).nth(1);
    if (await addButton.count() > 0) {
        await addButton.click();
    } else {
        throw new Error('The "Add Question" button was not found.');
    }
    await page.waitForTimeout(1000);
});
