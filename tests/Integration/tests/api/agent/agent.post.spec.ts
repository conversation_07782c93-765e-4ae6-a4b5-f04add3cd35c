import { test } from 'fixtures/apiOptions/agentFixture';
import { PromptMessage } from '@pageObjects/API/prompt';
/**
 * This test suite is responsible for testing the POST Agent Request Service.
 * It covers different scenarios such as a successful response, invalid parameter name, and missing required property.
*/
test.describe.parallel('POST Agent Request Service', () => {
    test('should respond with completed prompt response', async ({ request, promptUrl, apiToken, vercelShareToken }) => {
        const promptMessage = new PromptMessage(request);

        await promptMessage.agentPost(promptMessage.promptPost, apiToken, `${promptUrl}/api/v1/chat/completions`, vercelShareToken);
    });

    // This test is skipped because it is not currently implemented
    test('should respond with invalid parameter name', async ({ request, promptUrl, apiToken, vercelShareToken }) => {
        const promptMessage = new PromptMessage(request);

        delete(promptMessage.promptPost.metadata as {language?: string | undefined}).language;

        const invalidData = {
            ...promptMessage.promptPost,
            metadata: { },  // Add invalid parameter
        };

        await promptMessage.agentPost(invalidData, apiToken, `${promptUrl}/api/v1/chat/completions`, vercelShareToken);
    });

    test('should respond with required property', async ({ request, promptUrl, apiToken, vercelShareToken }) => {
        const promptMessage = new PromptMessage(request);

        delete (promptMessage.promptPost as { prompt?: string }).prompt;

        await promptMessage.agentPost(promptMessage.promptPost, apiToken, `${promptUrl}/api/v1/chat/completions`, vercelShareToken);
    });
});

