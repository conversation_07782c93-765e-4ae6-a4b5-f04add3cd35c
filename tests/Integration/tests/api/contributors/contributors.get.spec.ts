import { expect } from '@playwright/test';
import { test } from 'fixtures/apiOptions/contributorsFixture';
import { ContributorsService } from '@pageObjects/API/contributors';


test.describe.serial('GET Contributors Request Service', () => {
    let contributorService: ContributorsService;

    test.beforeEach(({ request }) => {
        contributorService = new ContributorsService(request);

    });


    test('should get a list of contributors', async ({ token, data }) => {
        let { getAllcontributors, verifyCommonResponse } = contributorService;
            getAllcontributors = getAllcontributors.bind(contributorService);
            verifyCommonResponse = verifyCommonResponse.bind(contributorService);

        const contributorsResponse = await getAllcontributors(token);
        verifyCommonResponse('GET', 'Contributors', contributorsResponse.data, data);
    });

    test('should get a specific contributors', async ({ id, name, token }) => {
        const getContributorsById = contributorService.getContributorsById.bind(contributorService);
        const contributorsResponse = await getContributorsById(id, token);

        expect(contributorsResponse.data.id, `GET Contributors expected id: ${id}`).toEqual(Number(id));
        expect(contributorsResponse.data.name, `GET Contributors expected name: ${name}`).toEqual(name);
    });

    test('should return 404 resource not found', async ({ token }) => {
        let {getContributorsById, verify404Response} = contributorService;
        getContributorsById = getContributorsById.bind(contributorService);
        verify404Response = verify404Response.bind(contributorService);

        const incorrectId = -1;
        const contributorsResponse = await getContributorsById(Number(incorrectId), token, 404);
        verify404Response('GET', 'Contributors', contributorsResponse);
    });

});
