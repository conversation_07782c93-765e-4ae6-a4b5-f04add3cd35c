import { test } from 'fixtures/apiOptions/categoriesFixture';
import { CategoriesService } from '@pageObjects/API/categories';

test.describe('GET Categories Request Service', () => {
    let categoriesService: CategoriesService;

    test.beforeEach(({ request }) => {
        categoriesService = new CategoriesService(request);
    });

    test('should get a list of categories', async ({ data }) => {
        let { getAllCategories, verifyCommonResponse } = categoriesService;
        getAllCategories = categoriesService.getAllCategories.bind(categoriesService);
        verifyCommonResponse = verifyCommonResponse.bind(categoriesService);
        const categoriesResp = await getAllCategories(200);

        verifyCommonResponse('GET', 'Categories', categoriesResp.data, data);
    });

    test('should get a specific category', async ({ data }) => {
        let { getCategoryById, verifyCommonResponse } = categoriesService;
        getCategoryById = categoriesService.getCategoryById.bind(categoriesService);
        verifyCommonResponse = verifyCommonResponse.bind(categoriesService);
        const { id } = data;
        const categoriesResp = await getCategoryById(Number(id), 200);

        verifyCommonResponse('Get', 'Categories', categoriesResp, data);
    });

    test('should return 404 resource not found', async () => {
        let { getCategoryById, verify404Response } = categoriesService;
        getCategoryById = categoriesService.getCategoryById.bind(categoriesService);
        verify404Response = verify404Response.bind(categoriesService);
        const incorrectId = -1;
        const categoriesResp = await getCategoryById(Number(incorrectId), 404);

        verify404Response('GET', 'Categories', categoriesResp);
    });
});
