import { expect } from '@playwright/test';
import { test } from 'fixtures/apiOptions/collectionsFixture';
import { CollectionsService } from '@pageObjects/API/collections';


test.describe.serial('GET Collections Request Service', () => {
    let collectionService: CollectionsService;

    test.beforeEach(({ request }) => {
        collectionService = new CollectionsService(request);

    });


    test('should get a list of collections', async ({ token, data }) => {
        let { getAllCollections, verifyCommonResponse } = collectionService;
            getAllCollections = getAllCollections.bind(collectionService);
            verifyCommonResponse = verifyCommonResponse.bind(collectionService);

        const collectionsResp = await getAllCollections(token);
        verifyCommonResponse('GET', 'Collections', collectionsResp.data, data);  });

    test('should get a specific collection', async ({ id, name, token }) => {
        const getCollectionById = collectionService.getCollectionById.bind(collectionService);
        const collectionResp = await getCollectionById(id, token);

        expect(collectionResp.data.id, `GET Collections expected id: ${id}`).toEqual(Number(id));
        expect(collectionResp.data.name,  `GET Collections expected name: ${name}`).toEqual(name);
    });

    test('should return 404 resource not found', async ({ token }) => {
        const incorrectId = -1;
        let {getCollectionById, verify404Response} = collectionService;
        getCollectionById = getCollectionById.bind(collectionService);
        verify404Response = verify404Response.bind(collectionService);

        const collectionResp = await getCollectionById(Number(incorrectId), token, 404);
        verify404Response('GET', 'Collections', collectionResp);
    });

});
