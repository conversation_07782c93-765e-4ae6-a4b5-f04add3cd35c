import { expect } from '@playwright/test';
import { test } from 'fixtures/apiOptions/collectionsFixture';
import { CollectionsService } from '@pageObjects/API/collections';

test.describe.serial('POST Collections Service', () => {
    let collectionService: CollectionsService;

    test.beforeEach(({ request }) => {
        collectionService = new CollectionsService(request);
    });

    test.skip('should create a new collection', async ({ token, data }) => {
        const postCollection = collectionService.postCollection.bind(collectionService);


        const response = await postCollection(token, data, 201);

        expect(response.status()).toBe(201);
        expect(response.json()).toHaveProperty('id');
        expect(response.json()).toHaveProperty('name', 'New Collection');
    });


    test.skip('should return 401 unauthorized', async () => {
        const incorrectToken = 'incorrectToken';
        const postCollection = collectionService.postCollection.bind(collectionService);

        const data = {
            name: 'New Collection',
            'language': 'en',
            'user_id': 1,
            'team_id': 2
        };

        const response = await postCollection(incorrectToken, data, 401);

        expect(response.status()).toBe(401);
        expect(response.json()).toHaveProperty('message', 'Unauthorized');
    });
});
