import globals from 'globals';
import pluginJs from '@eslint/js';
import tseslint from 'typescript-eslint';
import eslint<PERSON>onfigPrettier from 'eslint-config-prettier';


/** @type {import('eslint').Linter.Config[]} */
export default [
    { files: ['tests/integration/**/*.{js,mjs,cjs,ts}'] },
    { languageOptions: { globals: globals.browser } },
    pluginJs.configs.recommended,
    ...tseslint.configs.recommended,
    eslintConfigPrettier,
    {ignores: [ 'playwright-report'] },
    {
        rules: {
            quotes: ['error', 'single', { 'allowTemplateLiterals': true }],
            semi: 'error',
            'prefer-const': 'error',
        },
        ignores: ['.node_modules/*']
    },
];
