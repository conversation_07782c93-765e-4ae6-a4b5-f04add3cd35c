{"name": "integration", "version": "1.0.0", "main": "index.js", "scripts": {"test": "playwright test", "staging": "STAGING=1 npx playwright test", "api:staging": "npx playwright test --workers=1 --project='API - Staging Testing' tests/api", "ui:e2e:prod": "npx playwright test --project='UI - Production Desktop E2E Chrome' tests/e2e", "ui:e2e:admin:staging": "npx playwright test --project='UI - Admin Staging Desktop E2E Chrome' tests/admin", "ui:e2e:admin:dev": "npx playwright test --project='UI - Admin Development Desktop E2E Chrome' tests/admin", "ui:e2e:admin:prod": "npx playwright test --project='UI - Admin Production Desktop E2E Chrome' tests/admin", "ui:prompt:dev": "npx playwright test --project='UI - Agent Prompt and Admin Development Testing' tests/agentUI", "ui:prompt:staging": "npx playwright test --project='UI - Agent Prompt and Admin Staging Testing' tests/agentUI", "ui:prompt:prod": "npx playwright test --ui --project='UI - Agent Prompt and Admin Production Testing' tests/agentUI", "lint": "npx eslint .", "lint:fix": "npm run lint -- --fix", "prettier": "npx prettier --check *.ts", "prettier:fix": "npm run prettier -- --write *.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.23.0", "@playwright/test": "^1.51.1", "@types/node": "^22.13.11", "eslint": "^9.23.0", "eslint-config-prettier": "10.1.1", "globals": "16.0.0", "prettier": "^3.5.3", "typescript": "^5.8.2", "typescript-eslint": "^8.28.0"}, "dependencies": {"dotenv": "^16.4.7", "fs-extra": "^11.3.0", "path": "^0.12.7", "playwright": "^1.51.1"}}