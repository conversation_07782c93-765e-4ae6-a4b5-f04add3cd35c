{
    "compilerOptions": {
        "baseUrl": "./",
        "esModuleInterop": true,
        "forceConsistentCasingInFileNames": true,
        "lib": [
            "ESNext"
        ],
        "module": "Node16",
        "moduleResolution": "node16",
        "noImplicitAny": true,
        "outDir": "./dist",
        "resolveJsonModule": true,
        "skipLibCheck": true,
        "strict": true,
        "strictNullChecks": true,
        "target": "ESNext",
        "paths": {
            "@api/*": ["api/*"],
            "@fixtures/*": ["fixtures/*"],
            "@data/*": ["data/*"],
            "@pageObjects/*": ["pageObjects/*"],
        }
    },
    "include": [
        "**/*.ts",
        "**/*.json",
        "fixtures/**/*",
        "data/**/*",
        "api/**/*",
        "fixtures/**/*",
        "scripts/**/*",
        "tests/api/**/*",
        "pageObjects/**/*",
        "tests/**/*"
    ]
}
