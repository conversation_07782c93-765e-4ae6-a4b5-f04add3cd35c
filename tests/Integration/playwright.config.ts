import { defineConfig, devices } from '@playwright/test';
import dotenv from 'dotenv';
import path from 'path';
import type { UserOptions } from '@fixtures/userOptions/userOptions';
import type { ApiOptions, StageOptions } from '@fixtures/baseOptions';

dotenv.config({ path: path.resolve(__dirname, '.env') });

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig<UserOptions, StageOptions & ApiOptions>({
    testDir: './tests',
    /* Run tests in files in parallel */
    fullyParallel: true,
    /* Fail the build on CI if you accidentally left test.only in the source code. */
    forbidOnly: !!process.env.CI,
    /* Retry on CI only */
    retries: process.env.CI ? 2 : 0,
    /* Opt out of parallel tests on CI. */
    workers: process.env.CI ? 1 : undefined,
    /* Reporting Slow tests  */
    reportSlowTests: { max: 0, threshold: 60001},
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
    reporter: 'html',
    /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
    use: {
        /* Base URL to use in actions like `await page.goto('/')`. */
        baseURL:
            process.env.STAGING === '1' ? process.env.STAGE : process.env.DEV,
        /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
        trace: 'on-first-retry',
    },

    /* Configure projects for major browsers */
    projects: [
        {
            name: 'setup',
            testMatch: /.*\.setup\.ts/,
            use: {
                ...devices['Desktop Chrome'],
                baseURL: process.env.ADMIN_PROD_BASE_URL,
                email: process.env.EMAIL,
                password: process.env.PASSWORD,
            },
        },
        {
            name: 'promptSetup',
            testMatch: /.*\.promptSetup\.ts/,
            use: {
                baseURL: process.env.ADMIN_PROD_BASE_URL,
                storageState: 'tests/setups/playwright/.auth/user.json',
            },
            dependencies: ['setup'],
        },
        {
            name: 'promptTeardown',
            testMatch: /.*\.promptTeardown\.ts/,
            use: {
                baseURL: process.env.ADMIN_PROD_BASE_URL,
                storageState: 'tests/setups/playwright/.auth/user.json',
            },
        },
        {
            name: 'API - Production Testing',
            use: {
                ...devices['Desktop Chrome'],
                baseURL: process.env.API_PROD,
                token: process.env.PROD_TOKEN,
                apiToken: process.env.AGENT_PROD_API_KEY,
                promptUrl: process.env.AGENT_PROD_PROMPT_URL,
            },
            retries: 2,
        },
        {
            name: 'API - Staging Testing',
            use: {
                ...devices['Desktop Chrome'],
                baseURL: process.env.API_STAGE,
                token: process.env.STAGE_TOKEN,
                vercelShareToken: process.env.STAGE_VERCEL_SHARE,
                apiToken: process.env.AGENT_STAGE_API_KEY,
                promptUrl: process.env.AGENT_STAGING_PROMPT_URL,
            },
            retries: 2,
            fullyParallel: true,
        },
        {
            name: 'API - Development Testing',
            use: {
                ...devices['Desktop Chrome'],
                baseURL: process.env.API_DEV,
                token: process.env.DEV_TOKEN,
                apiToken: process.env.AGENT_PROD_API_KEY,
                promptUrl: process.env.AGENT_PROD_PROMPT_URL,
            },
            retries: 2,
        },
        {
            name: 'UI - Production Desktop E2E Chrome',
            use: {
                baseURL: process.env.PROD_BASE_URL,
                storageState: 'tests/setups/playwright/.auth/user.json',
            },
            dependencies: ['setup'],
            retries: 2,
        },
        {
            name: 'UI - Admin Production UI Desktop E2E Chrome',
            use: {
                ...devices['Desktop Chrome'],
                baseURL: process.env.ADMIN_PROD_BASE_URL,
                storageState: 'tests/setups/playwright/.auth/user.json',
            },
            dependencies: ['setup'],
            retries: 2,
        },
        {
            name: 'UI - Agent Prompt and Admin Production Testing',
            use:{
                ...devices['Desktop Chrome'],
                baseURL: process.env.ADMIN_PROD_BASE_URL,
                storageState: 'tests/setups/playwright/.auth/user.json',
            },
            dependencies: ['setup', 'promptSetup'],
            teardown: 'promptTeardown',
            retries: 2,
        },
        {
            name: 'UI - Admin Production Desktop E2E Chrome',
            use: {
                baseURL: process.env.ADMIN_PROD_BASE_URL,
                storageState: 'tests/setups/playwright/.auth/user.json',
            },
            dependencies: ['setup'],
            retries: 2,
        },
        {
            name: 'UI - Admin Staging Desktop E2E Chrome',
            use: {
                baseURL: process.env.STAGE_BASE_URL,
                storageState: 'tests/setups/playwright/.auth/user.json',
            },
            dependencies: ['setup'],
            retries: 2,
        },
        {
            name: 'UI - Admin Development Desktop E2E Chrome',
            use: {
                ...devices['Desktop Chrome'],
                baseURL: process.env.DEV_BASE_URL,
                storageState: 'tests/setups/playwright/.auth/user.json',
            },
            dependencies: ['setup'],
            retries: 2,
        },
    ],

    /* Run your local dev server before starting the tests */
    // webServer: {
    //   command: 'npm run start',
    //   url: 'http://127.0.0.1:3000',
    //   reuseExistingServer: !process.env.CI,
    // },
});
