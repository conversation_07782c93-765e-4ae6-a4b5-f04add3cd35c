import { test, expect, APIRequestContext } from '@playwright/test';
import promptData from '@data/agentRequest.json';
import promptResponse from '@data/agentResponse.json';

interface PromptResponse {
    success: boolean;
    errors?: string[];
    completion?: string;
    status: number;
    choices?: Array<{
        message: {
            content: string;
        };
    }>;
}

interface ApiResponse {
    status: number;
    body: PromptResponse;
}

export class PromptMessage {
    readonly promptRequestContext: APIRequestContext;
    readonly promptPost: typeof promptData;
    readonly promptResponse: typeof promptResponse;
    readonly errorInvalidParams: { status: number; body: { errors: string[] } };

    constructor(private request: APIRequestContext) {
        this.promptRequestContext = request;
        this.promptPost = promptData;
        this.promptResponse = promptResponse;
        this.errorInvalidParams = {
            status: 200,
            body: { errors: [
                    '\'lang\' is not a valid parameter.',
                    'Input must have required property \'prompt\'.',
                    'Input must have required property \'messages\'.'
                    ],
            },
        };
    }

    /**
     * This function sends a prompt request to an API endpoint and validates the response.
     *
     * @param data - The data to be sent in the request body.
     * @param apiKey - The API key to be included in the request headers.
     * @param url - The URL of the API endpoint to send the request to.
     * @param status - The expected status code of the response. Defaults to 200.
     *
     * @returns A Promise that resolves to an object containing the status code and response body.
     *
     * @remarks
     * The function performs the following steps:
     * 1. Sends a POST request to the specified URL with the provided data and API key.
     * 2. Validates the status code of the response against the expected status.
     * 3. If the response indicates an error (success: false), validates the response body against the expected error messages.
     * 4. Validates the response body against the expected completion message.
     */

    public async agentPost (data: object, apiKey: string, url: string, vercelShare?: string, status: number = 200) {
        let statusResponse : number;
        let callResponse : PromptResponse = { success: false, status: 0 };
        const params: Record<string, string> = {};

        if (vercelShare) {
            params['_vercel_share'] = vercelShare;
        }

        await test.step('I can send a prompt request', async () => {
            const response = await this.promptRequestContext.post( url, {
                headers: { 'x-api-key': apiKey},
                data: data,
                failOnStatusCode: false,
                params: params,
            });

            statusResponse = response.status();

            if ([403, 405].includes(statusResponse)) {
                throw new Error(`Expected error message for status ${await response.text()}. One possibility is invalid apiKey or no apiKey.`);
            }

            callResponse = await response.json() as PromptResponse;


            return { status: statusResponse, body: callResponse } as ApiResponse;
        });



        await test.step(`The status is ${status}`,  async () => {
            expect(statusResponse, `Expected ${status}`).toBe(status);
        });


        if (callResponse.success === false && callResponse.errors) {
            const error = this.errorInvalidParams.body.errors.filter(
                error => callResponse.errors?.includes(error)
            );
            if (error.length > 0) {
                await test.step('The response body contains the expected error message', async () => {
                    expect(callResponse.errors, `Expected error message: ${error}`).toEqual(error);
                });
                return;
            }
        }


        await test.step(`The response body contains the expect response message`, async () => {
            const expectedKeywords = [
                /Jesus.*God.*man/,
                /fully God.*fully man/,
                /mystery.*faith/,
                /incarnation/,
                /hypostatic union/,
                /eternal Word/,
                /salvation/,
                /mediator/,
            ];

            const responseBody = callResponse.choices?.[0]?.message?.content || '';

            const containsExpectedKeyword = expectedKeywords.some(pattern => pattern.test(responseBody));
            expect(containsExpectedKeyword, 'Expected at least one keyword or pattern to be matched in the response').toBeTruthy();
        });

    }
}
