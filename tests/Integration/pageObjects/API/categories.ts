import { test, expect, APIRequestContext } from '@playwright/test';
import { BaseService } from '../baseService';
export class CategoriesService extends BaseService {

    readonly categoriesContext: APIRequestContext;

    constructor(private request: APIRequestContext) {
        super();
        this.categoriesContext = request;
    }

    /**
      * Retrieves all categories from the API.
      *
      * @param status - The expected HTTP status code of the response. Defaults to 200.
      * @returns A Promise that resolves to the JSON response of the API request.
      *
      * @example
      * ```typescript
      * const categoriesService = new CategoriesService(request);
      * const categories = await categoriesService.getAllCategories(status = 200);
      * console.log(categories);
      * ```
    */
    public async getAllCategories(status: number = 200) {

        const response = await this.categoriesContext.get('v1/categories/', { failOnStatusCode: false });

        await test.step(`The status is ${status}`,  async () =>{
            expect(response.status(), `Expected ${status}`).toBe(status);
        });

        return await response.json();
    }

    /**
     * Retrieves a category by its ID from the API.
     *
     * @param categoryId - The unique identifier of the category to retrieve.
     * @param status - The expected HTTP status code of the response. Defaults to 200.
     * @returns A Promise that resolves to the JSON response of the API request.
     *
     * @example
     * ```typescript
     * const categoriesService = new CategoriesService(request);
     * const category = await categoriesService.getCategoryById(categoryId = 123, status = 200);
     * console.log(category);
     * ```
     */
    public async getCategoryById(categoryId: number, status: number = 200) {
        const response = await this.categoriesContext.get(`/v1/categories/${categoryId}`, { failOnStatusCode: false } );

        await test.step(`The status is ${status}`,  async () =>{
            expect(response.status(), `Expected ${status}`).toBe(status);
        });

        return await response.json();
    }
}
