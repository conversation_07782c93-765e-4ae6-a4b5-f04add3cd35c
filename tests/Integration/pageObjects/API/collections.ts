import { test, expect, APIRequestContext } from '@playwright/test';
import { BaseService } from '../baseService';

export class CollectionsService extends BaseService {

    readonly collectionsContext: APIRequestContext;

    constructor(private request: APIRequestContext) {
        super();
        this.collectionsContext = request;
    }

    /**
      * Retrieves all collections from the API.
      *
      * @param status - The expected HTTP status code of the response. Defaults to 200.
      * @returns A Promise that resolves to the JSON response of the API request.
      *
      * @example
      * ```typescript
      * const collectionsService = new collectionsService(request);
      * const collections = await collectionsService.getAllCollections(status = 200);
      * console.log(collections);
      * ```
    */
    public async getAllCollections(token: string,  status: number = 200) {

        const response = await this.collectionsContext.get('v1/collections/',
            {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                failOnStatusCode: false,
            }
        );

        await test.step(`The status is ${status}`,  async () =>{
            expect(response.status(), `Expected ${status}`).toBe(status);
        });

        return await response.json();
    }

    /**
     * Retrieves a category by its ID from the API.
     *
     * @param categoryId - The unique identifier of the category to retrieve.
     * @param status - The expected HTTP status code of the response. Defaults to 200.
     * @returns A Promise that resolves to the JSON response of the API request.
     *
     * @example
     * ```typescript
     * const collectionsService = new collectionsService(request);
     * const collectionsResponse = await collectionsService.getCategoryById(categoryId = 123, status = 200);
     * console.log(collectionsResponse);
     * ```
     */
    public async getCollectionById(collectionId: number, token: string,  status: number = 200) {
        const response = await this.collectionsContext.get(`/v1/collections/${collectionId}`,
            {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                failOnStatusCode: false,
            }
        );

        await test.step(`The status is ${status}`,  async () =>{
            expect(response.status(), `Expected ${status}`).toBe(status);
        });

        return await response.json();
    }

    /**
     * Posts a new collection to the API.
     *
     * @param token - The authentication token for the API request.
     * @param data - The data to be sent in the request body. It should be an object representing the collection to be created.
     * @param status - The expected HTTP status code of the response. Defaults to 200.
     *
     * @returns A Promise that resolves to the JSON response of the API request.
     *
     * @example
     * ```typescript
     * const collectionsService = new CollectionsService(request);
     * const newCollectionData = { name: 'New Collection', description: 'This is a new collection.' };
     * const createdCollection = await collectionsService.postCollection(token, newCollectionData, 201);
     * console.log(createdCollection);
     * ```
     */

    public async postCollection(token: string, data: object, status: number = 200) {
        const response = await this.collectionsContext.post('/v1/collections/', {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
            },
            data,
            failOnStatusCode: false,
        });

        await test.step(`The status is ${status}`, async () => {
            expect(response.status(), `Expected ${status}`).toBe(status);
        });

        return await response.json();
    }

}
