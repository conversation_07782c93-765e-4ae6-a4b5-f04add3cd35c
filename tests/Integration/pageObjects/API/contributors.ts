import { test, expect, APIRequestContext } from '@playwright/test';
import { BaseService } from '../baseService';


export class ContributorsService extends BaseService {

    readonly contributorsContext: APIRequestContext;

    constructor(private request: APIRequestContext) {
        super();
        this.contributorsContext = request;
    }

    /**
      * Retrieves all contributors from the API.
      *
      * @param status - The expected HTTP status code of the response. Defaults to 200.
      * @returns A Promise that resolves to the JSON response of the API request.
      *
      * @example
      * ```typescript
      * const contributorsService = new contributorsService(request);
      * const contributors = await contributorsService.getAllcontributors(status = 200);
      * console.log(contributors);
      * ```
    */
    public async getAllcontributors(token: string,  status: number = 200) {

        const response = await this.contributorsContext.get('v1/contributors/',
            {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                params: {
                    'sort': 'id'
                },
                failOnStatusCode: false,
            }
        );

        await test.step(`The status is ${status}`,  async () =>{
            expect(response.status(), `Expected ${status}`).toBe(status);
        });

        return await response.json();
    }

    /**
     * Retrieves a category by its ID from the API.
     *
     * @param categoryId - The unique identifier of the category to retrieve.
     * @param status - The expected HTTP status code of the response. Defaults to 200.
     * @returns A Promise that resolves to the JSON response of the API request.
     *
     * @example
     * ```typescript
     * const contributorsService = new contributorsService(request);
     * const contributorsResponse = await contributorsService.getCategoryById(categoryId = 123, status = 200);
     * console.log(contributorsResponse);
     * ```
     */
    public async getContributorsById(contributorsId: number, token: string,  status: number = 200) {
        const response = await this.contributorsContext.get(`/v1/contributors/${contributorsId}`,
            {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                failOnStatusCode: false,
            }
        );

        await test.step(`The status is ${status}`,  async () =>{
            expect(response.status(), `Expected ${status}`).toBe(status);
        });

        return await response.json();
    }

    /**
     * Posts a new Contributors to the API.
     *
     * @param token - The authentication token for the API request.
     * @param data - The data to be sent in the request body. It should be an object representing the Contributors to be created.
     * @param status - The expected HTTP status code of the response. Defaults to 200.
     *
     * @returns A Promise that resolves to the JSON response of the API request.
     *
     * @example
     * ```typescript
     * const contributorsService = new contributorsService(request);
     * const newContributorsData = { name: 'New Contributors', description: 'This is a new Contributors.' };
     * const createdContributors = await contributorsService.postContributors(token, newContributorsData, 201);
     * console.log(createdContributors);
     * ```
     */

    public async postContributor(token: string, data: object, status: number = 200) {
        const response = await this.contributorsContext.post('/v1/contributors/', {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
            },
            data,
            failOnStatusCode: false,
        });

        await test.step(`The status is ${status}`, async () => {
            expect(response.status(), `Expected ${status}`).toBe(status);
        });

        return await response.json();
    }

    /**
     * Verifies that the actual response matches the expected 404 (Not Found) response.
     *
     * @param httpResponse - A string representing the HTTP response being verified.
     * @param expectedResponse - The expected response that the actual response should match.
     *
     * @returns {Promise<void>} A Promise that resolves when the verification step is completed.
     *
     * @example
     * ```typescript
     * const contributorsService = new contributorsService(request);
     * const expectedResponse = { error: 'Not Found' };
     * await contributorsService.verify404Response('GET', expectedResponse);
     * ```
     */
}
