import { test, expect } from '@playwright/test';

interface DataObject {
    id: number | undefined | null;
    name?: string | undefined | null;
    description?: string | undefined | null;
}

export class BaseService {
    readonly errorMessage: { status: number; message: Array<string> };

    constructor() {
        this.errorMessage = {
            status: 404,
            message: ['resource not found'],
        };
    }

    /**
     * Verifies that the actual response matches the expected response.
     *
     * @param httpResponse - A string representing the HTTP response being verified.
     * @param serviceName  - The name of the service
     * @param actualResponse - The actual response received from the API.
     * @param expectedData - The expected data that the actual response should match.
     * @returns {Promise<void>} A Promise that resolves when the verification step is completed.
     *
     * @example
     * ```typescript
     * const collectionsService = new CollectionsService(request);
     * const actualCollections = await collectionsService.getAllCollections(token, 200);
     * const expectedCollections = { id: 1, name: 'Sample Collection', description: 'This is a sample collection.' };
     * await collectionsService.verifyCommonResponse('GET', 'CollectionsService', actualCollections, expectedCollections);
     * ```
     */
    public async verifyCommonResponse(
        httpResponse: string,
        serviceName: string,
        actualResponse: object,
        expectedData: DataObject,
    ): Promise<void> {
        await test.step(`${httpResponse} ${serviceName} List should match Json`, async () => {
            const actualResponseArray = Object.values(
                actualResponse,
            ) as DataObject[];
            const foundMatch = actualResponseArray.some(
                (item) =>
                    item.id === expectedData.id &&
                    item.name === expectedData.name &&
                    item.description === expectedData.description,
            );
            expect(foundMatch,`${httpResponse} ${serviceName}: ${expectedData.name} is found ${foundMatch}`).toBe(true);
            expect(expectedData.id, `${httpResponse} ${serviceName} expected id: ${expectedData.id}`).toBe(actualResponseArray[0].id);
            expect(expectedData.name, `${httpResponse} ${serviceName} expected name: ${expectedData.name}`).toBe(actualResponseArray[0].name);
            expect(expectedData.description, `${httpResponse} ${serviceName} expected description: ${expectedData.description}`).toBe(expectedData.description);
        });
    }

    /**
     * Verifies that the actual response matches the expected 404 (Not Found) response.
     *
     * @param httpResponse - A string representing the HTTP response being verified.
     * @param serviceName - The name of the service
     * @param expectedResponse - The expected response that the actual response should match.
     * @returns {Promise<void>} A Promise that resolves when the verification step is completed.
     *
     * @example
     * ```typescript
     * const collectionsService = new CollectionsService(request);
     * const expectedResponse = { message: 'resource not found' };
     * await collectionsService.verify404Response('GET', 'CollectionsService', expectedResponse);
     * ```
     */
    public async verify404Response(
        httpResponse: string,
        serviceName: string,
        expectedResponse: { message: string },
    ): Promise<void> {
        await test.step(`${httpResponse} ${serviceName}`, async () => {
            const error = this.errorMessage.message.filter((error) => expectedResponse.message.includes(error) );
            expect(error.length >= 0,`${httpResponse} ${serviceName} expected message: ${expectedResponse.message}`).toEqual(true);
            expect(this.errorMessage.status, `${httpResponse} ${serviceName} expected status code: ${this.errorMessage.status}`).toBe(404);
        });
    }
}
