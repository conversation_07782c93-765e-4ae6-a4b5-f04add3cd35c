import { test as base } from '@playwright/test';
import agentResponse from '@data/agentResponse.json';
import agentRequest from '@data/agentRequest.json';
import { ApiOptions } from '@fixtures/baseOptions';


export type Timings = {
    start: string;
    end: string;
    elapsed: number;
};

export type Usage = {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    completion_tokens_details:{
        reasoning_tokens: number;
        accepted_prediction_tokens: number;
        rejected_prediction_tokens: number;
    }
};

export type Data = {
    model: string;
    stream: boolean;
    prompt: string;
    response_format: {
        type: string;
    };
    usage: Usage;
    metadata: {
        anonymous: boolean;
        conversation: null;
        language: string;
        session: null;
        translation: string;
    };
    frequency_penalty: number;
    presence_penalty: number;
    max_completion_tokens: number;
    reasoning_effort: string;
    temperature: number;
    top_p: number;
    user: null;

};

export type AgentAPIRequest = {
    model: string;
    stream: boolean;
    prompt: string;
    response_format: {
        type: string;
    };
    metadata: {
        anonymous: boolean;
        conversation: null;
        language: string;
        session: null;
        translation: string;
    };
    frequency_penalty: number;
    presence_penalty: number;
    max_completion_tokens: number;
    reasoning_effort: string;
    temperature: number;
    top_p: number;
    user: null;
    additional_field: string;
    timeout: number;
    retry_attempts: number;
    usage: Usage;
}
interface MessageContent {
    content: string;
    role: string;
}

interface Choice {
    finish_reason: string;
    index: number;
    logprobs: null;
    message: MessageContent;
}

interface Metadata {
    anonymous: boolean;
    conversation: null;
    language: string;
    translation: string;
    // ... add other metadata properties as needed
}

interface PromptResponse {
    success: boolean;
    errors?: string[];
    choices: Choice[];
    created: number;
    id: string;
    metadata: Metadata;
    user: null;
}

export interface AgentAPIResponse {
    status: number;
    body: PromptResponse;
}

export const test = base.extend<ApiOptions>({
    apiToken: ['', { option: true, scope: 'test' }],
    vercelShareToken: ['', { option: true, scope: 'test' }],
    promptUrl: ['', { option: true, scope: 'test' }]
});

// Add agent-specific fixtures in a separate extension
export const agentTest = test.extend<{
    agentRequest: AgentAPIRequest;
    agentResponse: AgentAPIResponse;
}>({
    agentRequest: [agentRequest as AgentAPIRequest, { option: true }],
    agentResponse: [{
        status: 200,
        body: {
            success: true,
            ...agentResponse
        }
    } as AgentAPIResponse, { option: true }]
});
