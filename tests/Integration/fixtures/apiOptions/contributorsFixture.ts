import { test as base } from '@playwright/test';
import contributorsList from '@data/contributors.json';
import { CategoriesType } from './categoriesFixture';
import { StageOptions } from '@fixtures/baseOptions';


// Type definitions
export type ContributorsList = {
    data: ContributorsType;
};

export type ContributorsType = {
    id: number;
    name: string;
    title?: string | null;
    first_name?: string;
    last_name?: string;
    suffix?: string | null;
    bio?: string | null;
    born_in?: number | null;
    died_in?: number | null;
    referral_url?: string | null | undefined;
    is_controversial?: boolean | null;
    is_approved?: boolean | null ;
    social_active?: number | null;
    rating?: number | null;
    featured_seq?: number | null ;
    user_id?: number | undefined;
    team_id?: number | undefined;
    slug?: string | undefined;
    is_locked?: boolean | null;
    merged_contributor_id?: number | undefined | null;
    created_at?: string;
    updated_at?: string;
    deleted_at?: string | null;
    categories?: CategoriesType[] | null;
    avatar_url?: string;
};

const contributorsData = contributorsList.data[0];

export const test = base.extend<ContributorsType, StageOptions & ContributorsList>({
    // ContributorsType properties with default values
    data: [contributorsData as ContributorsType, { option: true, scope: 'worker' }],
    id: [contributorsData.id, { option: true }],
    name: [contributorsData.name, { option: true }],
    title: [contributorsData.title, { option: true }],
    first_name: [contributorsData.first_name, { option: true }],
    last_name: [contributorsData.last_name, { option: true }],
    suffix: [contributorsData.suffix, { option: true }],
    bio: [contributorsData.bio, { option: true }],
    born_in: [contributorsData.born_in, { option: true }],
    died_in: [contributorsData.died_in, { option: true }],
    referral_url: [contributorsData.referral_url, { option: true }],
    // StageOptions property
    token: ['', { option: true, scope: 'worker' }],
});
