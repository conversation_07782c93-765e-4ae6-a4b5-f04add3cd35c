import { test as base } from '@playwright/test';
import collectionsList from '@data/collections.json';
import { CategoriesType } from './categoriesFixture';
import { StageOptions } from '@fixtures/baseOptions';


export type CollectionsList = {
    data: CollectionType;
};

export type CollectionType = {
    id: number;
    name: string;
    type?: string;
    parent_id?: number | null;
    url?: string | null | undefined;
    external_id?: number | null;
    classification_id?: number | null;
    referral_url?: string | null;
    is_approved: boolean | null;
    social_active: number;
    description?: string | null;
    language?: string;
    auto_import_sources?: boolean | null;
    auto_categorize_sources?: string | null;
    auto_index_sources?: number | null;
    auto_list_sources?: boolean | string | null;
    default_source_weight?: number | null;
    source_image_handling?: string | null;
    enforce_source_excerpts?: number | null;
    enforce_source_categories?: string | null;
    rating?: number | null;
    featured_seq: number | null;
    user_id: number | null;
    team_id: number | null;
    slug: string | null;
    created_at: string;
    updated_at?: string;
    categories?: CategoriesType[] | null;
    image_url?: string | null;
};


export const test = base.extend<CollectionType, StageOptions & CollectionsList>({
    data: [collectionsList.data as CollectionType, { option: true, scope: 'worker'}],
    id: [collectionsList.data.id, { option: true }],
    name: [collectionsList.data.name, { option: true }],
    description: [collectionsList.data.description, { option: true }],
    parent_id: [collectionsList.data.parent_id, { option: true }],
    url: [collectionsList.data.url, { option: true }],
    external_id: [collectionsList.data.external_id, { option: true }],
    classification_id: [collectionsList.data.classification_id, { option: true }],
    referral_url: [collectionsList.data.referral_url, { option: true }],
    is_approved: [collectionsList.data.is_approved, { option: true }],
    social_active: [collectionsList.data.social_active, { option: true }],
    language: [collectionsList.data.language, { option: true }],
    auto_import_sources: [collectionsList.data.auto_import_sources, { option: true }],
    auto_categorize_sources: [collectionsList.data.auto_categorize_sources, { option: true }],
    auto_index_sources: [collectionsList.data.auto_index_sources, { option: true }],
    auto_list_sources: [collectionsList.data.auto_list_sources, { option: true }],
    default_source_weight: [collectionsList.data.default_source_weight, { option: true }],
    source_image_handling: [collectionsList.data.source_image_handling, { option: true }],
    enforce_source_excerpts: [collectionsList.data.enforce_source_excerpts, { option: true }],
    enforce_source_categories: [collectionsList.data.enforce_source_categories, { option: true }],
    rating: [collectionsList.data.rating, { option: true }],
    featured_seq: [collectionsList.data.featured_seq, { option: true }],
    user_id: [collectionsList.data.user_id, { option: true }],
    team_id: [collectionsList.data.team_id, { option: true }],
    slug: [collectionsList.data.slug, { option: true }],
    created_at: [collectionsList.data.created_at, { option: true }],
    updated_at: [collectionsList.data.updated_at, { option: true }],
    image_url: [collectionsList.data.image_url, { option: true }],
    token: ['', { option: true, scope: 'worker' }],
});

