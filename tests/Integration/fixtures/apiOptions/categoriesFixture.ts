import { test as base } from '@playwright/test';
import categories from '@data/categories.json';

export type CategoriesType = {
  id: number;
  name?: string | null;
  description?: string | null;
  parent_id?: number | null;
  slug?: string | null;
  tagline?: string | null;
  is_active?: boolean | null;
  created_at?: string | null;
  updated_at?: string | null;
  deleted_at?: string | null;
};

type CustomFixtures = {
  data: CategoriesType;
};

const categoriesList = categories.data[0] as CategoriesType;

export const test = base.extend<CustomFixtures>({
    data: [{ ...categoriesList }, { option: true, scope: 'test' }]
});
