### APP ###
APP_NAME="Apologist Ignite"
APP_ENV=development
APP_KEY=base64:CHANGEMEPLEASE
APP_DEBUG=true
APP_URL=https://api.apologist.test

APP_TIMEZONE=UTC
APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US
APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database
BCRYPT_ROUNDS=12

### SUBDOMAINS ###
APP_BASE_DOMAIN=apologist.test
APP_SUBDOMAIN_APP=ignite
APP_SUBDOMAIN_IDENTITY=id
APP_SUBDOMAIN_API=api
APP_SUBDOMAIN_ADMIN=admin
APP_SUBDOMAIN_AGENT=agent
APP_PORT_AGENT=3000
APP_API_VERSION=v1

### IDENTIFIERS ###
ADMIN_USER_DOMAIN=apologistproject.org
ADMIN_EMAIL=<EMAIL>
SUPERADMIN_ROLE_ID=1
ADMIN_ROLE_ID=2
MEMBER_ROLE_ID=3

### PRODUCTS ###
PRODUCTS_URL=https://apologistproject.org/products
SUPPORT_URL="https://apologistproject.org/contact#contact"
AGENT_URL=http://localhost:3000
AGENT_DEMO_PATH="/en?corpus="
AGENT_LEARN_URL=https://apologistproject.org/products/apologist-agent
SOCIAL_URL=https://apologist.com
SOCIAL_LEARN_URL=https://apologistproject.org/products/apologist-social
BEACON_LANGUAGES=en,es,fr,de,it,ja,nl,pt,ru,zh
BEACON_TRANSLATIONS=esv,niv,bsb,kjv,net,nkjv,nlt,csb,nasb

### KNOWLEDGE BASE ###
KNOWLEDGE_BASE_URL=https://apologistproject.org/documentation/apologist-ignite/apologist-ignite-overview
KNOWLEDGE_BASE_DASHBOARD_URL=https://apologistproject.org/documentation/apologist-ignite/dashboard
KNOWLEDGE_BASE_CONTRIBUTORS_URL=https://apologistproject.org/documentation/apologist-ignite/contributors
KNOWLEDGE_BASE_SOURCES_URL=https://apologistproject.org/documentation/apologist-ignite/sources
KNOWLEDGE_BASE_COLLECTIONS_URL=https://apologistproject.org/documentation/apologist-ignite/collections

### DRIVERS ###
BROADCAST_CONNECTION=log
CACHE_STORE=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

### LOGS ###
LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

### Google ###
GOOGLE_CLOUD_KEY_FILE=/Users/<USER>/bubbly-hope-388816-eaa97483aa0a.json
GOOGLE_CLOUD_PROJECT_ID=some-project
GOOGLE_CLOUD_STORAGE_BUCKET=apg-files
GOOGLE_CLOUD_STORAGE_API_ENDPOINT=https://storage.googleapis.com
YOUTUBE_API_KEY=some-key
YOUTUBE_VIDEO_URL=https://www.youtube.com/watch?v={videoId}

### TRANSCRIPTION ###
TRANSCRIPTION_PROVIDER=AssemblyAI
SPEECHFLOW_API_KEY=some-key
SPEECHFLOW_API_SECRET=some-key
HAPPYSCRIBE_ORGANIZATION_ID=some-id
HAPPYSCRIBE_API_KEY=some-key
ASSEMBLYAI_API_KEY=some-ky

### DATABASE ###
DB_CONNECTION=app
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=apologist
DB_USERNAME=root
DB_PASSWORD=
DB_SSLMODE=

### AGENT DATABASE ###
AGENT_DB_CONNECTION=agent
AGENT_DB_HOST=localhost
AGENT_DB_PORT=5432
AGENT_DB_DATABASE=apg_agent
AGENT_DB_USERNAME=postgres
AGENT_DB_PASSWORD=
AGENT_DB_SSLMODE=disable

### CACHE ###
MEMCACHED_HOST=127.0.0.1
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_CLIENT=predis

### MAIL ###
MAIL_MAILER=smtp
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

### VITE ###
VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

### CASHIER ###
CASHIER_CURRENCY=USD
CASHIER_CURRENCY_LOCALE=en
PADDLE_SANDBOX=true
PADDLE_SELLER_ID=some-id
PADDLE_AUTH_CODE=some-code
PADDLE_WEBHOOK_SECRET=some-secret
SPARK_BASIC_MONTHLY_PLAN=some-plan
SPARK_BASIC_YEARLY_PLAN=some-plan
SPARK_PLUS_MONTHLY_PLAN=some-plan
SPARK_PLUS_YEARLY_PLAN=some-plan
SPARK_PREMIUM_MONTHLY_PLAN=some-plan
SPARK_PREMIUM_YEARLY_PLAN=some-plan

### LOGIN PROVIDERS ###
GOOGLE_CLIENT_ID=some-id
GOOGLE_CLIENT_SECRET=some-secret
GOOGLE_REDIRECT_URI=some-uri
APPLE_CLIENT_ID=some-client
APPLE_CLIENT_SECRET=some-secret
APPLE_REDIRECT_URI=some-uri
MICROSOFT_CLIENT_ID=some-id
MICROSOFT_CLIENT_SECRET=some-secret
#MICROSOFT_CLIENT_SECRET_ID=some-id
MICROSOFT_REDIRECT_URI=some-uri
FACEBOOK_CLIENT_ID=some-id
FACEBOOK_CLIENT_SECRET=some-secret
FACEBOOK_REDIRECT_URI=some-uri
TWITTER_CLIENT_ID=some-uri
TWITTER_CLIENT_SECRET=some-secret
TWITTER_REDIRECT_URI=some-uri
LINKEDIN_CLIENT_ID=some-id
LINKEDIN_CLIENT_SECRET=some-secret
LINKEDIN_REDIRECT_URI=some-uri

### RAG VENDOR ###
RAG_API_KEY=some-key
RAG_CUSTOMER_ID=some-id
RAG_CORPUS_ID=some-id

### PUSHER ###
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

OPENAI_API_KEY=
OPENAI_ORGANIZATION=
