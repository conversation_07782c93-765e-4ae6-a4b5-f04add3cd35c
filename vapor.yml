id: 69048
name: apg-ignite
ignore:
    - storage
environments:
    production:
        runtime: 'php-8.3:al2-arm'
        gateway-version: 2
        separate-vendor: true
        scheduler: true
        domain:
            - ignite.apologist.com
            - id.apologist.com
            - api.apologist.com
            - admin.apologist.com
        database: apg-ignite-production
        cache: apg-ignite
        storage: apg-ignite-production
        tmp-storage: 1024
        timeout: 300
        memory: 2048
        cli-timeout: 300
        cli-memory: 2048
        cli-tmp-storage: 1024
        queues:
            - apg-ignite-default-production: 325
            - apg-ignite-high-production: 500
            - apg-ignite-low-production: 150
            - apg-ignite-throttled-production: 25
        queue-timeout: 300
        queue-memory: 2048
        queue-tmp-storage: 1024
        build:
            - 'php artisan clockwork:clean --all'
            - 'rm -rf ../../../storage/framework/cache/data/*'
            - 'rm -rf ../../../storage/framework/views/*'
            - 'rm -rf ../../../storage/logs/*'
            - 'COMPOSER_MIRROR_PATH_REPOS=1 composer install --no-dev'
            - 'php artisan event:cache'
            - 'npm ci && npm run build && rm -rf node_modules'
        deploy:
            - 'php artisan migrate --force'
    staging:
        runtime: 'php-8.3:al2-arm'
        gateway-version: 2
        separate-vendor: true
        scheduler: true
        domain:
            - ignite.apologist.build
            - id.apologist.build
            - api.apologist.build
            - admin.apologist.build
        database: apg-ignite-staging
        cache: apg-ignite
        storage: apg-ignite-staging
        tmp-storage: 1024
        timeout: 300
        memory: 2048
        cli-timeout: 300
        cli-memory: 2048
        cli-tmp-storage: 1024
        queues:
            - apg-ignite-default-staging: 325
            - apg-ignite-high-staging: 500
            - apg-ignite-low-staging: 150
            - apg-ignite-throttled-staging: 25
        queue-timeout: 300
        queue-memory: 2048
        queue-tmp-storage: 1024
        build:
            - 'php artisan clockwork:clean --all'
            - 'rm -rf ../../../storage/framework/cache/data/*'
            - 'rm -rf ../../../storage/framework/views/*'
            - 'rm -rf ../../../storage/logs/*'
            - 'COMPOSER_MIRROR_PATH_REPOS=1 composer install --no-dev'
            - 'php artisan event:cache'
            - 'npm ci && npm run build && rm -rf node_modules'
        deploy:
            - 'php artisan migrate --force'
    development:
        runtime: 'php-8.3:al2-arm'
        gateway-version: 2
        separate-vendor: true
        scheduler: true
        domain:
            - ignite.apologist.dev
            - id.apologist.dev
            - api.apologist.dev
            - admin.apologist.dev
        database: apg-ignite-development
        cache: apg-ignite
        storage: apg-ignite-development
        tmp-storage: 1024
        timeout: 300
        memory: 2048
        cli-timeout: 300
        cli-memory: 2048
        cli-tmp-storage: 1024
        queues:
            - apg-ignite-default-development: 325
            - apg-ignite-high-development: 500
            - apg-ignite-low-development: 150
            - apg-ignite-throttled-development: 25
        queue-timeout: 300
        queue-memory: 2048
        queue-tmp-storage: 1024
        build:
            - 'php artisan clockwork:clean --all'
            - 'rm -rf ../../../storage/framework/cache/data/*'
            - 'rm -rf ../../../storage/framework/views/*'
            - 'rm -rf ../../../storage/logs/*'
            - 'COMPOSER_MIRROR_PATH_REPOS=1 composer install --no-dev'
            - 'php artisan event:cache'
            - 'npm ci && npm run build && rm -rf node_modules'
        deploy:
          - 'php artisan migrate --force'
