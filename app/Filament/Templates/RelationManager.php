<?php

namespace App\Filament\Templates;

use Filament\Actions\StaticAction;
use Filament\Resources\RelationManagers\RelationManager as BaseRelationManager;
use Filament\Tables;
use Filament\Tables\Actions\CreateAction;
use Illuminate\Support\Str;

class RelationManager extends BaseRelationManager
{
    protected static string $createLabel = 'add';

    protected static string $createLabelPast = 'added';

    protected static function getCreateLabel(): string
    {
        return static::$createLabel;
    }

    protected static function getCreateLabelPast(): string
    {
        return static::$createLabelPast;
    }

    /**
     * @return CreateAction
     */
    protected static function getTableCreateAction(?string $label = null): Tables\Actions\CreateAction
    {
        $createAction = Tables\Actions\CreateAction::make('create')->extraAttributes(['id' => btn_id('create')]);
        $createLabelFn = Str::apa(static::getCreateLabel()).' '.$label ?? Str::apa($createAction->getModelLabel());
        $createLabelPastFn = fn () => Str::apa($label ?? $createAction->getModelLabel().' '.static::getCreateLabelPast());

        return $createAction->label($createLabelFn)
            ->extraAttributes(['id' => btn_id(Str::slug($createLabelFn))])
            ->modalHeading($createLabelFn)
            ->modalSubmitAction(fn (StaticAction $action) => $action->label($createLabelFn)->extraAttributes(['id' => btn_id('create-relation')]))
            ->modalCancelAction(fn (StaticAction $action) => $action->extraAttributes(['id' => btn_id('cancel-create-relation')]))
            ->extraModalFooterActions(function () use ($createAction): array {
                return $createAction->canCreateAnother() ? [
                    $createAction->makeModalSubmitAction('createAnother', ['another' => true])
                        ->extraAttributes(['id' => btn_id('create-another-relation')])
                        ->label(Str::apa(implode(
                            ' ',
                            [
                                static::getCreateLabel(),
                                'and',
                                static::getCreateLabel(),
                                'another',
                            ]
                        ))),
                ] : [];
            })
            ->successNotificationTitle($createLabelPastFn);
    }
}
