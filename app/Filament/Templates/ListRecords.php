<?php

namespace App\Filament\Templates;

use App\Filament\Traits\HasProductTour;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords as BaseListRecords;
use Illuminate\Support\Str;
use JibayMcs\FilamentTour\Tour\HasTour;

class ListRecords extends BaseListRecords
{
    use HasProductTour;
    use HasTour;

    /**
     * @return array|Actions\Action[]|Actions\ActionGroup[]
     */
    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make('create')
                ->extraAttributes(['id' => btn_id('create')])
                ->label(Str::apa(static::getResource()::getCreateLabel() . ' ' . static::getResource()::getModelLabel())),
        ];
    }
}
