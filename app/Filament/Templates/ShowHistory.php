<?php

namespace App\Filament\Templates;

use Filament\Actions\Action;
use Illuminate\Support\Facades\URL;
use pxlrbt\FilamentActivityLog\Pages\ListActivities;

class ShowHistory extends ListActivities
{
    /**
     * @return array|Action[]|\Filament\Actions\ActionGroup[]
     */
    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->extraAttributes(['id' => btn_id('back')])
                ->url(URL::previous()),
        ];
    }
}
