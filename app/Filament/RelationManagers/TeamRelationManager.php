<?php

namespace App\Filament\RelationManagers;

use App\Filament\Templates\RelationManager;
use App\Filament\Traits\HasActions;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFields;
use App\Filament\Traits\HasFilters;
use App\Models\Model;
use App\Models\Role;
use App\Services\Gatekeeper;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\Alignment;
use Filament\Support\Enums\IconPosition;
use Filament\Support\Enums\IconSize;
use Filament\Tables;
use JaOcero\RadioDeck\Forms\Components\RadioDeck;

class TeamRelationManager extends RelationManager
{
    use HasActions;
    use HasColumns;
    use HasFields;
    use HasFilters;

    protected static function getTableColumnRole(): Tables\Columns\TextColumn
    {
        return static::getTableColumnBadge('role.name', 'Role', [])
            ->formatStateUsing(function ($state, ?Model $record): string {
                if ($record->model_id == $record->team?->user_id) {
                    $state = 'owner';
                }

                return strtoupper(is_object($state) ? $state->value : $state);
            })
            ->color(function ($state, ?Model $record): string {
                if ($record->model_id == $record->team?->user_id) {
                    return 'primary';
                } else {
                    $color = [
                        'super admin' => 'danger',
                        'admin' => 'warning',
                        'moderator' => 'success',
                        'manager' => 'info',
                        'default' => 'gray',
                    ];
                    $value = strtolower(is_object($state) ? $state->value : $state);

                    return $color[$value] ?? $color['default'];
                }
            });
    }

    protected static function getTableActionEditRow(): Tables\Actions\EditAction
    {
        return Tables\Actions\EditAction::make('edit')
            ->extraAttributes(['class' => btn_id('edit')])
            ->label('Edit')
            ->icon('heroicon-s-pencil-square');
    }

    protected static function getFormFieldRole(): RadioDeck
    {

        $roles = Role::where('is_team', true)->orderBy('name', 'desc')->get();
        $roleOptions = [];
        $roleDescriptions = [];
        $roleIcons = [];
        foreach ($roles as $role) {
            $roleOptions[$role->id] = $role->name;
            $roleDescriptions[$role->id] = $role->description;
            $roleIcons[$role->id] = $role->icon;
        }

        return RadioDeck::make('role_id')
            ->label('Role')
            ->options($roleOptions)
            ->descriptions($roleDescriptions)
            ->icons($roleIcons)
            ->required()
            ->default(Gatekeeper::getMemberRoleId())
            ->iconSize(IconSize::Large) // Small | Medium | Large | (string - sm | md | lg)
            ->iconPosition(IconPosition::Before) // Before | After | (string - before | after)
            ->alignment(Alignment::Center) // Start | Center | End | (string - start | center | end)
            ->color(Color::hex('#7137ff')) // supports all color custom or not
            ->columns(2)
            ->columnSpan(2)
        ;

    }

}
