<?php

namespace App\Filament\RelationManagers;

use App\Filament\Templates\RelationManager;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFields;
use App\Filament\Traits\HasFilters;
use App\Services\Auditor;
use App\Services\Formatter;
use Carbon\Carbon;
use ErrorException;
use Exception;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use ReflectionClass;
use ReflectionMethod;

class HistoryRelationManager extends RelationManager
{
    use HasColumns;
    use HasFields;
    use HasFilters;

    protected static string $relationship = 'ledgers';

    protected static ?string $title = 'History';

    public function infolist(Infolist $infolist): Infolist
    {

        $parent = $this->getOwnerRecord();
        $ledger = $infolist->getRecord();
        $modifiedFields = Auditor::getModifiedFields($ledger);
        $schema = [];

        foreach ($modifiedFields as $field => $changed) {

            $schema[] = Infolists\Components\Fieldset::make($changed['label'])
                ->schema([
                    Infolists\Components\TextEntry::make("{$field}_old")
                        ->label('From')
                        ->state($changed['from'])
                        ->listWithLineBreaks($changed['relation'])
                        ->bulleted($changed['relation']),
                    Infolists\Components\TextEntry::make("{$field}_new")
                        ->label('To')
                        ->state($changed['to'])
                        ->listWithLineBreaks($changed['relation'])
                        ->bulleted($changed['relation']),
                ]);

        }

        return $infolist->schema($schema);

    }

    /**
     * @throws Exception
     */
    public function table(Table $table): Table
    {

        $parent = $this->getOwnerRecord();
        $keys = $parent->getFillable();
        foreach ((new ReflectionClass($parent))->getMethods(ReflectionMethod::IS_PUBLIC) as $method) {
            if ($method->class != get_class($parent) ||
                ! empty($method->getParameters()) ||
                $method->getName() == __FUNCTION__) {
                continue;
            }
            try {
                $returnType = strval($method->getReturnType());
                if (
                    ($method->getName() != 'ledgers') &&
                    str_starts_with($returnType, 'Illuminate\\Database\\Eloquent\\Relations')
                    && str_ends_with($returnType, 'Many')
                ) {
                    $keys[] = $method->getName();
                }
            } catch (ErrorException $e) {
            }
        }
        sort($keys);
        $fields = [];
        foreach ($keys as $field) {
            $fields[$field] = Auditor::getEntryLabel($field);
        }

        return $table
            ->modifyQueryUsing(function (Builder $query) {
                $query->with('user')
                    ->whereJsonDoesntContain('modified', 'remember_token');
            })
            ->defaultSort('created_at', 'desc')
            ->columns([
                static::getTableColumnEvent(),
                static::getTableColumnName('SUSPENDED', 'owner.name', 'User')
                    ->visibleFrom('md'),
                Tables\Columns\TextColumn::make('properties')
                    ->label('Changed Fields')
                    ->formatStateUsing(function ($state, ?Model $record) {

                        $modified = Auditor::getModifiedFields($record);
                        $pivot = $record->getPivotData();

                        $changed = [];
                        if (! empty($modified)) {
                            $changed = array_map(Auditor::class.'::getEntryLabel', array_keys($modified));
                        }
                        if (! empty($pivot)) {
                            $changed[] = Auditor::getEntryLabel($pivot['relation']);
                        }

                        return implode(', ', $changed);

                    })
                    ->visibleFrom('md'),
                static::getTableColumnSince('created_at', 'Time Changed'),
            ])
            ->filters([
                static::getTableFilterSelect('event', Arr::only(Auditor::ACTIONS, config('accountant.events'))),
                static::getTableFilterRelation('owner', 'User'),
                static::getTableFilterSelect('field', $fields)
                    ->query(function (Builder $query, array $state) {
                        $query->where(function (Builder $query) use ($state) {
                            foreach ($state as $field) {
                                $query->orWhereJsonContains('modified', $field)
                                    ->orWhere('pivot->relation', $field);
                            }
                        });
                    }),
                DateRangeFilter::make('created_at')
                    ->label('Date Range')
                    ->maxDate(Carbon::now())
                    ->withIndicator(),
            ])
            ->filtersFormColumns(2)
            ->actions([
                Tables\Actions\ViewAction::make('view')
                    ->extraAttributes(['class' => btn_id('view')])
                    ->color('primary')
                    ->modalHeading(function ($record) {
                        $name = '';
                        if ($record->owner) {
                            $name = $record->owner->name;
                        }
                        $formattedTime = Formatter::datetime($record->created_at);
                        $formattedEvent = Auditor::getActionLabel($record->event);

                        return "{$name} {$formattedEvent} at {$formattedTime}";
                    }),
            ]);
    }
}
