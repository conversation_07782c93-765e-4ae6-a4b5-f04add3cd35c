<?php

namespace App\Filament\Traits;

use App\Models\Team;
use App\Services\Auditor;
use App\Services\Formatter;
use App\Services\Gatekeeper;
use App\Services\Labeller;
use Archilex\ToggleIconColumn\Columns\ToggleIconColumn;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Closure;
use Filament\Support\Enums\IconPosition;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;

trait HasColumns
{
    protected static function getTableColumnNestedName(): Tables\Columns\TextColumn
    {
        return static::getTableColumnName()
            ->formatStateUsing(function (string $state, ?Model $record) {
                $numAncestors = count($record->ancestors);
                $prefix = ($numAncestors > 0) ? str_repeat('&nbsp;', $numAncestors * 4).'—&nbsp;&nbsp;' : '';

                return new HtmlString("{$prefix}{$state}");
            });
    }

    protected static function getTableColumnName(
        string $archivedLabel = 'ARCHIVED',
        string $field = 'name',
        string $label = 'Name'
    ): BadgeableColumn {
        return BadgeableColumn::make($field)
            ->label($label)
            ->suffixBadges([
                Badge::make('archived')
                    ->label(strtoupper($archivedLabel))
                    ->color('danger')
                    ->visible(fn ($record): bool => method_exists($record, 'trashed') && $record->trashed()),
            ])
            ->separator('')
            ->sortable()
            ->searchable();
    }

    protected static function getTableColumnTextLink(string $field, string $label, Closure $linkClosure, ?Closure $formatClosure = null): TextColumn
    {
        if (is_null($formatClosure)) {
            $formatClosure = fn ($state) => $state;
        }

        return Tables\Columns\TextColumn::make($field)
            ->label($label)
            ->sortable()
            ->searchable()
            ->html()
            ->formatStateUsing(fn (string $state, $record): string => "<a href='{$linkClosure($record)}' class='hover:underline text-sm' @click.stop>{$formatClosure($state)}</a>"
            );
    }

    protected static function getTableColumnActive(string $field = 'is_active', string $label = 'Status'): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make($field)
            ->label($label)
            ->badge()
            ->formatStateUsing(fn (bool $state): string => $state ? 'ACTIVE' : 'INACTIVE')
            ->color(fn (bool $state): string => $state ? 'success' : 'gray');
    }

    protected static function getTableColumnEmail(string $field = 'email'): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make($field)
            ->label('Email')
            ->sortable()
            ->searchable()
            ->copyable()
            ->copyMessage('Copied!')
            ->copyMessageDuration(2500)
            ->icon('heroicon-o-clipboard')
            ->iconPosition(IconPosition::After);
    }

    protected static function getTableColumnCount(string $relation, string $label): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make("{$relation}_count")
            ->label($label)
            ->sortable()
            ->counts($relation);
    }

    protected static function getTableColumnParent(): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make('parent.name')
            ->label('Parent')
            ->sortable()
            ->searchable();
    }

    protected static function getTableColumnApprovalStatus(string $label = 'Community Status'): Tables\Columns\TextColumn
    {
        return static::getTableColumnBadge(
            'approvalStatus.status',
            $label,
            function (string $state, ?Model $record) {
                return $record->getApprovalStatusColor();
            },
            function (?Model $record) {
                $model = $record::class;

                return call_user_func("{$model}::getApprovalStatusIcons");
            }
        )
            ->formatStateUsing(function ($state, ?Model $record) {
                return $record->getApprovalStatusName();
            })
            ->icon(function ($record) {
                return $record->getApprovalStatusIcon();
            });
    }

    protected static function getTableColumnContributionRole(): Tables\Columns\TextColumn
    {
        return static::getTableColumnBadge('role', 'Role', ['author' => 'primary', 'default' => 'gray']);
    }

    protected static function getTableColumnBadge(string $field, string $label, string|array|Closure $color, array|Closure|null $icons = null): Tables\Columns\TextColumn
    {
        $field = Tables\Columns\TextColumn::make($field)
            ->label($label ?? Labeller::mutate($field))
            ->sortable()
            ->formatStateUsing(function ($state): string {
                return strtoupper(is_object($state) ? $state->value : $state);
            })
            ->badge();
        if (is_string($color)) {
            $field->color($color);
        } elseif (is_array($color)) {
            $field->color(function ($state) use ($color): string {
                $value = strtolower(is_object($state) ? $state->value : $state);

                return $color[$value] ?? $color['default'];
            });
        } else {
            $field->color(function ($state, ?Model $record) use ($color) {
                return $color($state, $record);
            });
        }

        /*
        if (!is_null($icons)) {
            $field->icons($icons);
        }*/
        return $field;
    }

    protected static function getTableColumnType(): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make('type')
            ->label('Type')
            ->sortable()
            ->formatStateUsing(function ($state): string {
                return Labeller::mutate($state->value);
            });
    }

    protected static function getTableColumnOwner(): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make('owner.name')
            ->label('Owner');
    }

    protected static function getTableColumnTeam(): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make('team.name')
            ->label('Team')
            ->visible(Gatekeeper::userCanAdminister(Team::class));
    }

    /**
     * @return TextColumn
     */
    protected static function getTableColumnCreated(bool $visible = false): Tables\Columns\TextColumn
    {
        return static::getTableColumnTimestamp('created_at', 'Created', $visible);
    }

    protected static function getTableColumnUpdated(bool $visible = false): Tables\Columns\TextColumn
    {
        return static::getTableColumnTimestamp('updated_at', 'Last Updated', $visible);
    }

    protected static function getTableColumnTimestamp(string $field, string $label, bool $visible = true): Tables\Columns\TextColumn
    {
        $column = Tables\Columns\TextColumn::make($field)
            ->label($label)
            ->dateTime()
            ->sortable();
        if (! $visible) {
            $column->toggleable(isToggledHiddenByDefault: true);
        }

        return $column;
    }

    protected static function getTableColumnSince(string $field, string $label, bool $visible = true): Tables\Columns\TextColumn
    {
        $column = Tables\Columns\TextColumn::make($field)
            ->label($label)
            ->since()
            ->tooltip(function ($record) use ($field) {
                return Formatter::datetime($record->$field);
            })
            ->sortable();
        if (! $visible) {
            $column->toggleable(isToggledHiddenByDefault: true);
        }

        return $column;
    }

    protected static function getTableColumnEvent(string $label = 'Action'): Tables\Columns\TextColumn
    {
        return static::getTableColumnBadge(
            'event',
            $label,
            [
                'created' => 'success',
                'updated' => 'warning',
                'restored' => 'success',
                'deleted' => 'danger',
                'forceDeleted' => 'danger',
                //                        'toggled'       =>  'warning',
                'synced' => 'warning',
                //                        'existingPivotUpdated'  =>  'warning',
                //                        'attached'      =>  'warning',
                //                        'detached'      =>  'warning',
                'default' => 'gray',
            ]
        )
            ->formatStateUsing(fn (string $state) => strtoupper(Auditor::getActionLabel($state)));
    }

    /**
     * @return TextColumn
     */
    protected static function getTableColumnFeaturedSequence(): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make('featured_seq')
            ->label('FS')
            ->tooltip('Featured Sequence')
            ->sortable()
            ->toggleable(isToggledHiddenByDefault: true);
    }

    protected static function getTableColumnLocked(): ToggleIconColumn
    {
        return ToggleIconColumn::make('is_locked')
            ->label(new HtmlString(Blade::render('<x-heroicon-s-lock-closed class="w-4 h-4" />')))
            ->alignCenter()
            ->onIcon('heroicon-s-lock-closed')
            ->offIcon('heroicon-s-lock-open')
            ->hoverColor('text-white')
            ->onColor('text-yellow-500')
            ->offColor('text-gray-500')
            ->size('sm')
            ->extraAttributes(fn ($record) => ['class' => $record->is_locked ? 'locked' : 'unlocked']);
    }

    protected static function getTableColumnKey(): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make('key')
            ->label('Key')
            ->sortable()
            ->searchable()
            ->copyable()
            ->copyMessage('Copied!')
            ->copyMessageDuration(2500)
            ->icon('heroicon-o-clipboard')
            ->iconPosition(IconPosition::After);
    }


}
