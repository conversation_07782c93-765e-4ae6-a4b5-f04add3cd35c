<?php

namespace App\Filament\Traits;

use App\Jobs\RunOperationOnModelsJob;
use App\Models\Traits\CanBeLocked;
use App\Models\Traits\CanBeModerated;
use App\Services\Gatekeeper;
use Closure;
use Filament\Notifications\Notification;
use Filament\Tables;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;
use Laravel\SerializableClosure\Exceptions\PhpVersionNotSupportedException;
use Laravel\SerializableClosure\SerializableClosure;

trait HasBulkActions
{
    protected static function getTableBulkActions(): array
    {

        $modelLabel = static::$pluralModelLabel ?? Str::plural(class_basename(static::$model));

        $actions = [

            static::getTableBulkActionBase(
                "Archive",
                'heroicon-s-archive-box',
                Gatekeeper::isAdmin() ? 'warning' : 'danger',
                "Archive Selected {$modelLabel}",
                null,
                fn ($model) => $model->delete(),
                "Selected {$modelLabel} Archived"
            )
                ->visible(Gatekeeper::userCan('delete', static::getModel()))
            ,

            static::getTableBulkActionBase(
                "Restore",
                'heroicon-s-arrow-path-rounded-square',
                'success',
                "Restore Selected {$modelLabel}",
                null,
                fn ($model) => $model->restore(),
                "Selected {$modelLabel} Restored"
            )
                ->visible(Gatekeeper::userCan('restore', static::getModel()))
            ,

            static::getTableBulkActionBase(
                "Delete",
                'heroicon-s-trash',
                'danger',
                "Delete Selected {$modelLabel}",
                'Note that this operation cannot be reversed.',
                fn ($model) => $model->forceDelete(),
                "Selected {$modelLabel} Deleted"
            )
                ->visible(Gatekeeper::userCan('force_delete', static::getModel()))
            ,

        ];

        if (
            Gatekeeper::userCan('moderate', static::getModel()) &&
            uses_trait(static::$model, CanBeModerated::class)
        ) {
            $actions[] = static::getTableBulkActionApprove($modelLabel);
            $actions[] = static::getTableBulkActionUnapprove($modelLabel);
        }

        if (
            Gatekeeper::userCan('lock', static::getModel()) &&
            uses_trait(static::$model, CanBeLocked::class)
        ) {
            $actions[] = static::getTableBulkActionLock($modelLabel);
            $actions[] = static::getTableBulkActionUnlock($modelLabel);
        }

        return [
            Tables\Actions\BulkActionGroup::make($actions)
                ->label('Status'),
        ];

    }

    protected static function getTableBulkActionSetAgentWeight(string $field, string $label, string $typePlural, Table $table): BulkAction
    {
        return static::getTableBulkActionBase(
            "Set {$label}",
            'heroicon-s-scale',
            'info',
            "Set {$label} for Selected {$typePlural}"
        )
            ->form(fn () => [static::getFormFieldWeight($field, $label)])
            ->action(function ($records) use ($field, $label, $table, $typePlural) {

                increase_timeout('HIGH');

                $data = $table->getLivewire()->getMountedTableBulkActionForm()->getState();
                $weight = $data[$field];
                $models = static::$model::whereIn('id', $records->modelKeys())
                    ->where($field, '!=', $weight)
                    ->get();

                $attrs = [];
                $attrs[$field] = $weight;
                static::runOperationOnModels($models, fn ($model) => $model->update($attrs));

                Notification::make()
                    ->title("{$label} Set for Selected {$typePlural}")
                    ->success()
                    ->icon('heroicon-s-scale')
                    ->send();

            })
        ;
    }

    protected static function getTableBulkActionApprove(string $modelLabel): BulkAction
    {
        return static::getTableBulkActionBase(
            'Approve',
            'heroicon-s-check-circle',
            'success',
            "Approve Selected {$modelLabel}",
            'Note that this operation cannot be reversed.',
            fn ($model) => $model->autoApprove(),
            "Selected {$modelLabel} Approved"
        );
    }

    protected static function getTableBulkActionUnapprove(string $modelLabel): BulkAction
    {
        return static::getTableBulkActionBase(
            'Unapprove',
            'heroicon-s-arrow-uturn-left',
            'warning',
            "Unapprove Selected {$modelLabel}",
            null,
            fn ($model) => $model->unapprove(),
            "Selected {$modelLabel} Unapproved"
        );
    }

    protected static function getTableBulkActionLock(string $modelLabel): BulkAction
    {
        return static::getTableBulkActionBase(
            'Lock',
            'heroicon-s-lock-closed',
            'warning',
            "Lock Selected {$modelLabel}",
            "The {$modelLabel} will not be able to be updated while locked.",
            fn ($model) => $model->lock(),
            "Selected {$modelLabel} Locked"
        );
    }

    protected static function getTableBulkActionUnlock(string $modelLabel): BulkAction
    {
        return static::getTableBulkActionBase(
            'Unlock',
            'heroicon-s-lock-open',
            'warning',
            "Unlock Selected {$modelLabel}",
            "The {$modelLabel} be able to be updated once locked.",
            fn ($model) => $model->unlock(),
            "Selected {$modelLabel} Unlocked"
        );
    }

    protected static function getTableBulkActionEnableSocial(string $labelPlural): Tables\Actions\BulkAction
    {
        return static::getTableBulkActionBase(
            'Enable Social Listings',
            'heroicon-s-eye',
            'success',
            "Enable Selected {$labelPlural} Social Listings",
            "Approved listings for the selected {$labelPlural} will be visible on Apologist Social.",
            ['social_active' => true],
            "Social Listings Enabled for Selected {$labelPlural}"
        );
    }

    protected static function getTableBulkActionDisableSocial(string $labelPlural): BulkAction
    {
        return static::getTableBulkActionBase(
            'Disable Social Listings',
            'heroicon-s-eye-slash',
            'danger',
            "Disable Selected {$labelPlural} Social Listings",
            "Listings for the selected {$labelPlural} will not be shown on Apologist Social.",
            ['social_active' => false],
            "Social Listings Disabled for Selected {$labelPlural}"
        );
    }

    protected static function getTableBulkActionChangeTeam(string $labelPlural, Table $table): BulkAction
    {
        return static::getTableBulkActionBase(
            'Change Team',
            'heroicon-s-users',
            'info',
            "Change Team for Selected {$labelPlural}"
        )
            ->form(function () {
                return [
                    static::getFormFieldTeam(),
                ];
            })
            ->action(function ($records) use ($table, $labelPlural) {

                increase_timeout('HIGH');

                $data = $table->getLivewire()->getMountedTableBulkActionForm()->getState();
                $teamId = $data['team'];
                $models = static::$model::whereIn('id', $records->modelKeys())
                    ->where('team_id', '!=', $teamId)
                    ->get();

                static::runOperationOnModels($models, fn ($model) => $model->update(['team_id' => $teamId]));

                Notification::make()
                    ->title("Team Changed for Selected {$labelPlural}")
                    ->success()
                    ->icon('heroicon-s-users')
                    ->send();

            });
    }

    protected static function getTableBulkActionChangeOwner(string $labelPlural, Table $table): BulkAction
    {
        return static::getTableBulkActionBase(
            'Change Owner',
            'heroicon-s-user',
            'info',
            "Change Owner for Selected {$labelPlural}"
        )
            ->form(function () {
                return [
                    static::getFormFieldOwner(),
                ];
            })
            ->action(function ($records) use ($table, $labelPlural) {

                increase_timeout('HIGH');

                $data = $table->getLivewire()->getMountedTableBulkActionForm()->getState();
                $userId = $data['user'];
                $models = static::$model::whereIn('id', $records->modelKeys())
                    ->where('user_id', '!=', $userId)
                    ->get();

                static::runOperationOnModels($models, fn ($model) => $model->update(['user_id' => $userId]));

                Notification::make()
                    ->title("Owner Changed for Selected {$labelPlural}")
                    ->success()
                    ->icon('heroicon-s-user')
                    ->send();

            });
    }

    protected static function getTableBulkActionBase(
        string $label,
        string $icon,
        string $color,
        ?string $modalHeading = null,
        ?string $modalDescription = null,
        Closure|array|null $fnOperation = null,
        ?string $successTitle = null,
        ?string $successDescription = null
    ): BulkAction {

        $action = Tables\Actions\BulkAction::make(Str::snake($label))
            ->extraAttributes(['id' => btn_id(Str::snake($label, '-'))])
            ->label($label)
            ->color($color)
            ->icon($icon)
            ->requiresConfirmation()
            ->modalSubmitActionLabel($label)
            ->modalIcon($icon)
            ->deselectRecordsAfterCompletion();

        if (! is_null($modalHeading)) {
            $action->modalHeading($modalHeading);
        }

        if (! is_null($modalDescription)) {
            $action->modalDescription($modalDescription);
        }

        if (! is_null($fnOperation)) {

            if (is_array($fnOperation)) {
                $updates = $fnOperation;
                $fnOperation = fn ($model) => $model->update($updates);
            }

            $action->action(function ($records) use ($fnOperation, $icon, $successTitle, $successDescription) {

                increase_timeout('HIGH');

                static::runOperationOnModels($records, $fnOperation);

                if (! is_null($successTitle)) {
                    $notification = Notification::make()
                        ->title($successTitle)
                        ->success()
                        ->icon($icon);
                    if (! is_null($successDescription)) {
                        $notification->body($successDescription);
                    } elseif ($records->count() > env('ASYNC_PROCESSING_THRESHOLD')) {
                        $notification->body('
                            This operation will be performed in the background.
                            It should complete within a few minutes.
                        ');
                    }
                    $notification->send();
                }

            });
        }

        return $action;

    }

    /**
     * @throws PhpVersionNotSupportedException
     */
    protected static function runOperationOnModels(Collection $records, Closure $fnOperation): void
    {
        if ($records->count() > env('ASYNC_PROCESSING_THRESHOLD')) {
            $firstModel = $records->first();
            dispatch(new RunOperationOnModelsJob(
                $firstModel::class,
                $records->modelKeys(),
                new SerializableClosure($fnOperation)
            ));
        } else {
            $records->each($fnOperation);
        }
    }
}
