<?php

namespace App\Filament\Traits;

use App\Enums\SourceContributionRole;
use App\Models\Contributor;
use App\Services\Gatekeeper;
use App\Services\Labeller;
use Closure;
use CodeWithDennis\FilamentSelectTree\SelectTree;
use Exception;
use Filament\Facades\Filament;
use Filament\Tables;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\Indicator;
use Illuminate\Database\Eloquent\Builder;

trait HasFilters
{
    /**
     * @return Filter
     *
     * @throws Exception
     */
    protected static function getTableFilterParent(): Tables\Filters\Filter
    {
        return Tables\Filters\Filter::make('parent')
            ->form([
                SelectTree::make('parent_id')
                    ->label('Parent')
                    ->relationship('parent', 'name', 'parent_id')
                    ->enableBranchNode()
                    ->searchable(),
            ])
            ->query(function (Builder $query, array $data) {
                return $query->when($data['parent_id'], function ($query, $parent_id) {
                    return $query->where('parent_id', $parent_id);
                });
            })
        ;
    }

    /**
     * @throws Exception
     */
    protected static function getTableFilterType(array $options): Tables\Filters\SelectFilter
    {
        return static::getTableFilterSelect('type', $options);
    }

    /**
     * @throws Exception
     */
    protected static function getTableFilterStatus(): Tables\Filters\SelectFilter
    {
        $model = static::$model;
        $options = call_user_func("{$model}::getApprovalStatusNames");

        return static::getTableFilterSelect('status', $options, 'Status')
            ->query(function (Builder $query, array $state): Builder {
                if (! empty($state['values'])) {
                    $query->whereHas('approvalStatus', function (Builder $query) use ($state) {
                        $query->whereIn('status', $state['values']);
                    });
                }

                return $query;
            })
        ;
    }

    /**
     * @throws Exception
     */
    protected static function getTableFilterSelect(string $field, array $options, ?string $label = null): Tables\Filters\SelectFilter
    {
        return Tables\Filters\SelectFilter::make($field)
            ->label($label ?? Labeller::mutate($field))
            ->multiple()
            ->options($options)
        ;
    }

    /**
     * @throws Exception
     */
    protected static function getTableFilterBoolean(string $field, string $label, ?string $offLabel = null): Tables\Filters\TernaryFilter
    {
        return Tables\Filters\TernaryFilter::make($field)
            ->label($label)
            ->boolean()
            ->placeholder('')
            ->trueLabel('Yes')
            ->falseLabel('No')
            ->indicateUsing(static::getBinaryIndicators($label, $offLabel ?? "Not {$label}"))
        ;
    }

    /**
     * @throws Exception
     */
    protected static function getTableFilterTeam(bool $all = false): Tables\Filters\SelectFilter
    {
        return static::getTableFilterRelation('team', 'Team')
            ->visible(
                Gatekeeper::userCan('moderate', static::getModel()) ||
                Gatekeeper::userCanAdminister(static::getModel())
            )
            ->default(((Filament::getCurrentPanel()->getId() == 'app') && !$all) ? [get_current_team_id()] : [])
        ;
    }

    /**
     * @throws Exception
     */
    protected static function getTableFilterOwner(): Tables\Filters\SelectFilter
    {
        return static::getTableFilterRelation('owner', 'Owner');
    }

    /**
     * @throws Exception
     */
    protected static function getTableFilterContributors(): Tables\Filters\SelectFilter
    {
        return static::getTableFilterRelation('contributors')
            ->indicator('Contributors')
            ->indicateUsing(function (array $data): ?string {
                if (empty($data['values'])) {
                    return null;
                }
                $contributors = Contributor::whereIn('id', array_unique($data['values']))->pluck('name')->toArray();
                $lastContributor = $contributors[count($contributors) - 1];

                return 'Contributors: '.str_replace(", {$lastContributor}", " & {$lastContributor}", implode(', ', $contributors));
            });
    }

    /**
     * @throws Exception
     */
    protected static function getTableFilterRelation(string $relation, ?string $label = null, string $optionField = 'name'): Tables\Filters\SelectFilter
    {
        return Tables\Filters\SelectFilter::make($relation)
            ->label($label ?? Labeller::mutate($relation))
            ->multiple()
            ->relationship($relation, $optionField ?? 'name')
            ->searchable()
        ;
    }

    /**
     * @throws Exception
     */
    protected static function getTableFilterArchived(): Tables\Filters\TernaryFilter
    {
        return static::getTableFilterTernary('deleted_at', 'Archived')
            ->default(0)
            ->indicateUsing(static::getTernaryIndicators('Archived', 'Active'))
        ;
    }

    /**
     * @throws \Exception
     */
    public static function getTableFilterSuspended(): Tables\Filters\TernaryFilter
    {
        return static::getTableFilterArchived()
            ->label('Suspended')
            ->indicateUsing(static::getTernaryIndicators('Suspended', 'Active'))
        ;
    }

    /**
     * @throws \Exception
     */
    public static function getTableFilterRevoked(): Tables\Filters\TernaryFilter
    {
        return static::getTableFilterArchived()
            ->label('Revoked')
            ->indicateUsing(static::getTernaryIndicators('Revoked', 'Valid'))
        ;
    }

    /**
     * @throws Exception
     */
    protected static function getTableFilterActive(): Tables\Filters\TernaryFilter
    {
        return static::getTableFilterTernary('deleted_at', 'Suspended')
            ->default(0);
    }

    /**
     * @throws Exception
     */
    protected static function getTableFilterIndexed(): Tables\Filters\TernaryFilter
    {
        return static::getTableFilterTernary('agent_indexed_at', 'Indexed for Agent')
            ->indicateUsing(static::getTernaryIndicators('Indexed', 'Unindexed', true))

        ;
    }

    /**
     * @throws Exception
     */
    protected static function getTableFilterTernary(string $field, string $label): Tables\Filters\TernaryFilter
    {
        return Tables\Filters\TernaryFilter::make($field)
            ->label($label)
            ->nullable()
            ->placeholder('')
            ->trueLabel('Yes')
            ->falseLabel('No')
        ;
    }

    /**
     * @throws Exception
     */
    protected static function getTableFilterContributionRole(): Tables\Filters\SelectFilter
    {
        return Tables\Filters\SelectFilter::make('role')
            ->multiple()
            ->options(SourceContributionRole::asOptions())
        ;
    }

    /**
     * @throws Exception
     */
    protected static function getTableFilterLanguage(): Tables\Filters\SelectFilter
    {
        return Tables\Filters\SelectFilter::make('language')
            ->multiple()
            ->options(config('agent.languages'))
        ;
    }

    /**
     * @throws Exception
     */
    protected static function getTableFilterFeatured(): Tables\Filters\TernaryFilter
    {
        return static::getTableFilterBoolean('featured_seq', 'Featured', 'Not Featured')
            ->nullable();
    }

    protected static function getBinaryIndicators(string $onLabel, string $offLabel): Closure
    {
        return function (array $state) use ($onLabel, $offLabel): array {
            if (is_null($state['value']) || ($state['value'] === '')) {
                return [];
            } elseif (intval($state['value'])) {
                return [Indicator::make($onLabel)];
            } else {
                return [Indicator::make($offLabel)];
            }
        };
    }

    protected static function getTernaryIndicators(string $onLabel, string $offLabel, bool $hideNull = false): Closure
    {
        return function (array $state) use ($onLabel, $offLabel, $hideNull): array {
            if (!$hideNull && (is_null($state['value']) || ($state['value'] === ''))) {
                return [Indicator::make("{$onLabel} or {$offLabel}")];
            } elseif (intval($state['value'])) {
                return [Indicator::make($onLabel)];
            } else {
                return [];
            }
        };
    }
}
