<?php

namespace App\Filament\Traits;

use App\Features\Analytics;
use App\Jobs\InvalidateFrontendCacheJob;
use App\Jobs\ManageListingSearchDocumentJob;
use App\Jobs\SyncFrontendListingUrlJob;
use App\Models\Collection;
use App\Services\Gatekeeper;
use Closure;
use Filament\Actions\Action as Action;
use Filament\Actions\StaticAction;
use Filament\Forms;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action as FormAction;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Actions\Action as TableAction;
use Filament\Tables\Actions\EditAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

use Laravel\Pennant\Feature;
use function Filament\Support\get_model_label;

trait HasActions
{
    protected const BEACON_SNIPPET_TEMPLATE = '<script async id="apg-beacon" data-lang="{lang}" data-corpus="{corpus}" '.
        'data-headless="{headless}" data-color="{color}" data-translation="{translation}" data-phone="{phone}" '.
        'data-qr="{qr}" src="https://apologist.ai/beacon/agent.min.js"></script>';

    protected const BEACON_SNIPPET_PARAMS = [
        'lang',
        'corpus',
        'headless',
        'color',
        'translation',
        'phone',
        'qr',
    ];

    protected static function getTableActionEditRow(): Tables\Actions\EditAction
    {
        return Tables\Actions\EditAction::make('edit')
            ->extraAttributes(['class' => btn_id('edit')])
            ->label('')
            ->icon('heroicon-s-chevron-right');
    }

    /**
     * @return EditAction
     */
    protected static function getTableActionEdit(?string $label = null): Tables\Actions\EditAction
    {
        $editAction = Tables\Actions\EditAction::make('edit')
            ->extraAttributes(['class' => btn_id('edit')])
            ->visible(fn ($record) => ! $record->trashed());

        return $editAction->modalHeading(fn () => Str::apa('Edit '.$label ?? $editAction->getModelLabel()))
            ->modalSubmitAction(fn (StaticAction $action) => $action->label('Save Changes')->extraAttributes(['id' => btn_id('save-relation')]))
            ->modalCancelAction(fn (StaticAction $action) => $action->extraAttributes(['id' => btn_id('cancel-save-relation')]));
    }

    /**
     * @return Tables\Actions\DeleteAction
     */
    protected static function getTableActionArchive(): Tables\Actions\Action
    {
        return static::decorateModalAction(
            Tables\Actions\DeleteAction::make('archive'),
            'archive',
            'Archive',
            'heroicon-s-archive-box',
            'danger'
        )
            ->visible(fn ($record) => ! $record->trashed())
            ->successNotificationTitle('Archived');
    }

    /**
     * @return Tables\Actions\DeleteAction
     */
    protected static function getTableActionSuspend(): Tables\Actions\Action
    {
        return static::decorateModalAction(
            Tables\Actions\DeleteAction::make('suspend'),
            'suspend',
            'Suspend',
            'heroicon-s-pause-circle',
            'danger'
        )
            ->visible(fn ($record) => ! $record->trashed())
            ->successNotificationTitle('Suspended');
    }

    /**
     * @return Tables\Actions\DeleteAction
     */
    protected static function getTableActionRevoke(): Tables\Actions\Action
    {
        return static::decorateModalAction(
            Tables\Actions\DeleteAction::make('revoke'),
            'revoke',
            'Revoke',
            'heroicon-s-no-symbol',
            'danger'
        )
            ->visible(fn ($record) => ! $record->trashed())
            ->successNotificationTitle('Revoked');
    }

    /**
     * @return Tables\Actions\ForceDeleteAction
     */
    protected static function getTableActionDelete(): Tables\Actions\Action
    {
        return static::decorateModalAction(
            Tables\Actions\ForceDeleteAction::make('delete'),
            'delete',
            'Delete',
            'heroicon-s-trash',
            'danger'
        )
            ->successNotificationTitle('Deleted');
    }

    /**
     * @return Tables\Actions\RestoreAction
     */
    protected static function getTableActionRestore(): Tables\Actions\Action
    {
        return static::decorateModalAction(
            Tables\Actions\RestoreAction::make('restore'),
            'restore',
            'Restore',
            'heroicon-s-arrow-path-rounded-square',
            'success'
        );
    }

    /**
     * @return Tables\Actions\RestoreAction
     */
    protected static function getTableActionReactivate(): Tables\Actions\Action
    {
        return static::decorateModalAction(
            Tables\Actions\RestoreAction::make('reactivate'),
            'reactivate',
            'Reactivate',
            'heroicon-s-arrow-up-circle',
            'success'
        )
            ->successNotificationTitle('Reactivated')
        ;
    }

    protected static function decorateModalAction(
        Tables\Actions\Action $baseAction,
        string $name,
        string $label,
        string $icon,
        string $color
    ): Tables\Actions\Action {
        return $baseAction
            ->extraAttributes(['class' => btn_id($name)])
            ->label($label)
            ->icon($icon)
            ->color($color)
            ->modalIcon($icon)
            ->modalHeading(fn (Model $record): string => "{$label} {$record->name}")
            ->modalSubmitAction(fn (StaticAction $action) => $action->extraAttributes(['id' => btn_id("confirm-{$name}}")]))
            ->modalCancelAction(fn (StaticAction $action) => $action->extraAttributes(['id' => btn_id("cancel-{$name}}")]))
        ;
    }

    protected static function getTableActionPreview(Closure $urlClosure): TableAction
    {
        return TableAction::make('view')
            ->extraAttributes(['class' => btn_id('view')])
            ->icon('heroicon-s-arrow-top-right-on-square')
            ->iconButton()
            ->tooltip('Preview')
            ->label(false)
            ->url($urlClosure)
            ->openUrlInNewTab()
            ->extraAttributes(['id' => btn_id('preview')])
        ;
    }

    protected static function getAnalyzeHeaderAction(string $analyzePage): Action
    {
        return Action::make('Analyze')
            ->extraAttributes(['class' => btn_id('analyze')])
            ->label('Analyze')
            ->iconButton()
            ->icon('heroicon-s-chart-bar-square')
            ->color('primary')
            ->url(fn ($record) => $analyzePage::getUrl(['model_id' => $record->id]))
            ->tooltip('Analyze')
            ->visible(fn () => Feature::active(Analytics::class))
        ;
    }

    public static function decorateViewAction(
        TableAction|Action $baseAction,
        Closure $labelClosure,
        Closure $activeClosure,
        Closure $urlClosure
    ): TableAction|Action {
        return $baseAction
            ->extraAttributes(['class' => btn_id('view')])
            ->label($labelClosure)
            ->iconButton()
            ->icon(fn ($record) => $activeClosure($record) ? 'heroicon-s-eye' : 'heroicon-s-eye-slash')
            ->color(fn ($record) => $activeClosure($record) ? 'primary' : 'gray')
            ->url($urlClosure)
            ->openUrlInNewTab()
            ->disabled(fn ($record) => !$activeClosure($record))
            ->tooltip($labelClosure)
        ;
    }

    protected static function decorateViewListingAction($baseAction): TableAction|Action
    {
        return static::decorateViewAction(
            $baseAction,
            fn ($record) => $record->frontendListingUrl ? 'View Social Listing' : 'Social Listing Not Active',
            fn ($record) => $record->frontendListingUrl,
            fn ($record) => $record->frontendListingUrl?->url
        );
    }

    protected static function decorateViewReferenceAction($baseAction): TableAction|Action
    {
        return $baseAction
            ->extraAttributes(['class' => btn_id('view-reference')])
            ->label('External Source')
            ->iconButton()
            ->icon('heroicon-s-arrow-top-right-on-square')
            ->url(fn ($record) => $record->url ?? $record->referral_url)
            ->openUrlInNewTab()
            ->color(fn ($record) => empty($record->url ?? $record->referral_url) ? 'gray' : 'primary')
            ->disabled(fn ($record) => empty($record->url ?? $record->referral_url))
            ->tooltip(fn ($record) => empty($record->url ?? $record->referral_url) ? 'No External Source Available' : 'View External Source')
        ;
    }

    protected static function decorateLockAction($baseAction, ?string $resource = null): TableAction|Action
    {
        if (is_null($resource)) {
            $resource = static::class;
        }
        $modelLabel = ucwords(get_model_label($resource::getModel()));

        return $baseAction
            ->extraAttributes(['id' => btn_id('lock')])
            ->tooltip("{$modelLabel} Unlocked")
            ->iconButton()
            ->color('gray')
            ->icon('heroicon-s-lock-open')
            ->visible(fn ($record) => ! $record->is_locked)
            ->disabled(fn ($record) => ! Gatekeeper::userCan('lock', $resource::getModel(), $record))
            ->action(function ($record, $livewire) {
                $record->lock();
                Notification::make()
                    ->title('Locked')
                    ->success()
                    ->send();
                $livewire->dispatch('$refresh');

                return true;
            })
            ->requiresConfirmation()
            ->modalIcon('heroicon-s-lock-closed');
    }

    protected static function decorateUnlockAction($baseAction, ?string $resource = null): TableAction|Action
    {
        if (is_null($resource)) {
            $resource = static::class;
        }
        $modelLabel = ucwords(get_model_label($resource::getModel()));

        return $baseAction
            ->extraAttributes(['id' => btn_id('unlock')])
            ->tooltip("{$modelLabel} Locked; Cannot Edit")
            ->iconButton()
            ->color('warning')
            ->icon('heroicon-s-lock-closed')
            ->visible(fn ($record) => $record->is_locked)
            ->disabled(fn ($record) => ! Gatekeeper::userCan('lock', $resource::getModel(), $record))
            ->action(function ($record, $livewire) {
                $record->unlock();
                Notification::make()
                    ->title('Unlocked')
                    ->success()
                    ->send();
                $livewire->dispatch('$refresh');

                return true;
            })
            ->requiresConfirmation()
            ->modalIcon('heroicon-s-lock-open');
    }

    /**
     * @return array|Tables\Actions\Action[]|Tables\Actions\ActionGroup[]
     */
    protected static function getRelationManagerTableActions(?string $label = null, ?Model $record = null): array
    {
        $restrictEditing = restrict_editing($record);

        return [
            static::getTableActionEdit($label)
                ->hidden($restrictEditing),
            static::getTableActionArchive()
                ->hidden($restrictEditing),
            static::getTableActionRestore()
                ->hidden($restrictEditing),
            static::getTableActionDelete()
                ->hidden($restrictEditing),
        ];
    }

    protected static function getGenerateBeaconAction(?Closure $visibilityClosure = null, ?Closure $sourceIdClosure = null): FormAction
    {

        if (is_null($visibilityClosure)) {
            $visibilityClosure = fn () => true;
        }
        if (is_null($sourceIdClosure)) {
            $sourceIdClosure = fn () => '';
        }

        $generateBeaconSnippet = function (Component $component, Get $get, Set $set, ?Model $record) use ($sourceIdClosure) {
            $snippet = static::BEACON_SNIPPET_TEMPLATE;
            foreach (static::BEACON_SNIPPET_PARAMS as $param) {
                $key = Arr::last(explode('.', $component->getKey()));
                $replace = $get($param);
                if ($param == 'headless') {
                    $replace = boolval($replace) ? 'true' : '';
                } elseif ($param == 'corpus') {
                    $replace = base64_encode($sourceIdClosure($record));
                }
                $snippet = str_replace('{'.$param.'}', $replace, $snippet);
                $snippet = str_replace(" data-{$param}=\"\"", '', $snippet);
            }
            if ($key == 'snippet') {
                return $snippet;
            } else {
                $set('snippet', $snippet);
            }
        };

        return FormAction::make('beacon')
            ->extraAttributes(['id' => btn_id('beacon')])
            ->label('Generate Beacon')
            ->tooltip('Generate Beacon embed code that draws only on context from this Source.')
            ->icon('heroicon-s-signal')
            ->outlined()
            ->requiresConfirmation()
            ->form([
                Fieldset::make('Beacon Options')
                    ->schema([
                        Forms\Components\Toggle::make('headless')
                            ->label('Hide Header')
                            ->helperText('Hide Beacon branding, language switcher, and help modal.')
                            ->afterStateUpdated($generateBeaconSnippet)
                            ->live()
                        ,
                        static::getFormFieldAgentLanguage()
                            ->afterStateUpdated($generateBeaconSnippet)
                            ->live()
                        ,
                        Forms\Components\ColorPicker::make('color')
                            ->label('White Label Color')
                            ->hexColor()
                            ->afterStateUpdated($generateBeaconSnippet)
                            ->live()
                        ,
                        static::getFormFieldTranslation()
                            ->afterStateUpdated($generateBeaconSnippet)
                            ->live()
                        ,
                        Forms\Components\TextInput::make('phone')
                            ->label('Contact Phone Number')
                            ->tel()
                            ->afterStateUpdated($generateBeaconSnippet)
                            ->live(onBlur: true)
                        ,
                        Forms\Components\TextInput::make('qr')
                            ->label('SMS QR Code Image URL')
                            ->url()
                            ->activeUrl()
                            ->afterStateUpdated($generateBeaconSnippet)
                            ->live(onBlur: true)
                        ,
                    ]),
                Forms\Components\TextInput::make('snippet')
                    ->label('Generated Beacon Snippet')
//                    ->rows(4)
//                    ->autosize()
                    ->hint('Click on clipboard icon to copy snippet')
                    ->readOnly()
                    ->formatStateUsing($generateBeaconSnippet)
                    ->extraAttributes(['class' => 'text-large font-mono'])
                    ->suffixAction(
                        FormAction::make('copy')
                            ->extraAttributes(['id' => btn_id('copy')])
                            ->icon('heroicon-o-clipboard')
                            ->action(function ($livewire, $state) {
                                $livewire->js('
                                    window.navigator.clipboard.writeText("'.addslashes($state).'");
                                    new FilamentNotification()
                                        .success()
                                        .title("Beacon snippet copied!")
                                        .body("Paste the snippet in your website\'s HTML.")
                                        .seconds(5)
                                        .send();
                                ');
                            })
                    ),
            ])
            ->modalWidth(MaxWidth::TwoExtraLarge)
            ->modalHeading('Embed Apologist Beacon on Your Site')
            ->modalDescription("
                Modify the controls below to generate a code snippet to paste into your site's HTML. You only need
                to change parameters whose default you would like to override.
            ")
            ->modalIcon('heroicon-o-signal')
            ->modalCancelActionLabel('Close')
            ->modalSubmitAction(false)
            ->visible($visibilityClosure);
    }

    protected static function getAgentDemoAction(?string $tooltip = null, ?Closure $visibilityClosure = null, ?Closure $sourceIdClosure = null): FormAction
    {

        if (is_null($visibilityClosure)) {
            $visibilityClosure = fn () => true;
        }
        if (is_null($sourceIdClosure)) {
            $sourceIdClosure = fn () => '';
        }

        return FormAction::make('demo')
            ->extraAttributes(['id' => btn_id('demo')])
            ->label('See Agent Demo ↗')
            ->tooltip($tooltip)
            ->icon('heroicon-o-sparkles')
            ->outlined()
            ->url(fn ($record) => env('AGENT_URL').env('AGENT_DEMO_PATH').base64_encode($sourceIdClosure($record)))
            ->openUrlInNewTab()
            ->visible($visibilityClosure);

    }

    protected static function getSocialReindexAction(Closure $restrictPromotionClosure): Actions
    {
        return Forms\Components\Actions::make([

            Forms\Components\Actions\Action::make('social_reindex')
                ->extraAttributes(['id' => btn_id('social_reindex')])
                ->label('Reindex')
                ->tooltip('Manually reindex this listing on Apologist Social if it has become out of sync.')
                ->color('primary')
                ->outlined()
                ->icon('heroicon-s-circle-stack')
                ->action(function (?Model $record) {
                    if (! empty($record)) {
                        dispatch(new SyncFrontendListingUrlJob($record));
                        dispatch(new InvalidateFrontendCacheJob($record));
                        dispatch(new ManageListingSearchDocumentJob($record));
                        Notification::make()
                            ->title('Submitted for reindexing')
                            ->success()
                            ->send();
                    }
                    return true;
                })
                ->requiresConfirmation()
                ->modalIcon('heroicon-s-circle-stack')
                ->visible(fn ($record) => ! is_null($record) && $record->shouldIndexSearch())
            ,

            Forms\Components\Actions\Action::make('social_learn')
                ->extraAttributes(['id' => btn_id('social_learn')])
                ->label('Learn More ↗')
                ->icon('heroicon-s-book-open')
                ->color('primary')
                ->outlined()
                ->url(env('SOCIAL_LEARN_URL'))
                ->openUrlInNewTab()
            ,

        ])
            ->alignRight()
            ->hidden($restrictPromotionClosure)
        ;
    }
}
