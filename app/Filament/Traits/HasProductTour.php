<?php

namespace App\Filament\Traits;

use Exception;
use Illuminate\Support\HtmlString;
use <PERSON>bayMcs\FilamentTour\Tour\Step;
use <PERSON><PERSON>Mcs\FilamentTour\Tour\Tour;

trait HasProductTour
{
    //    use HasTour;

    /**
     * @throws Exception
     */
    public function tours(): array
    {

        if (auth()->check() && ! is_mobile()) {

            $kbUrl = env('KNOWLEDGE_BASE_URL');
            $kbDashboardUrl = env('KNOWLEDGE_BASE_DASHBOARD_URL');
            $kbOrganizationUrl = env('KNOWLEDGE_BASE_ORGANIZATION_URL');
            $kbContributorsUrl = env('KNOWLEDGE_BASE_CONTRIBUTORS_URL');
            $kbAgentsUrl = env('KNOWLEDGE_BASE_AGENTS_URL');
            $kbSourcesUrl = env('KNOWLEDGE_BASE_SOURCES_URL');
            $kbCollectionsUrl = env('KNOWLEDGE_BASE_COLLECTIONS_URL');
            $productsUrl = env('PRODUCTS_URL');
            $supportUrl = env('SUPPORT_URL');

            return [
                Tour::make('tour_product')
                    ->steps(

                        // Start
                        Step::make()
                            ->title("Welcome, we're glad you're here!")
                            ->description("
                                In this tour, we'll show you how to navigate around the product. Once completed, you
                                should have a good understanding of where to find everything you need to get started promoting
                                your resources in the Apologist ecosystem!
                            "),

                        // User Menu Trigger
                        Step::make('.fi-user-menu .fi-user-avatar')
                            ->title('User Avatar')
                            ->description('This is your User avatar. Clicking on it opens the User menu.'),

                        // User Menu
                        Step::make('.fi-user-menu .fi-dropdown-panel')
                            ->title('User Menu')
                            ->description(new HtmlString("
                                This is the User menu. Here you'll find a link to edit your Profile, the
                                <a href='$kbUrl' target='_blank'>Knowledge Base ↗</a>, and other helpful User settings.
                            "))
                            ->clickOnNext('.fi-user-avatar'),

                        // Team Menu Trigger
                        Step::make('.fi-team-menu')
                            ->title('Team Selector')
                            ->description('
                                Ignite is organized by Teams, which allow you to collaborate with other Users. You can only
                                work within the context of a single Team at a time. Your current Team context is displayed
                                here. Clicking on it reveals the Team menu.
                            ')
                            ->clickOnNext('.fi-team-menu .fi-dropdown-trigger'),

                        // Team Menu
                        Step::make('.fi-team-menu .fi-dropdown-panel')
                            ->title('Team Menu')
                            ->description("
                                This is the Team menu. If you are member of any other Teams, you may click on one to switch to
                                that Team context. You may create a new Team at any time, making you its owner. If you are
                                the owner of the current Team, you'll be able to edit its Profile.
                            ")
                            ->clickOnNext('.fi-team-menu .fi-dropdown-trigger'),

                        // Side Nav
                        Step::make('.fi-sidebar-nav-groups')
                            ->title('Main Menu')
                            ->description("
                                This is Apologist Ignite's main menu that helps you manage the items that the current Team
                                owns. Next, we'll provide a summary of each one.
                            "),

                        // Dashboard
                        Step::make('.fi-sidebar-group-default .fi-sidebar-item:nth-child(1) a')
                            ->title('Dashboard')
                            ->description("
                                The Dashboard is the first screen you'll see when logging in or switching to a new Team
                                context. It displays recent Team activity, charts that show you how well your Team's content
                                is performing on various channels, and the status of any unapproved items the Team owns.
                                <br><br>
                                <a href='{$kbDashboardUrl}' target='_blank'>Learn more ↗</a>
                            "),

                        // Organization
                        Step::make('.fi-sidebar-group-default .fi-sidebar-item:nth-child(2)')
                            ->title('Organization')
                            ->description(new HtmlString("
                                Any Organization that produces evangelism or apologetics content of their own may add
                                a listing for their Organization on Apologist Social. Any content that you list will be
                                linked back to your Organization's profile.
                                <br><br>
                                <a href='{$kbOrganizationUrl}' target='_blank'>Learn more ↗</a>
                            ")),

                        // Contributors
                        Step::make('.fi-sidebar-group-default .fi-sidebar-item:nth-child(3)')
                            ->title('Contributors')
                            ->description(new HtmlString("
                                A Contributor is any person that was involved in publishing content
                                (book, article, video, etc). While the author is usually the primary Contributor for a written work, other
                                people may have participated in different roles — such as translators, editors,
                                commentators, and foreword / introduction writers.
                                Other content types of different Contributor types, such as hosts for podcast episodes.
                                <br><br>
                                <a href='{$kbContributorsUrl}' target='_blank'>Learn more ↗</a>
                            ")),

                        // Lists
                        Step::make('.fi-sidebar-group-default .fi-sidebar-item:nth-child(4)')
                            ->title('Lists')
                            ->description(new HtmlString("
                                A List is a flexible grouping of content. Content can be a part of multiple
                                Lists. Lists may be nested, offering unlimited possibilities.
                                <br><br>
                                <a href='{$kbCollectionsUrl}' target='_blank'>Learn more ↗</a>
                            ")),

                        // Agents
                        Step::make('.fi-sidebar-group-default .fi-sidebar-item:nth-child(5)')
                            ->title('Agents')
                            ->description(new HtmlString("
                                Agents are instances of conversational AIs, independently configured for a specific use case.
                                <br><br>
                                <a href='{$kbAgentsUrl}' target='_blank'>Learn more ↗</a>
                            ")),

                        // Sources
                        Step::make('.fi-sidebar-group-content')
                            ->title('Content')
                            ->description(new HtmlString("
                                Content that is shown to other Users in the Apologist ecosystem is created under
                                Content. They may be
                                <a href='{$kbUrl}#0-toc-title' target='_blank'>advertised on Apologist Agent</a> or
                                <a href='{$kbUrl}#1-toc-title' target='_blank'>Apologist Social</a> to
                                help you gain exposure for your evangelism and apologetics resources.
                                <br><br>
                                <a href='{$kbSourcesUrl}' target='_blank'>Learn more ↗</a>
                            "))
                            ->clickOnNext('.fi-logo-dark'),

                        // Product Menu
                        Step::make('.fi-product-menu-dark .fi-dropdown-panel')
                            ->title('Apologist Platform')
                            ->description(new HtmlString("
                                There's an entire Apologist ecosystem waiting to be explored! Check out our other products
                                to help you understand how it all fits together. You have automatically been added to the
                                mailing list so that you'll be informed of upcoming product news.
                                <br><br>
                                <a href='{$productsUrl}' target='_blank'>Learn more ↗</a>
                            "))
                            ->clickOnNext('.fi-logo-dark'),

                    )
                    ->alwaysShow(true)
                    ->colors('#fff', '#000'),
            ];

        } else {
            return [];
        }

    }
}
