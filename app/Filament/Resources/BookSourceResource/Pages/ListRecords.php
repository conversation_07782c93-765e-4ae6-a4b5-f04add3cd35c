<?php

namespace App\Filament\Resources\BookSourceResource\Pages;

use App\Filament\Resources\BookSourceResource;
use App\Filament\Resources\SourceResource\Pages\ListRecords as BaseListRecords;
use Filament\Actions\Action;

class ListRecords extends BaseListRecords
{
    protected static string $resource = BookSourceResource::class;

    protected function getHeaderActions(): array
    {
        return array_merge(
            [
                static::getAnthologiesHeaderAction(),
                static::getSeriesHeaderAction(),
                static::getImportHeaderAction(),
            ],
            parent::getHeaderActions()
        );
    }

    protected static function getImportHeaderAction(): Action
    {
        return Action::make('import')
            ->extraAttributes(['id' => btn_id('import')])
            ->label('Import')
            ->icon('heroicon-s-cloud-arrow-down')
            ->outlined()
            ->url(ImportBooks::getUrl());
    }
}
