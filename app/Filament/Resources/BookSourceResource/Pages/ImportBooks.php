<?php

namespace App\Filament\Resources\BookSourceResource\Pages;

use App\Enums\SourceType;
use App\Filament\Resources\BookSourceResource;
use App\Filament\Traits\HasFields;
use App\Jobs\ImportBookSourceDataJob;
use App\Models\Contributor;
use App\Models\Source;
use App\Services\Gatekeeper;
use App\Services\AmazonClient;
use Awcodes\Shout\Components\Shout;
use Filament\Forms;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Resources\Pages\Page;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class ImportBooks extends Page implements Forms\Contracts\HasForms
{
    use HasFields;
    use InteractsWithFormActions;
    //    use HasPageShield;

    protected static string $resource = BookSourceResource::class;

    protected static ?string $title = 'Import Books by Author or Keyword(s)';

    protected static ?string $breadcrumb = 'Import';

    protected static string $view = 'pages.app.import-books';

    public ?array $data = [];

    protected array $books = [];

    protected bool $fresh = false;

    protected bool $strict = false;

    protected ?Contributor $contributor = null;

    protected ?bool $keywords = null;

    protected ?string $sort = null;

    public function mount(): void
    {

        if (request()->has('contributor_id')) {
            $contributorId = intval(request()->input('contributor_id'));
            if ($contributorId) {
                $this->contributor = Contributor::where('id', $contributorId)->first();
            }
        }

        $this->form->fill();

    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Wizard::make($this->getSteps())
                    ->previousAction(fn (Action $action) => $action->label('Cancel')->url(BookSourceResource::getUrl()))
                    ->nextAction(fn (Action $action) => $action->label('Next Step'))
                    ->submitAction(new HtmlString(Blade::render(<<<'BLADE'
                            <x-filament::button
                                type="submit"
                                size="sm"
                                x-data="{ clicked: false }"
                                @click="clicked = true"
                            >
                                Import Books
                                <x-filament::loading-indicator class="h-5 w-5 inline" x-show="clicked" />
                            </x-filament::button>
                        BLADE))
                    )
                ,
            ])
            ->columns(1)
            ->statePath('data');
    }

    protected function getSteps(): array
    {
        return [
            $this->getStep1(),
            $this->getStep2(),
            $this->getStep3(),
        ];
    }

    protected function getStep1(): Step
    {
        return Step::make('Enter Keywords')
            ->icon('heroicon-s-magnifying-glass')
            ->schema([
                $this->getFormFieldContributorNotice()
                    ->columnSpan(['lg' => 2]),
                $this->getFormFieldContributor(),
                $this->getFormFieldSearch(),
                $this->getFormFieldContributorError()
                    ->columnSpan(['lg' => 2]),
                $this->getFormFieldSort()
                    ->columnSpan(1),
                $this->getFormFieldFreshToggle()
                    ->columnSpan(1),
                //                $this->getFormFieldStrictToggle()
                //                    ->columnSpan(1),
            ])
            ->columns(2)
            ->afterValidation(fn (Get $get) => $this->processStep1($get))
        ;
    }

    protected function processStep1(Get $get): void
    {

        increase_timeout('HIGH');

        $this->fresh = $get('fresh');
        $this->sort = $get('sort');

        if (request()->has('strict')) {
            $this->strict = true;
        }

        $keywords = trim($get('keywords'));
        if (empty($keywords)) {
            $keywords = $this->contributor->short_name;
        }
        $this->keywords = $keywords;

        $cacheTime = env_int('BOOK_CACHE_TIME');
        $cacheSlug = Str::slug($keywords);
        $client = $this->getApiClient();

        $cacheKey = "book_search.{$cacheSlug}_{$this->sort}";
        if ($this->fresh) {
            Cache::forget($cacheKey);
        }
        $this->books = Cache::remember($cacheKey, $cacheTime, function () use ($client, $keywords) {
            return $client->getBooksByKeyword(
                $keywords,
                $this->sort,
                $this->strict,
                env('AMAZON_CATEGORY_ID'),
                env('BOOK_TOPIC_MATCH')
            );
        });
        //        Log::debug(json_encode($this->books, JSON_PRETTY_PRINT));

    }

    protected function getStep2(): Step
    {
        return Step::make('Select Books')
            ->icon('heroicon-s-book-open')
            ->schema([
                $this->getFormFieldBooksNotice(),
                $this->getFormFieldExternalIds(),
                $this->getFormFieldBooksError(),
            ])
            ->afterValidation(fn (Get $get, Set $set) => $this->processStep2($get, $set))
            ->columns(1)
        ;
    }

    protected function processStep2(Get $get, Set $set): void
    {

        increase_timeout('HIGH');

        $client = $this->getApiClient();
        $books = [];
        $topic = env('BOOK_TOPIC_MATCH');
        //                    $cacheTime = env_int('BOOK_CACHE_TIME');
        foreach ($get('external_ids') as $bookId) {
            $cacheKey = "book.{$bookId}";
            if ($this->fresh) {
                Cache::forget($cacheKey);
            }
            $book = Cache::rememberForever($cacheKey, function () use ($client, $bookId, $topic) {
                return $client->getBook($bookId, $topic);
            });
            if ($book) {
                $books[] = $book;
            }
        }
        //                    Log::debug(json_encode($books, JSON_PRETTY_PRINT));
        $set('books', $books);

    }

    protected function getStep3(): Step
    {
        return Step::make('Import Books')
            ->icon('heroicon-s-cloud-arrow-down')
            ->schema([
                $this->getFormFieldImportNotice(),
                $this->getFormFieldBooksRepeater(),
                $this->getFormFieldAiDescriptions(),
                $this->getFormFieldAiCategories(),
                $this->getFormFieldAutoApprove(),
                $this->getFormFieldImportError(),
            ])
            ->columns(1);
    }

    protected function getBooksUrl(string $page = 'index'): string
    {
        return BookSourceResource::getUrl($page);
    }

    protected function getFormFieldContributorNotice(): Shout
    {
        return Shout::make('contributor_notice')
            ->content(new HtmlString("
                <p class='mb-2'>
                    Select an approved Contributor managed by this Team below.
                    Or, enter freeform keywords to search.
                </p>
                <p>
                    Once you click to go to the next step, a book search on Amazon.com will be performed to find the top books that match the keyword(s).
                    Be patient; this process could take several minutes.
                </p>
            "))
            ->color('warning')
            ->icon(false)
            ->visible(Contributor::approved()->count() > 0);
    }

    /**
     * @return Select
     */
    protected function getFormFieldContributor(): Forms\Components\Select
    {
        return Forms\Components\Select::make('contributor_id')
            ->label('Contributor')
            ->requiredIf('keywords', null)
            ->options(fn () => Contributor::approved()->get()->pluck('name', 'id')->toArray())
            ->searchable()
            ->default($this->contributor?->id)
            ->visible(Contributor::approved()->count() > 0)
            ->disabled(fn (Get $get) => ! empty($get('keywords')))
            ->afterStateUpdated(function (Get $get, Set $set) {
                $contributorId = $get('contributor_id');
                if ($contributorId) {
                    $keywords = Contributor::where('id', $contributorId)->first()->short_name;
                    $set('keywords', $keywords);
                }
            })
            ->live()
        ;
    }

    /**
     * @return TextInput
     */
    protected function getFormFieldSearch(): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make('keywords')
            ->label('... or Keyword(s)')
            ->default($this->contributor?->short_name)
            ->requiredIf('contributor_id', null)
            ->disabled(fn (Get $get) => ! empty($get('contributor_id')))
            ->live(onBlur: true)
        ;
    }

    protected function getFormFieldContributorError(): Shout
    {
        return Shout::make('no_contributors')
            ->content(new HtmlString("
                Your Team currently has no Contributors.
                <a href='{$this->getBooksUrl('create')}' class='text-primary-500 hover:text-primary-400 underline'>Add a new Contributor now.</a>
            "))
            ->color('danger')
            ->icon(false)
            ->visible(Contributor::approved()->count() == 0);
    }

    protected function getFormFieldFreshToggle(): Forms\Components\Toggle
    {
        return Forms\Components\Toggle::make('fresh')
            ->label('Get Fresh Results')
            ->helperText('Search results are cached for 24 hours by default for quicker load times. Enable this option if you need up-to-the-minute results.');
    }

    /**
     * @return Select
     */
    protected function getFormFieldSort(): Forms\Components\Select
    {
        return Forms\Components\Select::make('sort')
            ->label('Sort Results By')
            ->options([
                'featured' => 'Featured',
                'average_review' => 'Highest Rated',
                'most_recent' => 'Most Recent',
            ])
            ->default('featured');
    }

    protected function getFormFieldStrictToggle(): Forms\Components\Toggle
    {
        return Forms\Components\Toggle::make('strict')
            ->label('Use Strict Mode')
            ->helperText("Strict Mode revalidates each book against the author's name and only displays strict matches. Keeping Strict Mode off will return all results.");
    }

    protected function getFormFieldBooksNotice(): Shout
    {
        return Shout::make('books_notice')
            ->content(new HtmlString("
                <p class='mb-2'>
                    Select the books you wish to import below.
                    Any books already imported by your Team or another Team cannot be re-imported.
                </p>
                <p>
                    On the next screen, you will have a chance to edit each book's details if desired before it is imported.
                    Note that the next step could take several minutes to load as detailed information about each book is gathered from Amazon.com.
                </p>
            "))
            ->color('warning')
            ->icon(false)
            ->visible(fn () => count($this->books) > 0);
    }

    protected function getFormFieldExternalIds(): Forms\Components\CheckboxList
    {
        return Forms\Components\CheckboxList::make('external_ids')
            ->label(fn (Get $get) => "Select search results for \"{$get('keywords')}\"")
            ->required()
            ->searchable()
            ->searchDebounce(500)
            ->options(function (Get $get) {
                $options = [];
                foreach ($this->books as $book) {

                    $attrs = [
                        'published_on' => 'Published On',
                        //                        'publisher' => 'Publisher',
                        'rating' => 'Rating',
                        'external_id' => 'ASIN',
                        'referral_url' => 'URL',
                    ];
                    $attrStr = "<dl class='text-gray-600 dark:text-gray-400'>";
                    foreach ($attrs as $key => $label) {
                        if (isset($book[$key])) {
                            $value = ($key == 'referral_url') ? "<a href='{$book[$key]}' target='_blank' class='underline hover:text-gray-900 hover:dark:text-white'>Amazon.com ↗</a>" : $book[$key];
                            $attrStr .= "<dt class='font-bold float-left w-16'>{$label}:</dt><dd class='ml-16'>{$value}</dd>";
                        }
                    }
                    $attrStr .= '</dl>';

                    if ($this->bookAlreadyImported($book['external_id'])) {
                        $source = $this->getImportedBook($book['external_id']);
                        $attrStr .= "<div class='font-bold text-green-500'>Book Already Added</div>";
                        if ($source->team_id == get_current_team_id()) {
                            $actionUrl = BookSourceResource::getUrl('edit', ['record' => $source->id]);
                            $attrStr .= "
                                <a href='{$actionUrl}' target='_blank' class='bg-primary-500 hover:bg-primary-400 rounded-md text-xs inline-block px-2 py-1'>
                                    Manage Book ↗
                                </a>
                            ";
                        } else {
                            $attrStr .= "<div class='text-gray-600 dark:text-gray-400'>Currently managed by another team.</div>";
                        }
                    }

                    $options[$book['external_id']] = new HtmlString("
                        <div class='flex space-x-4'>
                            <a href='{$book['referral_url']}' target='_blank' class='block flex-none'><img src='{$book['image_url']}' alt='{$book['name']}' class='w-16 border border-gray-500 shadow-md'></a>
                            <div class='flex-1'>
                                <h4 class='text-lg font-medium leading-tight mb-1'>{$book['name']}</h4>
                                {$attrStr}
                            </div>
                        </div>
                    ");

                }

                return $options;
            })
            ->disableOptionWhen(fn (string $value): bool => $this->bookAlreadyImported($value))
            ->bulkToggleable()
            ->columns(2)
            ->gridDirection('row')
            ->columnSpan(1)
            ->required()
            ->visible(fn () => count($this->books) > 0);
    }

    protected function getFormFieldBooksError(): Shout
    {
        return Shout::make('no_books')
            ->content(new HtmlString("
                There were no results for the Contributor you selected.
                <a href='{$this->getBooksUrl()}' class='text-primary-500 hover:text-primary-400 underline'>Go back to your books list.</a>
            "))
            ->color('danger')
            ->icon(false)
            ->visible(fn () => count($this->books) == 0);
    }

    protected function getFormFieldImportNotice(): Shout
    {
        return Shout::make('import_notice')
            ->content(new HtmlString("
                <p class='mb-2'>
                    Expand book details below and make any desired changes.
                    You may also remove any of the books if you changed your mind about what you wish to import.
                </p>
                <p>
                    Only basic information is presented here, but you will have a chance to change anything else about the book once it has been imported.
                    Please note that final import processing may take several minutes to complete.
                </p>
            "))
            ->color('warning')
            ->icon(false)
            ->visible(fn (Get $get) => count($get('external_ids')) > 0);
    }

    protected function getFormFieldBooksRepeater(): Repeater
    {
        return Repeater::make('books')
            ->label(fn (Get $get) => "Selected books for search \"{$get('keywords')}\"")
            ->schema([
                static::getFormFieldName(true, true, 'Title'),
                static::getFormFieldSubtitle(),
                static::getFormFieldReferralUrl()
                    ->columnSpan(1),
                static::getFormFieldBlockRichText(),
                static::getFormFieldDescriptionActions('book'),
                Forms\Components\Hidden::make('external_id'),
                Forms\Components\Hidden::make('image_url'),
                Forms\Components\Hidden::make('authors')->formatStateUsing(fn ($state) => json_encode($state)),
                //                            Forms\Components\Hidden::make('type')->default('book'),
                Forms\Components\Hidden::make('published_on'),
                Forms\Components\Hidden::make('rating'),
                Forms\Components\Hidden::make('num_ratings'),
                Forms\Components\Hidden::make('publisher'),
                //                        Forms\Components\ViewField::make('meta')
                //                            ->view('filament.resources.book-source-resource.meta')
                //                            ->viewData([
                //                                'min' => 1,
                //                                'max' => 5,
                //                            ])
            ])
            ->columns(2)
            ->itemLabel(fn ($state) => $state['name'])
            ->addable(false)
            ->reorderable(false)
            ->collapsible()
            ->collapsed()
            ->required()
            ->visible(fn (Get $get) => count($get('external_ids')) > 0)
        ;
    }

    protected static function getFormFieldSubtitle(): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make('subtitle')
            ->label('Subtitle')
            ->maxLength(500);
    }

    protected function getFormFieldAiDescriptions(): Forms\Components\Toggle
    {
        return Forms\Components\Toggle::make('ai_descriptions')
            ->label('Rewrite All Descriptions Using AI')
            ->helperText('Use Apologist Agent to rewrite the book descriptions. You can always edit the description again prior to submitting the book for approval.')
            ->visible(fn (Get $get) => count($get('external_ids')) > 0)
            ->default(true);
    }

    protected function getFormFieldAiCategories(): Forms\Components\Toggle
    {
        return Forms\Components\Toggle::make('ai_categories')
            ->label('Auto-Categorize Using AI')
            ->helperText('Use Apologist Agent to select categories. You can always edit the categories again prior to submitting the book for approval.')
            ->visible(fn (Get $get) => count($get('external_ids')) > 0)
            ->default(true);
    }

    protected function getFormFieldAutoApprove(): Forms\Components\Toggle
    {
        return Forms\Components\Toggle::make('auto_approve')
            ->label('Auto-Approve')
            ->visible(fn (Get $get) => (count($get('external_ids')) > 0) && Gatekeeper::userCanAdminister(Source::class));
    }

    protected function getFormFieldImportError(): Shout
    {
        return Shout::make('no_imports')
            ->content(new HtmlString("
                You did not select any books to import, or none are available for import.
                <a href='{$this->getBooksUrl()}' class='text-primary-500 hover:text-primary-400 underline'>Go back to your books list.</a>
            "))
            ->color('danger')
            ->icon(false)
            ->visible(fn (Get $get) => count($get('external_ids')) == 0);
    }

    public function create(): void
    {

        Notification::make()
            ->title('Importing books ...')
            ->success()
            ->icon('heroicon-s-cloud-arrow-down')
            ->body('This may take a few minutes, but you can continue working in the meantime.')
            ->send();

        $data = $this->form->getState();
        foreach ($data['books'] as $book) {
            $source = Source::create([
                'name' => $book['name'],
                'subtitle' => $book['subtitle'],
                'type' => SourceType::BOOK->value,
                'external_id' => $book['external_id'],
                'team_id' => get_current_team_id(),
                'user_id' => auth()->user()->id,
            ]);
            dispatch(new ImportBookSourceDataJob($source, true, $book, $data['ai_descriptions'], $data['ai_categories'], $data['auto_approve'] ?? false));
        }

        $this->redirect(BookSourceResource::getUrl('index'), true);

    }

    protected function getApiClient(): AmazonClient
    {
        return new AmazonClient(env('RAINFOREST_API_KEY'), env('AMAZON_ACCOUNT_ID'));
    }

    public function getTitle(): string
    {
        return static::$title;
    }

    public function getBreadcrumb(): string
    {
        return static::$breadcrumb;
    }

    protected function getImportedBook(mixed $externalId): ?Model
    {
        return $this->getImportedBookQry($externalId)->first();
    }

    protected function bookAlreadyImported(mixed $externalId): bool
    {
        return $this->getImportedBookQry($externalId)->exists();
    }

    protected function getImportedBookQry(mixed $externalId): Builder
    {
        return Source::type(SourceType::BOOK->value)->externalId($externalId);
    }
}
