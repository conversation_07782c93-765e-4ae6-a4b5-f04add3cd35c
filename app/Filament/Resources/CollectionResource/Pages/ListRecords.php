<?php

namespace App\Filament\Resources\CollectionResource\Pages;

use App\Filament\Resources\ArticleSourceResource;
use App\Filament\Resources\BookSourceResource;
use App\Filament\Resources\CollectionResource;
use App\Filament\Resources\MediaSourceResource;
use App\Filament\Templates\ListRecords as BaseListRecords;
use Filament\Actions\Action;

class ListRecords extends BaseListRecords
{
    protected static string $resource = CollectionResource::class;

    protected static string $createLabel = 'create';

    protected static function getBooksHeaderAction(): Action
    {
        return Action::make('books')
            ->extraAttributes(['id' => btn_id('books')])
            ->label('Back to Books')
            ->visible(str_ends_with(url()->previous(), 'books'))
            ->icon('heroicon-o-arrow-left-circle')
            ->outlined()
            ->url(BookSourceResource::getUrl());
    }

    protected static function getArticlesHeaderAction(): Action
    {
        return Action::make('articles')
            ->extraAttributes(['id' => btn_id('articles')])
            ->label('Back to Articles & Papers')
            ->visible(str_ends_with(url()->previous(), 'articles'))
            ->icon('heroicon-o-arrow-left-circle')
            ->outlined()
            ->url(ArticleSourceResource::getUrl());
    }

    protected static function getMediaHeaderAction(): Action
    {
        return Action::make('media')
            ->extraAttributes(['id' => btn_id('media')])
            ->label('Back to Media')
            ->visible(str_ends_with(url()->previous(), 'media'))
            ->icon('heroicon-o-film')
            ->outlined()
            ->url(MediaSourceResource::getUrl());
    }
}
