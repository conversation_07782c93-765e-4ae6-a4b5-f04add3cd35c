<?php

namespace App\Filament\Resources;

use App\Enums\CollectionType;
use App\Enums\SourceType;

class SeriesCollectionResource extends CollectionResource
{
    protected static ?string $navigationIcon = 'heroicon-s-rectangle-stack';

    protected static ?string $collectionType = CollectionType::SERIES->value;

    protected static ?array $allowedSourceTypes = [
        SourceType::BOOK->value,
        SourceType::MEDIA->value,
    ];

    protected static ?string $modelLabel = 'Series';

    protected static ?string $pluralModelLabel = 'Series';

    protected static ?string $navigationLabel = 'Series';

    protected static ?string $slug = 'series';

    protected static string $createLabel = 'add';

    public static function getPermissionPrefixes(): array
    {
        return [];
    }
}
