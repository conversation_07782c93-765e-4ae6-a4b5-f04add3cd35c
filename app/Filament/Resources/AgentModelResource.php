<?php

namespace App\Filament\Resources;

use App\Enums\AgentModelType;
use App\Filament\RelationManagers\HistoryRelationManager;
use App\Filament\Resources\AgentModelResource\Api\Transformers\AgentModelTransformer;
use App\Filament\Resources\Resource as BaseResource;
use App\Models\Agent;
use App\Models\AgentModel;
use App\Models\AgentModelProvider;
use App\Services\Labeller;
use Exception;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Tables\Table;
use Filament\Tables;
use Filament\Forms\Components\Tabs;
use Illuminate\Support\Str;

class AgentModelResource extends BaseResource
{
    protected static ?string $model = AgentModel::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-s-cube-transparent';

    protected static bool $isScopedToTenant = false;

    protected static string $createLabel = 'add';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        static::getSummaryTab(),
                        static::getProviderTab(),
                        static::getConfigurationTab(),
                    ])
                    ->columnSpan(2)
                ,
            ]);
    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->reorderable('seq')
            ->columns([
                static::getTableColumnName(),
                static::getTableColumnKey()
                    ->visibleFrom('sm'),
                Tables\Columns\TextColumn::make('provider.name')
                    ->label('Provider')
                    ->sortable()
                    ->searchable()
                    ->visibleFrom('xl')
                ,
                Tables\Columns\TextColumn::make('num_credits')
                    ->label('Credits')
                    ->sortable()
                    ->visibleFrom('xl')
                ,
                Tables\Columns\TextColumn::make('type')
                    ->label('Type')
                    ->formatStateUsing(fn ($state) => Labeller::mutate($state->value))
                    ->sortable()
                    ->visibleFrom('xl')
                ,
                static::getTableColumnActive(),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterTernary('is_active', 'Active'),
                static::getTableFilterArchived(),
                static::getTableFilterSelect('type', AgentModelType::asOptions(), 'Type'),
                static::getTableFilterRelation('provider', 'Provider')
                    ->preload()
                ,
            ])
            ->filtersFormColumns(2)
            ->actions([
                static::getTableActionEditRow(),
            ])
            ->bulkActions(static::getTableBulkActions())
            ->paginated(static::$pageOptions)
            ->defaultPaginationPageOption(static::$defaultPageOption)
            ->defaultSort('seq')
        ;
    }

    protected static function getSummaryTab(): Tabs\Tab
    {
        $tiers = config('agent.categories');
        return Tabs\Tab::make('Summary')
            ->schema([
                static::getFormFieldName(),
                static::getFormFieldKey(),
                static::getFormFieldActive(),
                Forms\Components\Toggle::make('is_recommended')
                    ->label('Recommended')
                ,
                static::getFormFieldType(AgentModelType::class)
                    ->label('Model Class')
                    ->required()
                ,
                TextInput::make('num_credits')
                    ->label('# Credits')
                    ->integer()
                    ->default(1)
                    ->minValue(0)
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'The number of credits per usage.'
                    )
                ,
            ])
            ->columns(2)
        ;
    }

    protected static function getProviderTab(): Tabs\Tab
    {
        $providers = AgentModelProvider::orderBy('name')->pluck('name', 'id')->toArray();
        return Tabs\Tab::make('Provider')
            ->schema([
                Select::make('provider_id') // TODO: for some reason the normal relation way of doing this is NOT working !!!
                    ->label('Provider')
                    ->options($providers)
                    ->required()
                ,
//                static::getFormFieldRelation('provider', 'Provider'),
                static::getFormFieldProviderModel(),
                static::getFormFieldCost('input'),
                static::getFormFieldCost('output'),
            ])
            ->columns(2)
        ;
    }

    protected static function getConfigurationTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Configuration')
            ->schema([
                TextInput::make('max_tokens')
                    ->label('Max Output Tokens')
                    ->integer()
                    ->default(4096)
                    ->step(1)
                    ->minValue(0)
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'The maximum number of tokens the model can respond with.'
                    )
                ,
                static::getFormFieldLanguages(),
                Forms\Components\Toggle::make('supports_reasoning_effort')
                    ->label('Supports Reasoning Effort')
                ,
                Forms\Components\Toggle::make('supports_verbosity')
                    ->label('Supports Verbosity')
                ,
                Forms\Components\Toggle::make('supports_temperature')
                    ->label('Supports Temperature')
                    ->default(true)
                ,
                Forms\Components\Toggle::make('supports_top_p')
                    ->label('Supports Top P')
                    ->default(true)
                ,
                Forms\Components\Toggle::make('supports_frequency_penalty')
                    ->label('Supports Frequency Penalty')
                    ->default(true)
                ,
                Forms\Components\Toggle::make('supports_presence_penalty')
                    ->label('Supports Presence Penalty')
                    ->default(true)
                ,
                static::getFormFieldRelation('fallback_model_id', 'fallbackModel', 'Fallback Model'),
                TextInput::make('stop_sequence')
                    ->label('Stop Sequence')
                    ->default(env('AGENT_STOP_SEQUENCE'))
                ,
                TextInput::make('strip_sequence')
                    ->label('Strip from Output')
                    ->default(env('AGENT_STRIP_SEQUENCE'))
                ,
            ])
            ->columns(2)
        ;
    }

    public static function getFormFieldProviderModel(): Forms\Components\TextInput
    {
        return TextInput::make('provider_model')
            ->label('Provider Model ID')
            ->required()
            ->maxLength(50)
            ->unique(ignoreRecord: true)
        ;
    }

    protected static function getFormFieldCost($type): TextInput
    {
        $typeLabel = Str::title($type);
        return TextInput::make("{$type}_cost")
            ->label("{$typeLabel} Token Cost")
            ->numeric()
            ->inputMode('decimal')
            ->prefix('$')
            ->default(1)
            ->minValue(0)
            ->hintIcon(
                'heroicon-s-question-mark-circle',
                tooltip: "The cost per million {$type} tokens."
            )
        ;
    }

    /**
     * @return string[]
     */
    public static function getRelations(): array
    {
        return [
            HistoryRelationManager::class,
        ];
    }

    public static function getApiTransformer(): string
    {
        return AgentModelTransformer::class;
    }
}
