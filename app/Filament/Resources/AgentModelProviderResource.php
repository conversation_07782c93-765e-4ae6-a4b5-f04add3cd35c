<?php

namespace App\Filament\Resources;

use App\Filament\RelationManagers\HistoryRelationManager;
use App\Filament\Resources\AgentModelProviderResource\Api\Transformers\AgentModelProviderTransformer;
use App\Filament\Resources\Resource as BaseResource;
use App\Models\AgentModelProvider;
use Exception;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class AgentModelProviderResource extends BaseResource
{
    protected static ?string $model = AgentModelProvider::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-s-cloud';

    protected static bool $isScopedToTenant = false;

    protected static string $createLabel = 'add';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Fieldset::make('Agent Provider Information')
                    ->schema([
                        static::getFormFieldName(),
                        static::getFormFieldKey(),
                        static::getFormFieldActive(),
                    ]),
            ]);
    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                static::getTableColumnName(),
                static::getTableColumnKey(),
                static::getTableColumnActive(),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterArchived(),
            ])
            ->actions([
                static::getTableActionEditRow(),
            ])
            ->bulkActions(static::getTableBulkActions())
            ->paginated(static::$pageOptions)
            ->defaultPaginationPageOption(static::$defaultPageOption);
    }

    /**
     * @return string[]
     */
    public static function getRelations(): array
    {
        return [
            HistoryRelationManager::class,
        ];
    }

    public static function getApiTransformer(): string
    {
        return AgentModelProviderTransformer::class;
    }
}
