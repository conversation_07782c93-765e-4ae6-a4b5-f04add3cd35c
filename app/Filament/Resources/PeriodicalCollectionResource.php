<?php

namespace App\Filament\Resources;

use App\Enums\CollectionType;
use App\Enums\SourceType;

class PeriodicalCollectionResource extends CollectionResource
{
    protected static ?string $navigationIcon = 'heroicon-s-newspaper';

    protected static ?string $collectionType = CollectionType::PERIODICAL->value;

    protected static ?array $allowedSourceTypes = [
        SourceType::ARTICLE->value,
    ];

    protected static ?string $modelLabel = 'Periodical';

    protected static ?string $pluralModelLabel = 'Periodicals';

    protected static ?string $navigationLabel = 'Periodicals';

    protected static ?string $slug = 'periodicals';

    protected static string $createLabel = 'add';

    public static function getPermissionPrefixes(): array
    {
        return [];
    }
}
