<?php

namespace App\Filament\Resources;

use App\Enums\AgentApiResponseFormat;
use App\Enums\AgentModelType;
use App\Enums\AgentVerbosity;
use App\Enums\CollectionType;
use App\Enums\AgentReasoningEffort;
use App\Enums\SourceType;
use App\Features\AgentMedia;
use App\Filament\RelationManagers\HistoryRelationManager;
use App\Filament\Resources\AgentResource\RelationManagers\AgentIntegrationsRelationManager;
use App\Filament\Resources\AgentResource\RelationManagers\AgentQuestionsRelationManager;
use App\Filament\Resources\AgentResource\RelationManagers\AgentTokensRelationManager;
use App\Filament\Resources\Resource as BaseResource;
use App\Filament\Traits\CanBeAdministered;
use App\Filament\Traits\CanBeLocked;
use App\Filament\Traits\CanBeModerated;
use App\Filament\Traits\CanBeOrdered;
use App\Filament\Traits\CanBePromoted;
use App\Filament\Traits\CanBeReplicated;
use App\Models\Agent;
use App\Models\AgentModel;
use App\Models\Category;
use App\Models\Collection;
use App\Models\Contributor;
use App\Models\Source;
use App\Models\Tag;
use App\Models\Team;
use App\Services\Gatekeeper;
use App\Services\Labeller;
use Awcodes\Shout\Components\Shout;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Closure;
use dacoto\DomainValidator\Validator\Domain;
use Exception;
use Filament\Forms;
use Filament\Forms\Components\ColorPicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieTagsInput;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Resources\Concerns\Translatable;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\Alignment;
use Filament\Support\Enums\IconPosition;
use Filament\Support\Enums\IconSize;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use JaOcero\RadioDeck\Forms\Components\RadioDeck;
use Laravel\Pennant\Feature;
use RalphJSmit\Filament\RecordFinder\Forms\Components\RecordFinder;

class AgentResource extends BaseResource implements HasShieldPermissions
{
    use CanBeAdministered;
    use CanBeLocked;
    use CanBeModerated;
    use CanBeOrdered;
    use CanBePromoted;
    use CanBeReplicated;
    use Translatable;

    protected static ?string $model = Agent::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-s-sparkles';

    protected static ?string $pluralModelLabel = 'Agents';

    protected static string $createLabel = 'create';

    protected static array $customPermissions = [
        'test',
    ];

    protected const FINDER_PAGE_SIZE = 10;

    public static function form(Form $form): Form
    {

        $canAdminister = Gatekeeper::userCanAdminister(Agent::class);
        $restrictEditingClosure = fn ($record) => restrict_editing($record);
        $restrictEditingOrAdminClosure = fn ($record) => restrict_editing($record) || ! $canAdminister;
        $cannotActivateAgentClosure = fn ($record) => !is_null($record) && !$record->team->credits()->canSpend($record->getCreditUsage());
        $team = get_current_team();

        $upgradeNotice = Shout::make('agent_upgrade_notice')
            ->content(fn ($record) => new HtmlString(view(
                'components.notices.agent-upgrade',
                [
                    'change_subscription_url' => route('filament.app.pages.billing.subscription', ['tenant'=>get_current_team_id()]),
                ]
            )))
            ->color('warning')
            ->icon(false)
            ->columnSpan(2)
        ;

        return $form
            ->schema([

                Shout::make('agent_disabled_notice')
                    ->visible($cannotActivateAgentClosure)
                    ->content(fn ($record) => new HtmlString(view(
                        'components.notices.agent-deactivated',
                        [
                            'buy_credits_url' => route('filament.app.pages.billing.products', ['tenant'=>get_current_team_id()]),
                            'edit_team_url' => route('filament.app.resources.teams.edit', ['tenant'=>get_current_team_id(), 'record'=>get_current_team_id()]),
                        ]
                    )))
                    ->color('warning')
                    ->icon(false)
                    ->columnSpan(2)
                ,

                Tabs::make('Tabs')
                    ->tabs([

                        static::getSummaryTab($cannotActivateAgentClosure)
                            ->disabled($restrictEditingClosure),

                        static::getMessagingTab($team->getPlanOption('agent.white_label'), $upgradeNotice)
                            ->disabled($restrictEditingClosure),

                        static::getAppearanceTab($team->getPlanOption('agent.white_label'), $upgradeNotice)
                            ->disabled($restrictEditingClosure),

                        static::getCreatorTab($team->getPlanOption('agent.creator_info'), $upgradeNotice)
                            ->disabled($restrictEditingClosure),

                        static::getMetaTab($team->getPlanOption('agent.white_label'), $upgradeNotice)
                            ->disabled($restrictEditingClosure),

                        static::getInstructionsTab($team->getPlanOption('agent.instructions'), $upgradeNotice)
                            ->disabled($restrictEditingClosure),

                        static::getConfigurationTab($team->getPlanOption('agent.realtime_translation'), $upgradeNotice)
                            ->disabled($restrictEditingClosure),

                        static::getSourcesTab(
                            $form,
                            $team->getPlanOption('agent.team_corpus'),
                            $team->getPlanOption('agent.custom_sources'),
                            $team->getPlanOption('agent.media'),
                            $upgradeNotice
                        )
                            ->disabled($restrictEditingClosure),

                        static::getAdvancedTab(
                            $team->getPlanOption('agent.model_categories', true),
                            $team->getPlanOption('agent.instructions'),
                            $team->getPlanOption('agent.advanced_config'),
                            $upgradeNotice
                        )
                            ->disabled($restrictEditingClosure),

//                        static::getPersonaTab()
//                            ->disabled($restrictEditingClosure),

                        static::getAdminTab()
                            ->disabled($restrictEditingOrAdminClosure)
                            ->visible($canAdminister),

                    ])
                    ->persistTabInQueryString()
                    ->id('agent'),

            ])
            ->columns(1);
    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                static::$isScopedToTenant = false;
                if (! Gatekeeper::userCanAdminister(static::getModel())) {
                    $query->whereBelongsTo(get_current_team());
                }
            })
            ->reorderable('seq')
            ->defaultSort('seq')
            ->columns([
                static::getTableColumnName(),
                TextColumn::make('target_worldview')
                    ->label('Target Worldview'),
                static::getTableColumnActive(),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterBoolean('is_active', 'Active'),
                static::getTableFilterBoolean('is_locked', 'Locked', 'Unlocked'),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
                static::getTableFilterArchived(),
                static::getTableFilterTeam(),
            ])
            ->actions([
                static::decorateLockAction(Action::make('Lock')),
                static::decorateUnlockAction(Action::make('Unlock')),
                static::decorateViewAction(
                    Action::make('View'),
                    fn ($record) => $record->is_active ? 'View Agent' : 'Agent Not Active',
                    fn ($record) => $record->is_active,
                    fn ($record) => $record->getUrl()
                ),
                static::getTableActionEditRow(),
            ])
            ->bulkActions(static::getTableBulkActions())
            ->paginated(static::$pageOptions)
            ->defaultPaginationPageOption(static::$defaultPageOption)
        ;
    }

    protected static function getSummaryTab(Closure $cannotActivateAgentClosure): Tabs\Tab
    {
        $supportUrl = env('SUPPORT_URL');
        return Tabs\Tab::make('Summary')
            ->schema([

                static::getFormFieldName(true, false)
                    ->live(onBlur: true)
                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                        if (empty($get('slug'))) {
                            $set('slug', Str::slug($state));
                        }
                    })
                ,

                static::getFormFieldActive('
                    Only active Agents will be visible to the public.
                    This Agent will be disabled if you enable the "pay-as-you-go" team option and run out of credits.
                ')
                    ->disabled($cannotActivateAgentClosure)
                ,

                Fieldset::make('domain')
                    ->id('domain')
                    ->schema([
                        static::getFormFieldSlug()
                            ->label(false)
                            ->required(false)
                            ->requiredWithout('vanity_domain')
                            ->label('Subdomain')
                            ->columnSpan(['default' => 3]),
                        Select::make('root_domain')
                            ->label('Root Domain')
                            ->options(function () {
                                $options = [];
                                $domains = env_array('AGENT_ROOT_DOMAINS');
                                if (Gatekeeper::isAdmin()) {
                                    $domains = array_merge(env_array('AGENT_ADMIN_ROOT_DOMAINS'), $domains);
                                }
                                foreach ($domains as $rootDomain) {
                                    $options[$rootDomain] = ".{$rootDomain}";
                                }
                                return $options;
                            })
                            ->selectablePlaceholder(false)
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Agent will be available at [subdomain].[root_domain]. Note that each of the 2 parts are independently editable.',
                            )
                            ->columnSpan(['default' => 4]),
                    ])
                    ->label('Standard Subdomain')
                    ->columns(['default' => 7, 'xs' => 7, 'sm' => 7, 'md' => 7, 'lg' => 7, 'xl' => 7, '2xl' => 7])
                    ->columnSpan(1)
                ,

                TextInput::make('vanity_domain')
                    ->label('Vanity Domain')
                    ->maxLength(250)
                    ->rules([new Domain])
                    ->requiredWithout('slug')
                    ->unique(ignoreRecord: true, modifyRuleUsing: static::getUniqueByTeamAndTypeValidationClosure())
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'Agent will also be available at the exact domain that you enter, as long as you also activate it using the directions below.'
                    )
                    ->helperText(view('components.help.agent-vanity-domain'))
                ,

                static::getFormFieldActive(
                    'Require users to enter a username and password to access the Agent.',
                    'has_basic_auth',
                    'Enable Password Protection'
                )
                    ->live()
                    ->columnSpan(2)
                ,

                TextInput::make('basic_auth_user')
                    ->label('Username')
                    ->maxLength(50)
                    ->required(fn (Get $get) => $get('has_basic_auth'))
                    ->disabled(fn (Get $get) => ! $get('has_basic_auth'))
                ,

                TextInput::make('basic_auth_password')
                    ->label('Password')
                    ->maxLength(250)
                    ->password()
                    ->autocomplete(false)
                    ->revealable()
                    ->required(fn (Get $get) => $get('has_basic_auth'))
                    ->disabled(fn (Get $get) => ! $get('has_basic_auth'))
                ,


            ])
            ->columns(2)
        ;
    }

    protected static function getMessagingTab(bool $canWhiteLabel, Shout $upgradeNotice): Tabs\Tab
    {
        $whiteLabelUpgradeNotice = clone $upgradeNotice;
        return Tabs\Tab::make('Messaging')
            ->icon('heroicon-s-language')
            ->iconPosition(IconPosition::Before)
            ->schema([
                static::getTranslateActions(
                    'messaging',
                    [
                        'intro_preamble',
                        'intro_headline',
                        'intro_description',
                        'questions_title',
                        'footer_text',
                        'footer_cta_label',
                        'footer_cta_url',
                        'response_footer_content',
                    ]
                ),
                Forms\Components\Fieldset::make('intro')
                    ->label('Intro Screen')
                    ->schema([
                        Forms\Components\Group::make()
                            ->schema([
                                TextInput::make('intro_preamble')
                                    ->label('Intro Preamble')
                                    ->live(onBlur: true),
                                TextInput::make('intro_headline')
                                    ->label('Intro Headline')
                                    ->live(onBlur: true),
                                TextInput::make('questions_title')
                                    ->label('Sample Questions Title')
                                    ->live(onBlur: true),
                            ])
                            ->columnSpan(['default' => 2, 'md' => 1])
                        ,
                        Forms\Components\Group::make()
                            ->schema([
                                Textarea::make('intro_description')
                                    ->label('Intro Description')
                                    ->rows(5)
                                    ->live(onBlur: true),
                            ])
                            ->columnSpan(['default' => 2, 'md' => 1])
                        ,
                    ])
                    ->columnSpan(2),
                Forms\Components\Fieldset::make('response_footer')
                    ->label('Response Footer')
                    ->schema([
                        TextInput::make('response_footer_threshold')
                            ->label('# Prompts Before Showing Response Footer')
                            ->numeric()
                            ->step(1)
                            ->minValue(1)
                            ->maxValue(5)
                            ->default(1)
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Only show the response footer after the user has prompted this many times. Values 1 to 5.'
                            )
                        ,
                        static::getFormFieldHtmlEditor('response_footer_content', 'Response Footer HTML')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'You can inject merge field values into link URLs as per the key below.'
                            )
                            ->helperText(view('components.help.agent-footer-merge'))
                            ->columnSpan(2),
                    ])
                    ->columnSpan(2),
                $whiteLabelUpgradeNotice
                    ->visible(!$canWhiteLabel)
                ,
                Forms\Components\Fieldset::make('footer')
                    ->label('Global Footer')
                    ->schema([
                        Forms\Components\TextInput::make('footer_text')
                            ->label('Footer Text')
                            ->maxLength(100)
                            ->columnSpan(2),
                        static::getFormFieldActive(
                            false,
                            'hide_footer_cta',
                            'Hide CTA Button in Footer'
                        )
                            ->live()
                            ->columnSpan(2)
                        ,
                        static::getFormFieldUrl('footer_cta_url', 'Footer CTA URL')
                            ->disabled(fn (Get $get) => $get('hide_footer_cta')),
                        Forms\Components\TextInput::make('footer_cta_label')
                            ->label('Footer CTA Label')
                            ->maxLength(50)
                            ->disabled(fn (Get $get) => $get('hide_footer_cta'))
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: "Defaults to 'Give'"
                            ),
                    ])
                    ->columnSpan(2)
                    ->disabled(!$canWhiteLabel)
                ,
            ])
            ->columns(2)
        ;
    }

    protected static function getAppearanceTab(bool $canWhiteLabel, Shout $upgradeNotice): Tabs\Tab
    {
        $whiteLabelUpgradeNotice = clone $upgradeNotice;
        return Tabs\Tab::make('Appearance')
            ->schema([
                static::getFormFieldSelect('theme', ['dark' => 'Dark', 'light' => 'Light'], 'Theme')
                    ->default('dark')
                ,
                ColorPicker::make('color')
                    ->label('Primary Color')
                    ->required()
                    ->default(env('AGENT_DEFAULT_PRIMARY_COLOR'))
                ,
                $whiteLabelUpgradeNotice
                    ->visible(!$canWhiteLabel)
                ,
                Forms\Components\Group::make()
                    ->schema([
                        ColorPicker::make('background_color')
                            ->label('Background Color')
                            ->disabled(!$canWhiteLabel)
                        ,
                        static::getFormFieldActive(
                            'Hide the logo, translation switcher, and language switcher when embedded.',
                            'hide_header',
                            'Hide Header when Embedded'
                        ),
                    ])
                    ->disabled(!$canWhiteLabel)
                ,
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\FileUpload::make('background_path')
                            ->label('Background Image / Video')
                            ->directory('agents/backgrounds')
                            ->maxSize(10240)
                            ->acceptedFileTypes([
                                'image/*',
                                'video/*',
                            ])
                            ->helperText('Up to 10 MB')
                            ->disabled(!$canWhiteLabel)
                        ,
                    ])
                    ->disabled(!$canWhiteLabel)
                ,
                static::getFormFieldUrl('display_font_url', 'Headline Font URL')
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'Any web-accessible font URL may be entered here — including a Google font.',
                    )
                    ->live()
                    ->disabled(!$canWhiteLabel)
                ,
                static::getFormFieldUrl('body_font_url', 'Body Font URL')
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'Any web-accessible font URL may be entered here — including a Google font.',
                    )
                    ->live()
                    ->disabled(!$canWhiteLabel)
                ,
                TextInput::make('display_font_name')
                    ->label('Display Font Name')
                    ->maxLength(100)
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'The name of the font. Google Font names will be automatically added.',
                    )
                ,
                TextInput::make('body_font_name')
                    ->label('Body Font Name')
                    ->maxLength(100)
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'The name of the font. Google Font names will be automatically added.',
                    )
                ,
                Textarea::make('custom_styles')
                    ->label('Custom Styles (advanced)')
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: "You may add custom CSS here, and it will be applied your Agent's interface."
                    )
                    ->helperText(view('components.help.agent-custom-styles'))
                    ->maxLength(1000)
                    ->rows(10)
                    ->extraAttributes(['class'=>'font-mono'])
                    ->columnSpan(2)
                    ->disabled(!$canWhiteLabel)
                ,
            ])
            ->columns(2)
        ;
    }

    protected static function getCreatorTab(bool $canSetCreator, Shout $upgradeNotice): Tabs\Tab
    {
        $creatorUpgradeNotice = clone $upgradeNotice;
        return Tabs\Tab::make('Creator')
            ->icon('heroicon-s-language')
            ->iconPosition(IconPosition::Before)
            ->schema([
                $creatorUpgradeNotice
                    ->visible(!$canSetCreator)
                ,
                static::getTranslateActions('creator', ['target_worldview', 'creator_name', 'creator_url', 'description'])
                    ->visible($canSetCreator),
                Forms\Components\Group::make()
                    ->schema([
                        TextInput::make('creator_name')
                            ->label('Creator Name')
                            ->maxLength(100)
                        ,
                        static::getFormFieldUrl('creator_url', 'Creator URL'),
                        TextInput::make('target_worldview')
                            ->label('Target Worldview')
                            ->maxLength(100)
                        ,
                    ])
                    ->disabled(!$canSetCreator),
                Forms\Components\Group::make()
                    ->schema([
                        static::getFormFieldImage('agents/images'),
                    ])
                    ->disabled(!$canSetCreator),
                static::getFormFieldHtmlEditor('description', 'Description')
                    ->columnSpan(2)
                    ->disabled(!$canSetCreator)
                ,
            ])
            ->columns(2)
        ;
    }

    protected static function getMetaTab(bool $canSetMeta, Shout $upgradeNotice): Tabs\Tab
    {
        $metaUpgradeNotice = clone $upgradeNotice;
        return Tabs\Tab::make('Meta')
            ->icon('heroicon-s-language')
            ->iconPosition(IconPosition::Before)
            ->schema([
                $metaUpgradeNotice
                    ->visible(!$canSetMeta)
                ,
                static::getTranslateActions('meta', ['meta_title', 'meta_description', 'meta_keywords'])
                    ->visible($canSetMeta),
                Forms\Components\Group::make()
                    ->schema([
                        TextInput::make('meta_title')
                            ->label('Meta Title')
                            ->maxLength(100),
                        Textarea::make('meta_keywords')
                            ->label('Meta Keywords')
                            ->rows(5)
                            ->maxLength(500)
                        ,
                        Textarea::make('meta_description')
                            ->label('Meta Description')
                            ->rows(5)
                            ->maxLength(500)
                        ,
                    ])
                    ->disabled(!$canSetMeta),
                Forms\Components\Group::make()
                    ->schema([
                        static::getFormFieldImage('agents/favicons', 'favicon_path', 'Favicon')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'The icon that appears in the browser tab.',
                            )
                        ,
                        static::getFormFieldImage('agents/icons', 'icon_path', 'App Icon')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'The icon that appears when saved to the home screen of a mobile device.',
                            )
                        ,
                        ColorPicker::make('icon_color')
                            ->label('App Icon Background Color')
                            ->disabled(!$canSetMeta)
                        ,
                    ])
                    ->disabled(!$canSetMeta),
            ])
            ->columns(2)
        ;

    }

    protected static function getInstructionsTab(bool $canSetInstructions, Shout $upgradeNotice): Tabs\Tab
    {
        $instructionsUpgradeNotice = clone $upgradeNotice;
        return Tabs\Tab::make('Instructions')
            ->schema([
                $instructionsUpgradeNotice
                    ->visible(!$canSetInstructions)
                ,
                Shout::make('instructions_notice')
                    ->content('These are the Agent\'s baseline operating instructions on what persona to take on and how to respond to prompts.')
                    ->color('primary')
                    ->icon('heroicon-s-information-circle')
                    ->columnSpan(2)
                ,
                Textarea::make('model_system_prompt')
                    ->label(false)
                    ->default(env('AGENT_DEFAULT_PROMPT'))
                    ->rows(20)
                    ->columnSpan(2)
                    ->disabled(!$canSetInstructions)
                ,
                Forms\Components\Select::make('parent')
                    ->label('Parent Agent')
                    ->relationship(
                        name: 'parent',
                        titleAttribute: 'name',
                        modifyQueryUsing: function (Builder $query, ?Agent $record) {
                            $query
                                ->approved()
                                ->where(function (Builder $qry) {
                                    return $qry->where('is_extensible', true)
                                        ->orWhere('is_template', true);
                                });
                            if ($record) {
                                $query->where('id', '!=', $record->id);
                            }
                        }
                    )
                    ->searchable()
                    ->helperText("Instructions from the selected parent Agent will automatically be prepended to this Agent's instructions.")
                    ->columnSpan(2)
                    ->disabled(!$canSetInstructions)
                ,
                static::getFormFieldActive(
                    new HtmlString("
                        Allow 3rd party Agents to prepend this Agent's instructions.
                        Note that the Agent's instructions will not be revealed to 3rd parties unless the 'This Agent is Open Source' option is enabled."
                    ),
                    'is_extensible',
                    'Allow Agent to be Extended'
                )
                    ->disabled(!$canSetInstructions)
                ,
                static::getFormFieldActive(
                    "Allow users to view this Agent's Instructions and Sources for transparency. ",
                    'is_open_source',
                    'This Agent is Open Source'
                )
                    ->disabled(!$canSetInstructions)
                ,
            ])
            ->columns(2)
        ;
    }

    protected static function getConfigurationTab(bool $canEnableRtt, Shout $upgradeNotice): Tabs\Tab
    {
        $translationUpgradeNotice = clone $upgradeNotice;
        $rttCredits = env_int('REALTIME_TRANSLATION_CREDITS');
        return Tabs\Tab::make('Configuration')
            ->schema([
                Select::make('supported_languages')
                    ->label('Supported Languages (leave blank for all)')
                    ->options(fn (Get $get, ?Model $record) => Agent::getAvailableLanguages(
                        $get('auto_translate'),
                        $get('model_id'),
                        $record
                    ))
                    ->multiple()
                    ->live()
                ,
                Select::make('default_language')
                    ->label('Default Language')
                    ->options(fn (Get $get, ?Model $record) => Agent::getSupportedLanguages(
                        $get('supported_languages'),
                        $get('auto_translate'),
                        $get('model_id'),
                        $record
                    ))
                    ->default(env('AGENT_DEFAULT_LANGUAGE'))
                //                    ->required()
                ,
                Select::make('supported_translations')
                    ->label('Supported Translations (leave blank for all)')
                    ->options(config('agent.translations'))
                    ->multiple()
                    ->live()
                ,
                Select::make('default_translation')
                    ->label('Default Translation')
                    ->options(fn (Get $get, ?Model $record) => Agent::getSupportedTranslations($get('supported_translations'), $record))
                    ->default(env('AGENT_DEFAULT_TRANSLATION'))
                //                    ->required()
                ,
                $translationUpgradeNotice
                    ->visible(!$canEnableRtt)
                ,
                static::getFormFieldActive(
                    new HtmlString("
                        <strong>Please note:</strong> Enabling this option will use an additional <strong>{$rttCredits} credits</strong>
                        per response for selected languages, or for <strong>all non-English languages</strong> if none are selected.
                    "),
                    'auto_translate',
                    'Use Real Time Translation for Specified Languages'
                )
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'Expand language support to 192 languages by using a translation service to translate user prompts to English, and Agent responses back into the original prompt language.',
                    )
                    ->live()
                    ->disabled(!$canEnableRtt)
                ,
                Select::make('auto_translate_languages')
                    ->label('Real Time Translation Languages (leave blank for all)')
                    ->options(fn (Get $get, ?Model $record) => Agent::getRealTimeTranslationLanguages(
                        $get('supported_languages'),
                        [env('TRANSLATION_DEFAULT_LANGUAGE')]
                    ))
                    ->multiple()
                    ->disabled(fn (Get $get) => !$get('auto_translate'))
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'Only the languages selected here will use real time translation. English will never use it.',
                    )
                    ->live()
                ,
                Textarea::make('custom_scripts')
                    ->label('Custom Scripts (advanced)')
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'Here you can add analytics scripts and other custom Javascript.',
                    )
                    ->rows(10)
                    ->extraAttributes(['class'=>'font-mono'])
                    ->columnSpan(2)
                ,
            ])
            ->columns(2)
        ;
    }

    protected static function getSourcesTab(Form $form, bool $canEnableTeamCorpus, bool $canSelectCustomSources, bool $canEnableMedia, Shout $upgradeNotice): Tabs\Tab
    {

        $teamCorpusUpgradeNotice = clone $upgradeNotice;
        $customSourcesUpgradeNotice = clone $upgradeNotice;
        $mediaUpgradeNotice = clone $upgradeNotice;
        $hasCustomCorpus = get_current_team()->has_custom_corpus;

        return Tabs\Tab::make('Sources')
            ->schema([

                $teamCorpusUpgradeNotice
                    ->visible(!$canEnableTeamCorpus)
                ,

                Shout::make('sources_notice')
                    ->visible(!$hasCustomCorpus)
                    ->content(new HtmlString('
                        All approved content contributed by the community will be used if no constraints are added here.
                        Every effort is made to validate the authenticity and relevance of materials provided by 3rd party contributors.
                        We are not responsible for copyright infringement on the part of content contributors.
                    '))
                    ->color('primary')
                    ->icon('heroicon-s-information-circle')
                    ->columnSpan(2)
                ,

                Shout::make('custom_corpus_notice')
                    ->visible($hasCustomCorpus)
                    ->content(new HtmlString('
                        Your team is configured to use a custom corpus.
                        This Agent will therefore not have access to community sources.
                        You may only use sources that you upload to this team.
                        Contact us if you would like to revert back to using the community corpus.
                    '))
                    ->color('warning')
                    ->icon('heroicon-s-exclamation-triangle')
                    ->columnSpan(2)
                ,

                static::getFormFieldActive(
                    'Use all Sources contributed by your team, regardless of whether they are in the community corpus or not.',
                    'use_team_corpus',
                    'Use All Team Sources'
                )
                    ->default(true)
                    ->disabled(!$canEnableTeamCorpus || $hasCustomCorpus)
                ,

                static::getFormFieldActive(
                    'Do not use any community Sources as context, other than what is specifically selected below.',
                    'disable_community_corpus',
                    'Disable Community Sources'
                )
                    ->disabled(!$canEnableTeamCorpus || $hasCustomCorpus)
                ,

                Forms\Components\Fieldset::make('sources')
                    ->label('Text Sources to Reference')
                    ->schema([
                        $customSourcesUpgradeNotice
                            ->visible($canEnableTeamCorpus && !$canSelectCustomSources)
                        ,
                        static::getFormFieldCategoryFinder()
                            ->disabled(!$canSelectCustomSources)
                        ,
                        static::getFormFieldCollectionFinder($hasCustomCorpus)
                            ->disabled(!$canSelectCustomSources)
                        ,
                        static::getFormFieldContributorFinder()
                            ->disabled(!$canSelectCustomSources)
                        ,
                        static::getFormFieldSourceFinder($hasCustomCorpus)
                            ->disabled(!$canSelectCustomSources)
                        ,
                        static::getFormFieldClassification()
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Only sources that match this denominational alignment will be included.',
                            )
                            ->disabled(!$canEnableTeamCorpus)
                        ,
                        SpatieTagsInput::make('tags')
                            ->reorderable()
                            ->suggestions(fn () => Tag::query()->team()->pluck('name')->all())
                        ,
                    ])
                ,

                Forms\Components\Fieldset::make('media')
                    ->label('Media to Show')
                    ->schema([
                        $mediaUpgradeNotice
                            ->visible(!$canEnableMedia)
                        ,
                        static::getFormFieldActive(
                            '
                                When enabled, each response will include applicable media (videos and/or podcasts).
                                You may select specific media Collections to draw on, or let any approved media Collection to be used.
                            ',
                            'show_media',
                            'Show Media'
                        )
                            ->live()
                        ,
                        static::getFormFieldMediaCollectionFinder()
                            ->disabled(fn (Get $get) => !$get('show_media'))
                        ,
                    ])
                    ->disabled(!$canEnableMedia)
                    ->visible(Feature::active(AgentMedia::class))
                ,
            ])
            ->columns(2)
        ;

    }

    protected static function getAdvancedTab(array $modelTypes, bool $canSetInstructions, bool $canSetAdvancedConfig, Shout $upgradeNotice): Tabs\Tab
    {

        if (Gatekeeper::isAdmin()) {
            $modelTypes = AgentModelType::values();
        }
        $modelOptions = [];
        foreach ($modelTypes as $type)
        {
            $modelOptions[Str::title($type)] = [];
            foreach (AgentModel::where('type', $type)->where('is_active', true)->orderBy('seq', 'ASC')->get() as $model)
            {
                $modelOptions[Str::title($type)][$model->id] = "{$model->name} ({$model->num_credits})";
            }
        }

        $instructionsUpgradeNotice = clone $upgradeNotice;
        $advancedConfigUpgradeNotice = clone $upgradeNotice;

        $formats = [
            'raw' => [
                'label' => 'OpenAI Compatible Stream',
                'description' => 'Best for streaming use cases as a direct swap-in to replace OpenAI integrations. Use any OpenAI SDK.',
                'icon' => 'heroicon-s-cube-transparent',
            ],
            'text' => [
                'label' => 'Plain Text / Markdown',
                'description' => 'Best for use cases where the raw text is desired in order to process it or display it in a non-web context.',
                'icon' => 'heroicon-s-italic',
            ],
            'html' => [
                'label' => 'HTML',
                'description' => 'Best for directly displaying output on a web page. Any markdown output is automatically converted to HTML.',
                'icon' => 'heroicon-s-code-bracket',
            ],
            'json' => [
                'label' => 'JSON',
                'description' => 'Best for non-streaming use cases or where a structured response is required, such as platform integrations.',
                'icon' => 'heroicon-o-square-3-stack-3d',
            ],
        ];
        $formatOptions = array_combine(array_keys($formats), array_column($formats, 'label'));
        $formatDescriptions = array_combine(array_keys($formats), array_column($formats, 'description'));
        $formatIcons = array_combine(array_keys($formats), array_column($formats, 'icon'));;

        return Tabs\Tab::make('Advanced')
            ->schema([

                Forms\Components\Fieldset::make('model')
                    ->label('Model')
                    ->schema([

                        Shout::make('advanced_notice')
                            ->content('The options below can drastically change how the Agent responds. Make sure to thoroughly test changes to these parameters.')
                            ->color('warning')
                            ->icon('heroicon-o-exclamation-triangle')
                            ->columnSpan(2)
                        ,

                        Select::make('model_id')
                            ->label('Model (# credits)')
                            ->options($modelOptions)
                            ->afterStateUpdated(fn (string $state, Set $set) => $set('model_max_tokens', AgentModel::find($state)->max_tokens))
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'The underlying base model to use. The number of credits used per response is listed in parentheses after the model name.'
                            )
                            ->required()
                            ->default(env_int('AGENT_DEFAULT_MODEL_ID'))
                            ->live()
                            ->columnSpan(2)
                        ,

                        $advancedConfigUpgradeNotice
                            ->visible($canSetInstructions && !$canSetAdvancedConfig)
                        ,
                        Select::make('model_reasoning_effort')
                            ->label('Reasoning Effort')
                            ->options(AgentReasoningEffort::asOptions())
                            ->default(AgentReasoningEffort::MEDIUM)
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'This option is only available on select reasoning models.'
                            )
                            ->disabled(fn (Get $get) => !empty($get('model_id') && !AgentModel::find($get('model_id'))->supports_reasoning_effort))
                            ->hidden(fn (Get $get) => !empty($get('model_id') && !AgentModel::find($get('model_id'))->supports_reasoning_effort))
                        ,
                        Select::make('model_verbosity')
                            ->label('Verbosity')
                            ->options(AgentVerbosity::asOptions())
                            ->default(AgentVerbosity::MEDIUM)
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'This option is only available on select models.'
                            )
                            ->disabled(fn (Get $get) => !empty($get('model_id') && !AgentModel::find($get('model_id'))->supports_verbosity))
                            ->hidden(fn (Get $get) => !empty($get('model_id') && !AgentModel::find($get('model_id'))->supports_verbosity))
                        ,
                        static::getFormFieldSlider(
                            'model_temperature',
                            'Temperature',
                            'The amount of creativity (variability) allowed. Values from from 0.0 to 2.0. Some reasoning models don\'t support this.',
                            0,
                            2,
                            .05
                        )
                            ->default(env_float('AGENT_DEFAULT_TEMPERATURE'))
                            ->disabled(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_temperature))
                            ->hidden(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_temperature))
                        ,
                        static::getFormFieldSlider(
                            'model_top_p',
                            'Top P',
                            'The probability cut-off of the token results to be considered. Lower values mean higher probability. Values from 0.0 to 1.0. Some reasoning models don\'t support this.',
                            0,
                            1,
                            .05
                        )
                            ->default(env_float('AGENT_DEFAULT_TOPP'))
                            ->disabled(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_top_p))
                            ->hidden(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_top_p))
                        ,
                        static::getFormFieldSlider(
                            'model_frequency_penalty',
                            'Frequency Penalty',
                            'How much to penalize new tokens based on their existing frequency in the text so far, to reduce repetition. Values from -2.0 to 2.0. Some reasoning models don\'t support this.',
                            -2,
                            2,
                            .1
                        )
                            ->default(0)
                            ->disabled(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_frequency_penalty))
                            ->hidden(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_frequency_penalty))
                        ,
                        static::getFormFieldSlider(
                            'model_presence_penalty',
                            'Presence Penalty',
                            'How much to penalize new tokens based on whether they appear in the text so far, to increase the likelihood of new topics being mentioned. Values from -2.0 to 2.0. Some reasoning models don\'t support this.',
                            -2,
                            2,
                            .1
                        )
                            ->default(0)
                            ->disabled(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_presence_penalty))
                            ->hidden(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_presence_penalty))
                        ,
                        static::getFormFieldSlider(
                            'model_max_tokens',
                            'Max Output Tokens',
                            'The maximum number of response tokens.',
                            1024,
                            fn (Get $get) => $get('model_id') ?
                                AgentModel::find($get('model_id'))->max_tokens :
                                env_int('AGENT_DEFAULT_MAX_TOKENS')
                            ,
                            1024
                        )
                            ->default(0)
                            ->disabled(!$canSetAdvancedConfig)
                        ,

                    ])
                ,

                Forms\Components\Fieldset::make('api')
                    ->label('API')
                    ->schema([

                        RadioDeck::make('api_response_format')
                            ->label('Default Response Format')
                            ->options($formatOptions)
                            ->descriptions($formatDescriptions)
                            ->icons($formatIcons)
                            ->required()
                            ->default('raw')
                            ->iconSize(IconSize::Large) // Small | Medium | Large | (string - sm | md | lg)
                            ->iconPosition(IconPosition::Before) // Before | After | (string - before | after)
                            ->alignment(Alignment::Center) // Start | Center | End | (string - start | center | end)
                            ->color(Color::hex('#7137ff')) // supports all color custom or not
                            ->columns(2)
                            ->columnSpan(2)
                        ,

                        TextInput::make('max_memories')
                            ->label('Max Context Memories')
                            ->numeric()
                            ->step(1)
                            ->minValue(0)
                            ->maxValue(10)
                            ->default(env('AGENT_DEFAULT_MEMORIES'))
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'The maximum number of previous prompt/response exchanges to provide the model as context. Values 0 to 10.'
                            )
                        ,

                        static::getFormFieldActive(
                            '
                                Remove Markdown characters from plain text responses.
                                Useful for voice integrations or elsewhere that Markdown won\'t work well.
                            ',
                            'strip_markdown',
                            'Strip Markdown from Response Text'
                        ),

                        static::getFormFieldActive(
                            '
                                Prevent API requests from overriding this Agent\'s system prompt.
                                Useful for integrations that require an undesired system prompt override.
                            ',
                            'lock_system_prompt',
                            'Lock System Prompt'
                        ),

                    ])
                ,

                Forms\Components\Fieldset::make('context')
                    ->label('Context')
                    ->schema([

                        $instructionsUpgradeNotice
                            ->visible(!$canSetInstructions)
                        ,
                        Textarea::make('model_context_prompt')
                            ->label('Context Instructions')
                            ->default(env('AGENT_CONTEXT_PROMPT'))
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'These instructions orient the Agent to what language and Bible translation to use. They also frame how it is to use source materials in its response.'
                            )
                            ->helperText(view('components.help.agent-context-prompt'))
                            ->maxLength(1000)
                            ->rows(10)
                            ->columnSpan(2)
                            ->disabled(!$canSetInstructions)
                        ,

                    ])
                ,

            ])
            ->columns(2)
        ;
    }

    protected static function getPersonaTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Persona')
            ->icon('heroicon-s-language')
            ->iconPosition(IconPosition::Before)
            ->schema([
                static::getTranslateActions('persona', ['persona_name', 'persona_tagline', 'persona_description']),
                Forms\Components\Group::make()
                    ->schema([
                        static::getFormFieldActive(
                            'Advertise this Agent publicly on Apologist Agent Marketplace.',
                            'marketplace_active',
                            new HtmlString("List on <a href='' target='_blank' class='hover:underline text-gray-900 dark:text-white font-bold'>Apologist Agent Marketplace ↗</a>")
                        ),
                        TextInput::make('persona_name')
                            ->label('Name'),
                        TextInput::make('persona_tagline')
                            ->label('Tagline'),
                    ])
                    ->columnSpan(['default' => 2, 'md' => 1]),
                Forms\Components\Group::make()
                    ->schema([
                        static::getFormFieldAvatar('agents/personas', 'Avatar', 'persona_avatar_path'),
                    ])
                    ->columnSpan(['default' => 2, 'md' => 1]),
                static::getFormFieldBlockRichText('persona_description')
                    ->label('Description'),
            ])
            ->columns(2);
    }

    protected static function getAdminTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Admin')
            ->schema([

                static::getFormFieldActive(
                    'Show this Agent in the worldview selector.',
                    'is_selectable',
                    'Selectable as Worldview'
                ),
                static::getFormFieldActive(
                    'Allow this Agent to be selected as a 1st party base template.',
                    'is_template',
                    'Selectable as Base Template'
                ),
                static::getFormFieldTeam(),
                static::getFormFieldOwner(),
                static::getFormFieldActive(
                    'Enable semantic search via this Agent\'s API.',
                    'has_semantic_search',
                    'Enable Semantic Search'
                ),
                static::getFormFieldActive(
                    'Do not charge the team for use of this Agent.',
                    'is_nonbillable',
                    'Do Not Bill'
                )
                    ->default(fn (Get $get) => Team::find($get('team'))?->is_nonbillable),
                static::getFormFieldActive(
                    'Show debugging logs.',
                    'debug',
                    'Debug'
                ),
            ])
            ->columns(2)
        ;
    }

    protected static function getTranslateActions(string $tab, array $fields): Forms\Components\Actions
    {

        $defaultLanguage = config('agent.languages')[env('AGENT_DEFAULT_LANGUAGE')];

        return Forms\Components\Actions::make([

            Forms\Components\Actions\Action::make("{$tab}_translate")
                ->extraAttributes(['id' => btn_id("translate-{$tab}")])
                ->label('Translate All')
                ->icon('heroicon-s-language')
                ->outlined()
                ->action(function (Agent $record, $livewire, Get $get, Set $set) use ($fields) {
                    $lang = $livewire->getActiveActionsLocale();
                    foreach ($fields as $field) {
                        $set(
                            $field,
                            $livewire->translateFormField(
                                $lang,
                                $get($field),
                                $record->getTranslation($field, $lang)
                            )
                        );
                    }
                })
            ,

            Forms\Components\Actions\Action::make("{$tab}_reset_translation")
                ->extraAttributes(['id' => btn_id("reset-translation-{$tab}")])
                ->label("Reset All to {$defaultLanguage}")
                ->icon('heroicon-s-arrow-path')
                ->outlined()
                ->action(function (Agent $record, $livewire, Get $get, Set $set) use ($fields) {
                    foreach ($fields as $field) {
                        $set(
                            $field,
                            $record->getTranslation($field, env('AGENT_DEFAULT_LANGUAGE'))
                        );
                    }
                })
            ,

        ])
            ->columnSpan(2)
            ->alignment(Alignment::Center)
        ;

    }

    protected static function getFormFieldSourceFinder(): RecordFinder
    {
        return static::getFormFieldRecordFinder(
            'sources',
            Source::class,
            Source::where('agent_active', true)->whereNotNull('agent_indexed_at'),
            fn () => [
                static::getTableColumnName(),
                static::getTableColumnType(),
                Tables\Columns\TextColumn::make('published_on')
                    ->label('Published')
                    ->date()
                    ->sortable(),
            ],
            fn () => [
                static::getTableFilterMyTeam(),
                static::getTableFilterType(SourceType::asOptions()),
                static::getTableFilterLanguage(),
                static::getTableFilterRelation('categories'),
                static::getTableFilterRelation('collections')
                    ->getOptionLabelFromRecordUsing(function (Model $record) {
                        $type = Labeller::mutate($record->type->value);
                        return "{$record->name} [{$type}]";
                    }),
                static::getTableFilterContributors(),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
            ],
            function (Source $source) {
                $type = Labeller::mutate($source->type->value);
                return new HtmlString("<span class='font-bold'>{$source->name}</span> [{$type}]");
            },
            false,
            'Individual Sources',
            'Only the specific Sources selected will be included.'
        );
    }

    protected static function getFormFieldCollectionFinder(): RecordFinder
    {
        return static::getFormFieldRecordFinder(
            'collections',
            Collection::class,
            Collection::query(),
            fn () => [
                static::getTableColumnName(),
                static::getTableColumnType(),
            ],
            fn () => [
                static::getTableFilterMyTeam(),
                static::getTableFilterType(CollectionType::asOptions()),
                static::getTableFilterLanguage(),
                static::getTableFilterRelation('categories'),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
            ],
            function (Collection $collection) {
                $type = Labeller::mutate($collection->type->value);
                return new HtmlString("<span class='font-bold'>{$collection->name}</span> [{$type}]");
            },
        );
    }

    protected static function getFormFieldContributorFinder(): RecordFinder
    {
        return static::getFormFieldRecordFinder(
            'contributors',
            Contributor::class,
            Contributor::query(),
            fn () => [
                static::getTableColumnName(),
            ],
            fn () => [
                static::getTableFilterMyTeam(),
            ]
        );
    }

    protected static function getFormFieldCategoryFinder(): RecordFinder
    {
        return static::getFormFieldRecordFinder(
            'categories',
            Category::class,
            Category::active(),
            fn () => [
                static::getTableColumnName(),
                static::getTableColumnParent(),
            ],
            fn () => [
                static::getTableFilterMyTeam(),
            ],
            function (Category $category) {
                return new HtmlString(
                    "<span class='font-bold'>{$category->name}</span>".
                    ($category->parent ? " [{$category->parent->name}]" : '')
                );
            },
            true
        );
    }

    protected static function getFormFieldMediaCollectionFinder(): RecordFinder
    {
        return static::getFormFieldRecordFinder(
            'mediaCollections',
            Collection::class,
            Collection::whereIn('type', ['channel', 'podcast']),
            fn () => [
                static::getTableColumnName(),
                static::getTableColumnType(),
            ],
            fn () => [
                static::getTableFilterMyTeam(),
                static::getTableFilterType(Arr::only(CollectionType::asOptions(), ['channel', 'podcast'])),
                static::getTableFilterLanguage(),
                static::getTableFilterRelation('categories'),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
            ],
            function (Collection $collection) {
                $type = Labeller::mutate($collection->type->value);
                return new HtmlString("<span class='font-bold'>{$collection->name}</span> [{$type}]");
            },
            false,
            'Media Collections',
            'Media added to selected Collections will automatically be included.',
            'Select Media Collections'
        );
    }

    protected static function getFormFieldRecordFinder(
        string $relation,
        string $modelClass,
        Builder $baseQuery,
        Closure $columnsClosure,
        Closure $filtersClosure,
        Closure $recordLabelClosure = null,
        bool $public = false,
        string $label = null,
        string $description = null,
        string $buttonLabel = null
    ): RecordFinder {

        $modelLabel = Labeller::model($modelClass);
        $modelLabelPlural = Str::plural($modelLabel);

        if (is_null($label)) {
            $label = $modelLabelPlural;
        }
        if (is_null($description)) {
            $description = "Sources added to selected {$modelLabelPlural} will automatically be included.";
        }
        if (is_null($buttonLabel)) {
            $buttonLabel = "Select {$modelLabelPlural}";
        }

        return RecordFinder::make($relation)
            ->label($label)
            ->helperText($description)
            ->relationship($relation)
            ->multiple()
            ->standalone()
            ->query(function () use ($baseQuery, $public) {
                if (!$public) {
                    $baseQuery->privateOrContributed(get_current_team_id());
                }
                $baseQuery->orderBy('name', 'asc');
                return $baseQuery;
            })
            ->modifyTableUsing(function (Tables\Table $table) use ($columnsClosure, $filtersClosure) {
                $table->defaultPaginationPageOption(static::FINDER_PAGE_SIZE)
                    ->defaultSort('name', 'asc')
                    ->description(false)
                    ->paginated()
                    ->paginationPageOptions([static::FINDER_PAGE_SIZE])
                    ->columns($columnsClosure())
                    ->filters($filtersClosure())
                    ->filtersFormColumns(2)
                    ->actions([
                        static::decorateViewReferenceAction(Tables\Actions\Action::make('Reference')),
                    ])
                ;
            })
            ->getRecordLabelFromRecordUsing($recordLabelClosure)
            ->listWithLineBreaks()
            ->expandableLimitedList()
            ->bulleted()
            ->placeholder(false)
            ->openModalActionLabel($buttonLabel)
            ->openModalActionIcon(false)
            ->openModalActionColor('gray')
            ->openModalActionModalHeading("Select {$modelLabelPlural} to Include in Agent")
            ->openModalActionModalWidth(MaxWidth::SevenExtraLarge)
            ->openModalActionModalSubmitActionLabel("Confirm {$modelLabel} Selection")
        ;

    }

    protected static function getTableFilterMyTeam(): TernaryFilter
    {
        return TernaryFilter::make('team_id')
            ->label('Contributing Team')
            ->placeholder('')
            ->trueLabel('My Team')
            ->falseLabel('Other Teams')
            ->queries(
                true: fn (Builder $query) => $query->where('team_id', get_current_team_id()),
                false: fn (Builder $query) => $query->where('team_id', '!=', get_current_team_id()),
                blank: fn (Builder $query) => $query,
            )
            ;
    }

    protected static function getFormActionsAgent(): Forms\Components\Actions
    {
        $shouldIndex = fn ($record) => ! is_null($record) && ! $record->is_locked && $record->shouldIndexRag();
        $sourceIdClosure = fn ($record) => $record->id;

        return Forms\Components\Actions::make([
            static::getAgentDemoAction(
                '
                    Launch an instance of Agent that draws only on context from this Source. This will give you a good
                    idea of what it will look like if/when Agent uses your Source in its response.
                ',
                $shouldIndex,
                $sourceIdClosure
            ),
            Forms\Components\Actions\Action::make('learn')
                ->extraAttributes(['id' => btn_id('learn')])
                ->label('Learn More ↗')
                ->icon('heroicon-s-book-open')
                ->color('gray')
                ->url(env('AGENT_LEARN_URL'))
                ->openUrlInNewTab(),
            Forms\Components\Actions\Action::make('reindex')
                ->extraAttributes(['id' => btn_id('reindex')])
                ->label('Reindex')
                ->tooltip('Manually reindex this source on Apologist Agent if it has become out of sync.')
                ->color('gray')
                ->outlined()
                ->icon('heroicon-s-circle-stack')
                ->visible($shouldIndex)
                ->action(function (?\App\Models\Model $record) {
                    dispatch(function () use ($record) {
                        $record->reindexRagDocument();
                    })->catch(function (Throwable $e) {
                        // This job has failed...
                    });
                    Notification::make()
                        ->title('Submitted for Reindexing')
                        ->success()
                        ->send();

                    return true;
                })
                ->requiresConfirmation()
                ->modalIcon('heroicon-s-circle-stack'),
        ])
            ->alignRight();
    }

    /**
     * @return string[]
     */
    public static function getRelations(): array
    {
        return [
            AgentQuestionsRelationManager::class,
            AgentIntegrationsRelationManager::class,
            AgentTokensRelationManager::class,
            HistoryRelationManager::class,
        ];
    }

}
