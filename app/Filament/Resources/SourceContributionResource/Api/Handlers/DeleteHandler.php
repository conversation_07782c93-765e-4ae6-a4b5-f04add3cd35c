<?php

namespace App\Filament\Resources\SourceContributionResource\Api\Handlers;

use App\Filament\Api\Handlers\DeleteHandler as BaseDeleteHandler;
use App\Filament\Resources\SourceContributionResource;
use App\Models\Source;

class DeleteHandler extends BaseDeleteHandler
{
    public static ?string $resource = SourceContributionResource::class;

    protected static ?string $parentTeamModel = Source::class;
}
