<?php

namespace App\Filament\Resources\SourceContributionResource\Api\Handlers;

use App\Filament\Api\Handlers\CreateHandler as BaseCreateHandler;
use App\Filament\Resources\SourceContributionResource;
use App\Models\Source;

class CreateHandler extends BaseCreateHandler
{
    public static ?string $resource = SourceContributionResource::class;

    protected static ?string $parentTeamModel = Source::class;
}
