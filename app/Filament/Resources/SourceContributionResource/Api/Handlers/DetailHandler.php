<?php

namespace App\Filament\Resources\SourceContributionResource\Api\Handlers;

use App\Filament\Api\Handlers\DetailHandler as BaseDetailHandler;
use App\Filament\Resources\SourceContributionResource;
use App\Models\Source;

class DetailHandler extends BaseDetailHandler
{
    public static ?string $resource = SourceContributionResource::class;

    protected static ?string $parentTeamModel = Source::class;
}
