<?php

namespace App\Filament\Resources\SourceContributionResource\Api\Handlers;

use App\Filament\Api\Handlers\UpdateHandler as BaseUpdateHandler;
use App\Filament\Resources\SourceContributionResource;
use App\Models\Source;

class UpdateHandler extends BaseUpdateHandler
{
    public static ?string $resource = SourceContributionResource::class;

    protected static ?string $parentTeamModel = Source::class;
}
