<?php

namespace App\Filament\Resources\SourceContributionResource\Api\Handlers;

use App\Filament\Api\Handlers\PaginationHandler as BasePaginationHandler;
use App\Filament\Resources\SourceContributionResource;
use App\Models\Source;

class PaginationHandler extends BasePaginationHandler
{
    public static ?string $resource = SourceContributionResource::class;

    protected static ?string $parentTeamModel = Source::class;
}
