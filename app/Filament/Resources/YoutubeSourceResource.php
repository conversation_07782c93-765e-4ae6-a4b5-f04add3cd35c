<?php

namespace App\Filament\Resources;

use App\Enums\CollectionType;
use App\Enums\SourceType;

class YoutubeSourceResource extends SourceResource
{
    protected static ?string $navigationIcon = 'heroicon-s-video-camera';

    protected static ?string $sourceType = SourceType::YOUTUBE->value;

    protected static ?array $allowedCollectionTypes = [
        CollectionType::LIST->value,
        CollectionType::CHANNEL->value,
    ];

    protected static ?string $modelLabel = 'Streaming Video';

    protected static ?string $pluralModelLabel = 'Streaming Videos';

    protected static ?string $navigationLabel = 'Streaming Videos';

    protected static ?string $slug = 'videos';

    protected static string $createLabel = 'add';

    public static function getPermissionPrefixes(): array
    {
        return [];
    }
}
