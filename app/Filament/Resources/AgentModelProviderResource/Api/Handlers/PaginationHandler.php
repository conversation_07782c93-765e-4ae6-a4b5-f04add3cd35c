<?php

namespace App\Filament\Resources\AgentModelProviderResource\Api\Handlers;

use App\Filament\Api\Handlers\PaginationHandler as BasePaginationHandler;
use App\Filament\Resources\AgentModelProviderResource;

class PaginationHandler extends BasePaginationHandler
{
    public static ?string $resource = AgentModelProviderResource::class;

    public static bool $public = true;

    protected static bool $restrictToTeams = false;
}
