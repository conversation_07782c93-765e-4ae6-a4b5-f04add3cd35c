<?php

namespace App\Filament\Resources\AgentModelProviderResource\Api\Handlers;

use App\Filament\Api\Handlers\DetailHandler as BaseDetailHandler;
use App\Filament\Resources\AgentModelProviderResource;

class DetailHandler extends BaseDetailHandler
{
    public static ?string $resource = AgentModelProviderResource::class;

    public static bool $public = true;

    protected static bool $restrictToTeams = false;
}
