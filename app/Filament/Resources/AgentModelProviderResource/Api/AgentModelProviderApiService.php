<?php

namespace App\Filament\Resources\AgentModelProviderResource\Api;

use App\Filament\Api\ApiService;
use App\Filament\Resources\AgentModelProviderResource;

class AgentModelProviderApiService extends ApiService
{
    protected static ?string $resource = AgentModelProviderResource::class;

    /**
     * @return string[]
     */
    protected static function getActions(): array
    {
        return [
            'Pagination',
            'Detail',
        ];
    }
}
