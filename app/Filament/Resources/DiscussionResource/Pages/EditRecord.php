<?php

namespace App\Filament\Resources\DiscussionResource\Pages;

use App\Filament\Resources\DiscussionResource;
use App\Filament\Templates\EditRecord as BaseEditRecord;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;

class EditRecord extends BaseEditRecord
{
    protected static string $resource = DiscussionResource::class;

    /**
     * @return array|Action[]|ActionGroup[]
     */
    protected function getHeaderActions(): array
    {
        return $this->getModerationHeaderActions();
    }
}
