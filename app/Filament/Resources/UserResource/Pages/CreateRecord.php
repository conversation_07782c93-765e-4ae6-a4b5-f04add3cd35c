<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use App\Filament\Templates\CreateRecord as BaseCreateRecord;
use App\Filament\Traits\HasRestrictedAccess;
use App\Services\Gatekeeper;

class CreateRecord extends BaseCreateRecord
{
    use HasRestrictedAccess;

    protected static string $resource = UserResource::class;

    protected function afterCreate(): void
    {
        $user = $this->getRecord();
        $user->memberships()->create(['team_id' => $user->current_team_id, 'role_id' => Gatekeeper::getMemberRoleId()]);
    }
}
