<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use App\Filament\Templates\EditRecord as BaseEditRecord;
use App\Filament\Traits\HasRestrictedAccess;
use App\Services\Gatekeeper;
use Filament\Actions;
use STS\FilamentImpersonate\Pages\Actions\Impersonate;

class EditRecord extends BaseEditRecord
{
    use HasRestrictedAccess;

    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return array_merge(
            $this->getRecord()->currentTeam ?
            [
                Impersonate::make()
                    ->record($this->getRecord())
                    ->redirectTo(route('filament.app.pages.dashboard', ['tenant' => $this->getRecord()->currentTeam?->id]))
                    ->visible(fn ($record) => Gatekeeper::userCan('impersonate', $record)),
            ] : [],
            parent::getHeaderActions()
        );
    }

    protected function getFormActionArchive(): Actions\DeleteAction
    {
        return $this->getFormActionSuspend();
    }

    protected function getFormActionRestore(): Actions\RestoreAction
    {
        return $this->getFormActionReactivate();
    }
}
