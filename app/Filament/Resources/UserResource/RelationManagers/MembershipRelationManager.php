<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use App\Filament\RelationManagers\TeamRelationManager;
use App\Filament\Traits\HasActions;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFields;
use App\Filament\Traits\HasFilters;
use App\Models\Role;
use App\Models\Team;
use App\Services\Gatekeeper;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MembershipRelationManager extends TeamRelationManager
{
    use HasActions;
    use HasColumns;
    use HasFields;
    use HasFilters;

    protected static string $relationship = 'memberships';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return 'Roles';
    }

    public function form(Form $form): Form
    {

        $teamRoleIds = Role::where('is_team', true)->pluck('id')->toArray();
        $rolesQry = Role::orderBy('seq', 'asc');
        if (! Gatekeeper::isSuperadmin()) {
            $rolesQry->whereIn('id', $teamRoleIds);
        }

        return $form
            ->schema([
                Forms\Components\Select::make('role_id')
                    ->label('Role')
                    ->options($rolesQry->pluck('name', 'id')->toArray())
                    ->live()
                ,
                static::getFormFieldTeam()
                    ->required(fn (Get $get) => in_array($get('role_id'), $teamRoleIds))
                    ->disabled(fn (Get $get) => ! in_array($get('role_id'), $teamRoleIds))
                    ->default(null)
                    ->getSearchResultsUsing(function (string $search, RelationManager $livewire): array {
                        return Team::where('name', 'like', "%{$search}%")
                            ->whereNot('id', $livewire->getOwnerRecord()->model_id)
                            ->whereNot('id', $livewire->getOwnerRecord()->user_id)
                            ->whereNotIn('id', $livewire->getOwnerRecord()->memberships->pluck('id'))
                            ->limit(50)
                            ->pluck('name', 'id')
                            ->toArray();
                    })
                    ->live()
                ,
            ])
        ;
    }

    /**
     * @throws \Exception
     */
    public function table(Table $table): Table
    {
        $ownerRecord = $this->getOwnerRecord();
        $restrictEdit = restrict_editing($ownerRecord);
        $restrictEditClosure = function ($record) use ($ownerRecord) {
            return restrict_editing($ownerRecord) || ($record->model_id === $record->team?->user_id);
        };

        return $table
            ->recordTitleAttribute('model.name')
            ->columns([
                static::getTableColumnRole(),
                static::getTableColumnTeam(),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterRelation('role'),
                static::getTableFilterActive(),
                static::getTableFilterSuspended(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make('add')
                    ->extraAttributes(['id' => btn_id('add-role')])
                    ->label('Add Role')
                    ->visible(Gatekeeper::userCanAdminister(static::getOwnerRecord()))
                    ->modalHeading('Add Role')
                    ->modalSubmitActionLabel('Add Role')
                    ->extraModalFooterActions(function ($action): array {
                        return [
                            $action->makeModalSubmitAction('createAnother', ['another' => true])
                                ->label('Add and Add Another'),
                        ];
                    })
                    ->successNotificationTitle('Role Added')
                    ->hidden($restrictEdit),
            ])
            ->actions([
                static::getTableActionEdit()
                    ->hidden($restrictEditClosure)
                    ->modalHeading('Edit Role'),
                static::getTableActionSuspend()
                    ->hidden($restrictEditClosure),
                static::getTableActionReactivate()
                    ->hidden($restrictEditClosure),
                static::getTableActionDelete()
                    ->hidden($restrictEditClosure),
            ])
            ->modifyQueryUsing(function (Builder $query) {
                $query->withoutGlobalScopes([
                    SoftDeletingScope::class,
                ]);
            })
        ;
    }
}
