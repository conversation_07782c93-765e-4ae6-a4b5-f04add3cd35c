<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use App\Filament\Traits\HasColumns;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Mail;
use RickDBCN\FilamentEmail\Mail\ResendMail;

class EmailLogRelationManager extends RelationManager
{
    use HasColumns;

    protected static string $relationship = 'emailLogs';

    protected static ?string $recordTitleAttribute = 'subject';

    public function table(Table $table): Table
    {
        return $table
            ->defaultSort(
                config('filament-email.resource.default_sort_column'),
                config('filament-email.resource.default_sort_direction')
            )
            ->columns([
                TextColumn::make('subject')
                    ->label(__('Subject'))
                    ->limit(50),
                static::getTableColumnSince('created_at', 'Time Sent')
                    ->visibleFrom('md'),

            ])
            ->actions([
                Tables\Actions\ViewAction::make('view')
                    ->extraAttributes(['class' => btn_id('view')])
                    ->label('')
                    ->icon(null),
                Action::make('preview')
                    ->extraAttributes(['class' => btn_id('preview')])
                    ->label(__('Preview'))
                    ->icon('heroicon-m-eye')
                    ->extraAttributes(['style' => 'h-41'])
                    ->modalFooterActions(
                        fn ($action): array => [
                            $action->getModalCancelAction(),
                        ])
                    ->fillForm(function ($record) {
                        $body = $record->html_body;

                        return [
                            'html_body' => $body,
                        ];
                    })
                    ->form([
                        ViewField::make('html_body')->hiddenLabel()
                            ->view('filament-email::filament-email.emails.html')->view('filament-email::HtmlEmailView'),
                    ]),
                Action::make('resend')
                    ->extraAttributes(['class' => btn_id('resend')])
                    ->label(__('Resend'))
                    ->icon('heroicon-s-envelope')
                    ->color('success')
                    ->action(function ($record) {
                        try {
                            Mail::to($record->to)
                                ->cc($record->cc)
                                ->bcc($record->bcc)
                                ->send(new ResendMail($record));
                            Notification::make()
                                ->title(__('E-mail has been successfully sent'))
                                ->success()
                                ->duration(5000)
                                ->send();
                        } catch (\Exception) {
                            Notification::make()
                                ->title(__('Something went wrong'))
                                ->danger()
                                ->duration(5000)
                                ->send();
                        }
                    }),
            ])
            ->recordAction('view');
    }

    protected function getTableRecordAction(): ?string
    {
        return 'view';
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Fieldset::make('Envelope')
                    ->label('')
                    ->schema([
                        TextInput::make('created_at')
                            ->label(__('Created at')),
                        TextInput::make('from')
                            ->label(__('From')),
                        Textinput::make('to')
                            ->label(__('To')),
                        TextInput::make('cc')
                            ->label(__('CC')),
                        TextInput::make('subject')
                            ->label(__('Subject'))
                            ->columnSpan(2),
                    ])->columns(3),
                Tabs::make('Content')->tabs([
                    Tabs\Tab::make('HTML')
                        ->schema([
                            ViewField::make('html_body')
                                ->view('filament-email::filament-email.emails.html')
                                ->view('filament-email::HtmlEmailView'),
                        ]),
                    Tabs\Tab::make('Text')
                        ->schema([
                            Textarea::make('text_body'),
                        ]),
                    Tabs\Tab::make('Raw')
                        ->schema([
                            Textarea::make('raw_body'),
                        ]),
                    Tabs\Tab::make('Debug info')
                        ->schema([
                            Textarea::make('sent_debug_info'),
                        ]),
                ])->columnSpan(2),
            ]);
    }

    protected function canCreate(): bool
    {
        return false;
    }

    protected function canDelete(Model $record): bool
    {
        return false;
    }
}
