<?php

namespace App\Filament\Resources\ChannelCollectionResource\Pages;

use App\Filament\Resources\ChannelCollectionResource;
use App\Filament\Resources\CollectionResource\Pages\ListRecords as BaseListRecords;
use App\Filament\Resources\YoutubeSourceResource;
use Filament\Actions\Action;

class ListRecords extends BaseListRecords
{
    protected static string $resource = ChannelCollectionResource::class;

    protected function getHeaderActions(): array
    {
        return array_merge(
            [
                Action::make('videos')
                    ->extraAttributes(['id' => btn_id('videos')])
                    ->label('Back to Videos')
                    ->icon('heroicon-o-arrow-left-circle')
                    ->outlined()
                    ->url(YoutubeSourceResource::getUrl()),
            ],
            parent::getHeaderActions()
        );
    }
}
