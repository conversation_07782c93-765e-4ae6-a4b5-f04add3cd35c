<?php

namespace App\Filament\Resources\RssCollectionResource\Pages;

use App\Filament\Resources\CollectionResource\Pages\ListRecords as BaseListRecords;
use App\Filament\Resources\RssCollectionResource;
use App\Filament\Resources\UrlSourceResource;
use Filament\Actions\Action;

class ListRecords extends BaseListRecords
{
    protected static string $resource = RssCollectionResource::class;

    protected function getHeaderActions(): array
    {
        return array_merge(
            [
                Action::make('urls')
                    ->extraAttributes(['id' => btn_id('urls')])
                    ->label('Back to Web Pages')
                    ->icon('heroicon-o-arrow-left-circle')
                    ->outlined()
                    ->url(UrlSourceResource::getUrl()),
            ],
            parent::getHeaderActions()
        );
    }
}
