<?php

namespace App\Filament\Resources;

use App\Filament\RelationManagers\HistoryRelationManager;
use App\Filament\Resources\CategoryResource\Api\Transformers\CategoryTransformer;
use App\Filament\Resources\Resource as BaseResource;
use App\Models\Category;
use Exception;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CategoryResource extends BaseResource
{
    protected static ?string $model = Category::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-s-folder';

    protected static bool $isScopedToTenant = false;

    protected static string $createLabel = 'create';

    public static function form(Form $form): Form
    {
        $visibilityClosure = fn ($record) => ! is_null($record) && ($record->sources()->count() > 0);
        $sourceIdClosure = fn ($record) => implode(',', $record->sources()->pluck('sources.id')->toArray());

        return $form
            ->schema([
                Fieldset::make('Category Information')
                    ->schema([
                        static::getFormFieldName()
                            ->columnSpan(['default' => 2, 'md' => 1]),
                        static::getFormFieldSlug()
                            ->columnSpan(['default' => 2, 'md' => 1]),
                        static::getFormFieldTagline()
                            ->columnSpan(['default' => 2, 'md' => 1]),
                        static::getFormFieldParent()
                            ->columnSpan(['default' => 2, 'md' => 1]),
                        static::getFormFieldActive('Only active categories will be returned via the API.')
                            ->columnSpan(['default' => 2, 'md' => 1]),
                        static::getFormFieldInlineRichText()
                            ->columnSpan(2),
                        //                        static::getFormFieldSources(),
                    ]),
            ]);
    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                $query->tree();
            })
            ->columns([
                static::getTableColumnNestedName(),
                static::getTableColumnParent(),
                static::getTableColumnActive(),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->defaultSort('name_nested')
            ->filters([
                static::getTableFilterParent(),
                static::getTableFilterArchived(),
            ])
            ->actions([
                static::getTableActionEditRow(),
            ])
            ->bulkActions(static::getTableBulkActions())
            ->paginated(static::$pageOptions)
            ->defaultPaginationPageOption(static::$defaultPageOption);
    }

    /**
     * @return string[]
     */
    public static function getRelations(): array
    {
        return [
            HistoryRelationManager::class,
        ];
    }

    public static function getApiTransformer(): string
    {
        return CategoryTransformer::class;
    }
}
