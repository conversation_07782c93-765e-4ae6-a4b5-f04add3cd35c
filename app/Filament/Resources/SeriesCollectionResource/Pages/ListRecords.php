<?php

namespace App\Filament\Resources\SeriesCollectionResource\Pages;

use App\Filament\Resources\CollectionResource\Pages\ListRecords as BaseListRecords;
use App\Filament\Resources\SeriesCollectionResource;

class ListRecords extends BaseListRecords
{
    protected static string $resource = SeriesCollectionResource::class;

    protected function getHeaderActions(): array
    {
        return array_merge(
            [
                static::getBooksHeaderAction(),
                static::getArticlesHeaderAction(),
                static::getMediaHeaderAction(),
            ],
            parent::getHeaderActions()
        );
    }
}
