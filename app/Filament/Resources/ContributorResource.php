<?php

namespace App\Filament\Resources;

use App\Enums\ContributorTitle;
use App\Filament\RelationManagers\HistoryRelationManager;
use App\Filament\Resources\ContributorResource\Api\Transformers\ContributorTransformer;
use App\Filament\Resources\ContributorResource\RelationManagers\ContributionsRelationManager;
use App\Filament\Resources\Resource as BaseResource;
use App\Filament\Traits\CanBeAdministered;
use App\Filament\Traits\CanBeLocked;
use App\Filament\Traits\CanBeModerated;
use App\Filament\Traits\CanBePromoted;
use App\Models\Contributor;
use App\Services\Gatekeeper;
use App\Services\Labeller;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Closure;
use Exception;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class ContributorResource extends BaseResource implements HasShieldPermissions
{
    use CanBeAdministered;
    use CanBeLocked;
    use CanBeModerated;
    use CanBePromoted;

    protected static ?string $model = Contributor::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-s-academic-cap';

    protected static ?string $pluralModelLabel = 'Contributors';

    protected static array $customPermissions = [
        'merge',
    ];

    public static function form(Form $form): Form
    {
        $restrictEditingClosure = fn ($record) => restrict_editing($record);
        $restrictPromotionClosure = fn ($record) => ! Gatekeeper::userCan('promote', Contributor::class, $record);


        return $form
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        static::getSummaryTab($restrictEditingClosure),
                        static::getListingTab($restrictEditingClosure, $restrictPromotionClosure),
                        static::getCategoriesTab($restrictEditingClosure),
                        static::getAdminTab($restrictEditingClosure),
                    ])
                    ->persistTabInQueryString()
                    ->id('contributor'),
            ])
            ->columns(1);
    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                static::$isScopedToTenant = false;
                if (! Gatekeeper::userCanAdminister(static::getModel())) {
                    $query->whereBelongsTo(get_current_team());
                }
            })
            ->columns([
                static::getTableColumnName()
                    ->searchable(['first_name', 'last_name', 'title', 'suffix']),
                static::getTableColumnCount('authored', '# Authorships')
                    ->visibleFrom('md'),
                static::getTableColumnCount('nonauthored', '# Other')
                    ->visibleFrom('md'),
                static::getTableColumnActive('social_active', 'Social Listing')
                    ->visibleFrom('md'),
                static::getTableColumnApprovalStatus()
                    ->visibleFrom('md'),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
                static::getTableColumnFeaturedSequence(),
            ])
            ->defaultSort(fn ($query) => $query
                ->orderBy('last_name', 'asc')
                ->orderBy('first_name', 'asc')
                ->orderBy('title', 'asc')
                ->orderBy('suffix', 'asc')
            )
            ->filters([
                static::getTableFilterStatus(),
                static::getTableFilterBoolean('social_active', 'Apologist Social'),
                static::getTableFilterFeatured(),
                static::getTableFilterArchived(),
                static::getTableFilterTeam(),
                static::getTableFilterOwner(),
            ])
            ->actions([
                static::decorateLockAction(Action::make('Lock')),
                static::decorateUnlockAction(Action::make('Unlock')),
                static::decorateViewListingAction(Tables\Actions\Action::make('Preview')),
                static::decorateViewReferenceAction(Tables\Actions\Action::make('Reference')),
                static::getTableActionEditRow(),
            ])
            ->bulkActions(static::getTableBulkActions())
            ->paginated(static::$pageOptions)
            ->defaultPaginationPageOption(static::$defaultPageOption);
    }

    protected static function getSummaryTab(Closure $restrictEditingClosure): Tabs\Tab
    {
        return Tabs\Tab::make('Summary')
            ->schema([
                static::getFormNoticePendingApproval('Contributor'),
                static::getFormFieldFirstName(),
                static::getFormFieldLastName(),
                static::getFormFieldTitle(),
                static::getFormFieldSuffix(),
                static::getFormFieldPastYear('born_in', 'Year of Birth'),
                static::getFormFieldPastYear('died_in', 'Year of Death'),
                static::getFormFieldRelation('merged_contributor_id', 'mergedContributor', 'Merged To Contributor')
                    ->visible(fn ($record) => $record && $record->mergedContributor),
            ])
            ->disabled($restrictEditingClosure)
            ->columns(2);
    }

    /**
     * @return Tab
     */
    protected static function getListingTab(Closure $restrictEditingClosure, Closure $restrictPromotionClosure): Tabs\Tab
    {
        return Tabs\Tab::make('Listing')
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        static::getFormFieldSocialActive($restrictPromotionClosure),
                        static::getFormFieldSlug()
                            ->columnSpan(1),
                        static::getFormFieldReferralUrl()
                            ->columnSpan(1),
                    ])
                    ->columnSpan(['default' => 2, 'md' => 1]),
                Forms\Components\Group::make()
                    ->schema([
                        static::getSocialReindexAction($restrictPromotionClosure),
                        static::getFormFieldAvatar('contributors')
                            ->columnSpan(1),
                    ])
                    ->columnSpan(['default' => 2, 'md' => 1]),
                static::getFormFieldBlockRichText('bio', 'Bio'),
            ])
            ->disabled($restrictEditingClosure)
            ->columns(2);
    }

    protected static function getCategoriesTab(Closure $restrictEditingClosure): Tabs\Tab
    {
        return Tabs\Tab::make('Categories')
            ->schema([
                static::getFormFieldCategoriesNotice('Contributor'),
                static::getFormFieldCategories(true),
            ])
            ->columns(2)
            ->disabled($restrictEditingClosure)
            ->visible(fn (?Model $record) => ! is_null($record));
    }

    protected static function getAdminTab(Closure $restrictEditingClosure): Tabs\Tab
    {
        $canAdminister = Gatekeeper::userCanAdminister(Contributor::class);
        $socialUrl = env('SOCIAL_URL');

        return Tabs\Tab::make('Admin')
            ->schema([
                static::getFormFieldTeam(),
                static::getFormFieldOwner(),
                Forms\Components\Toggle::make('is_controversial')
                    ->label('Designate as Controversial')
                    ->helperText(new HtmlString("
                        Toggle this on to enable a disclaimer on
                        <a href='{$socialUrl}' target='_blank' class='hover:underline text-gray-900 dark:text-white font-bold'>Apologist Social ↗</a>
                        that specifies that this Contributor is controversial in some way.
                        Reasons may include an excessively aggressive style, alleged or proven misconduct, etc.
                    ")),
                static::getFormFieldFeatured(),
            ])
            ->disabled($restrictEditingClosure)
            ->visible($canAdminister)
            ->columns(2);
    }

    /**
     * @return string[]
     */
    public static function getRelations(): array
    {
        return [
            ContributionsRelationManager::class,
            HistoryRelationManager::class,
        ];
    }

    protected static function getFormFieldTitle(): Forms\Components\Select
    {
        return static::getFormFieldSelect('title', ContributorTitle::asOptions())
            ->enum(ContributorTitle::class)
            ->required(false)
            ->live()
            ->afterStateUpdated(static::getUpdateSlugClosure());
    }

    protected static function getFormFieldFirstName(): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make('first_name')
            ->label('First Name')
            ->required()
            ->maxLength(250)
            ->live(onBlur: true)
            ->afterStateUpdated(static::getUpdateSlugClosure());
    }

    protected static function getFormFieldLastName(): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make('last_name')
            ->label('Last Name')
            ->required()
            ->maxLength(250)
            ->live(onBlur: true)
            ->afterStateUpdated(static::getUpdateSlugClosure());
    }

    protected static function getFormFieldSuffix(): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make('suffix')
            ->label('Suffix')
            ->maxLength(250)
            ->live(onBlur: true)
            ->afterStateUpdated(static::getUpdateSlugClosure());
    }

    protected static function getUpdateSlugClosure(): Closure
    {
        return function (Get $get, Set $set) {
            $nameParts = [
                Str::slug($get('title')),
                Str::slug($get('first_name')),
                Str::slug($get('last_name')),
                Str::slug($get('suffix')),
            ];
            $slug = trim(implode('-', $nameParts), '-');
            $set('slug', $slug);
        };
    }

    protected static function getTableBulkActions(): array
    {
        return array_merge(
            parent::getTableBulkActions(),
            [
                Tables\Actions\BulkActionGroup::make([
                    static::getTableBulkActionEnableSocial(static::$pluralModelLabel),
                    static::getTableBulkActionDisableSocial(static::$pluralModelLabel),

                ])
                    ->label('Social'),
            ]
        );
    }

    public static function getApiTransformer(): string
    {
        return ContributorTransformer::class;
    }
}
