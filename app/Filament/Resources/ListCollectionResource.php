<?php

namespace App\Filament\Resources;

use App\Enums\CollectionType;

class ListCollectionResource extends CollectionResource
{
    protected static ?string $navigationIcon = 'heroicon-s-list-bullet';

    protected static ?string $collectionType = CollectionType::LIST->value;

    protected static ?string $modelLabel = 'List';

    protected static ?string $pluralModelLabel = 'Lists';

    protected static ?string $navigationLabel = 'Lists';

    protected static ?string $slug = 'lists';

    protected static string $createLabel = 'create';

    public static function getPermissionPrefixes(): array
    {
        return [];
    }
}
