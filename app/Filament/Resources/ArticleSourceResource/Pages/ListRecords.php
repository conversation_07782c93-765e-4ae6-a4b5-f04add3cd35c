<?php

namespace App\Filament\Resources\ArticleSourceResource\Pages;

use App\Filament\Resources\ArticleSourceResource;
use App\Filament\Resources\SourceResource\Pages\ListRecords as BaseListRecords;

class ListRecords extends BaseListRecords
{
    protected static string $resource = ArticleSourceResource::class;

    protected function getHeaderActions(): array
    {
        return array_merge(
            [
                static::getPeriodicalsHeaderAction(),
            ],
            parent::getHeaderActions()
        );
    }
}
