<?php

namespace App\Filament\Resources;

use App\Enums\CollectionType;
use App\Enums\SourceType;

class AnthologyCollectionResource extends CollectionResource
{
    protected static ?string $navigationIcon = 'heroicon-s-book-open';

    protected static ?string $collectionType = CollectionType::ANTHOLOGY->value;

    protected static ?array $allowedSourceTypes = [
        SourceType::BOOK->value,
    ];

    protected static ?string $modelLabel = 'Anthology';

    protected static ?string $pluralModelLabel = 'Anthologies';

    protected static ?string $navigationLabel = 'Anthologies';

    protected static ?string $slug = 'anthologies';

    protected static string $createLabel = 'add';

    public static function getPermissionPrefixes(): array
    {
        return [];
    }
}
