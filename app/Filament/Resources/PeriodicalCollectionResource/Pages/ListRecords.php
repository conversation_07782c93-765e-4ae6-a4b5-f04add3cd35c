<?php

namespace App\Filament\Resources\PeriodicalCollectionResource\Pages;

use App\Filament\Resources\CollectionResource\Pages\ListRecords as BaseListRecords;
use App\Filament\Resources\PeriodicalCollectionResource;

class ListRecords extends BaseListRecords
{
    protected static string $resource = PeriodicalCollectionResource::class;

    protected function getHeaderActions(): array
    {
        return array_merge(
            [
                static::getBooksHeaderAction(),
                static::getArticlesHeaderAction(),
            ],
            parent::getHeaderActions()
        );
    }
}
