<?php

namespace App\Filament\Resources;

use App\Enums\CollectionType;
use App\Enums\SourceType;

class RssCollectionResource extends CollectionResource
{
    protected static ?string $navigationIcon = 'heroicon-s-rss';

    protected static ?string $collectionType = CollectionType::RSS->value;

    protected static ?array $allowedSourceTypes = [
        SourceType::URL->value,
    ];

    protected static ?string $modelLabel = 'RSS Feed';

    protected static ?string $pluralModelLabel = 'RSS Feeds';

    protected static ?string $navigationLabel = 'RSS Feeds';

    protected static ?string $slug = 'rss_feeds';

    protected static string $createLabel = 'add';

    public static function getPermissionPrefixes(): array
    {
        return [];
    }
}
