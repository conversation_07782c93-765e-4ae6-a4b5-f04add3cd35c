<?php

namespace App\Filament\Resources;

use App\Filament\RelationManagers\HistoryRelationManager;
use App\Filament\Resources\Resource as BaseResource;
use App\Filament\Resources\TeamResource\Api\Transformers\TeamTransformer;
use App\Filament\Resources\TeamResource\RelationManagers\FeatureRelationManager;
use App\Filament\Resources\TeamResource\RelationManagers\InvitationRelationManager;
use App\Filament\Resources\TeamResource\RelationManagers\MembershipRelationManager;
use App\Filament\Traits\CanBeAdministered;
use App\Filament\Traits\CanBeLocked;
use App\Models\Team;
use App\Services\Gatekeeper;
use Awcodes\Shout\Components\Shout;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Exception;
use Filament\Forms;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Tabs;

class TeamResource extends BaseResource implements HasShieldPermissions
{
    use CanBeAdministered;
    use CanBeLocked;

    protected static ?string $model = Team::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-s-users';

    protected static bool $isScopedToTenant = false;

    protected static string $createLabel = 'create';

    public static function form(Form $form): Form
    {
        $restrictEditingClosure = fn ($record) => restrict_editing($record);

        return $form
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        static::getSummaryTab(),
                        static::getAdminTab(),
                    ])
                    ->columnSpan(2)
                    ->columns(2)
                ,
            ])
            ->disabled($restrictEditingClosure);
    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                static::getTableColumnName(),
                static::getTableColumnOwner(),
                static::getTableColumnCount('memberships', '# Members')
                    ->visibleFrom('md'),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterOwner(),
                static::getTableFilterRelation('members'),
                static::getTableFilterArchived()
                    ->label('Suspended'),
            ])
            ->actions([
                static::decorateLockAction(Action::make('Lock')),
                static::decorateUnlockAction(Action::make('Unlock')),
                static::getTableActionEditRow(),
            ])
            ->bulkActions(static::getTableBulkActions())
            ->paginated(static::$pageOptions)
            ->defaultPaginationPageOption(static::$defaultPageOption);
    }

    protected static function getSummaryTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Summary')
            ->schema([

                Shout::make('team_notice')
                    ->visible(fn ($record) => ! is_null($record) && Gatekeeper::userOwns($record) && request()->has('new'))
                    ->content(new HtmlString(view(
                        'components.app.notices.edit-new-team',
                        [
                            'contributors_url' => env('KNOWLEDGE_BASE_CONTRIBUTORS_URL'),
                            'sources_url' => env('KNOWLEDGE_BASE_SOURCES_URL'),
                            'collections_url' => env('KNOWLEDGE_BASE_COLLECTIONS_URL'),
                        ]
                    )))
                    ->color('warning')
                    ->icon(false)
                    ->columnSpan(2)
                ,

                Forms\Components\Group::make()
                    ->schema([
                        static::getFormFieldName(),
                        static::getFormFieldOwner(true)
                            ->disabled(fn (?Model $record) => is_null($record) || ($record->getNumAdmins() < 2)),
                    ])
                ,

                Forms\Components\Group::make()
                    ->schema([
                        static::getFormFieldAvatar('teams'),
                    ])
                ,

                static::getFormFieldActive(
                    '
                            When toggled, only prepaid credits will be used.
                            This gives you full control over how much to spend.
                            However, please note that your Agents and other credit-based
                            functionality will be disabled once you run out of credits.
                        ',
                    'is_prepaid_only',
                    'Use Prepaid Credits Only'
                ),

            ])
        ;
    }

    protected static function getAdminTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Admin')
            ->schema([
                static::getFormFieldActive(
                    "
                        Users will not be able to switch to this team if it's deactivated.
                        Any Agents associated with the team will be disabled if the team is deactivated.
                    "
                ),
                static::getFormFieldActive(
                    'Allow the team to temporarily have access to all top plan entitlements while they are trialing the product. Untoggle when the team is ready to go live.',
                    'is_trial',
                    'Trial Mode (fully functional)'
                ),
                static::getFormFieldActive(
                    fn ($record) => is_null($record) ?
                        null :
                        new HtmlString("Corpus Key: <span class='text-white font-bold'>".env('TEAM_CORPUS_PREFIX').$record->id.'</span>'),
                    'has_custom_corpus',
                    'Custom Corpus'
                )
                    ->live()
                ,
                static::getFormFieldActive(
                    'Elevate status to partner. Includes top plan entitlements.',
                    'is_partner',
                    'Treat as Partner'
                ),
                TextInput::make('corpus_api_key')
                    ->label('Custom Corpus API Key')
                    ->maxLength(500)
                    ->disabled(fn (Get $get) => !$get('has_custom_corpus'))
                ,
                static::getFormFieldActive(
                    'Do not charge this team.',
                    'is_nonbillable',
                    'Do Not Bill'
                ),
            ])
            ->visible(Gatekeeper::isAdmin())
        ;
    }

    /**
     * @return string[]
     */
    public static function getRelations(): array
    {
        return [
            MembershipRelationManager::class,
            InvitationRelationManager::class,
            FeatureRelationManager::class,
            HistoryRelationManager::class,
        ];
    }

    public static function getApiTransformer(): string
    {
        return TeamTransformer::class;
    }
}
