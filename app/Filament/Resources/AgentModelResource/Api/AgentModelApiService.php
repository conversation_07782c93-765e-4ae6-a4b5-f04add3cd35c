<?php

namespace App\Filament\Resources\AgentModelResource\Api;

use App\Filament\Api\ApiService;
use App\Filament\Resources\AgentModelResource;

class AgentModelApiService extends ApiService
{
    protected static ?string $resource = AgentModelResource::class;
    protected static ?string $groupRouteName = 'models';

    /**
     * @return string[]
     */
    protected static function getActions(): array
    {
        return [
            'Pagination',
            'Detail',
        ];
    }
}
