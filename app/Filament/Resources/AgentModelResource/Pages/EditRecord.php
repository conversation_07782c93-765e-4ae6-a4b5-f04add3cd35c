<?php

namespace App\Filament\Resources\AgentModelResource\Pages;

use App\Filament\Resources\AgentModelResource;
use App\Filament\Templates\EditRecord as BaseEditRecord;
use App\Filament\Traits\HasRestrictedAccess;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use App\Filament\Traits\HasFields;

class EditRecord extends BaseEditRecord
{
    use HasRestrictedAccess;
    use HasFields;

    protected static string $resource = AgentModelResource::class;

    /**
     * @return array|Action[]|ActionGroup[]
     */
    protected function getHeaderActions(): array
    {
        return array_merge(
            [
                static::getReplicateHeaderAction(
                    [
                        static::getFormFieldName()
                            ->required(),
                        static::getFormFieldKey()
                            ->required(),
                        static::$resource::getFormFieldProviderModel(),
                    ],
                    ['is_active']
                )
                    ->mutateRecordDataUsing(function (array $data): array {
                        $data['name'] = "{$data['name']} copy";
                        $data['key'] = "{$data['key']}-copy";
                        $data['provider_model'] = "{$data['provider_model']}-copy";
                        return $data;
                    })
                ,
            ],
            parent::getHeaderActions(),
        );
    }

}
