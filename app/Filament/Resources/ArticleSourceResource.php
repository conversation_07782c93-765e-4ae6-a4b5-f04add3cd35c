<?php

namespace App\Filament\Resources;

use App\Enums\CollectionType;
use App\Enums\SourceType;

class ArticleSourceResource extends SourceResource
{
    protected static ?string $navigationIcon = 'heroicon-s-document-text';

    protected static ?string $sourceType = SourceType::ARTICLE->value;

    protected static ?array $allowedCollectionTypes = [
        CollectionType::LIST->value,
        CollectionType::PERIODICAL->value,
    ];

    protected static ?string $modelLabel = 'Article or Paper';

    protected static ?string $pluralModelLabel = 'Articles & Papers';

    protected static ?string $navigationLabel = 'Articles & Papers';

    protected static ?string $slug = 'articles';

    protected static string $createLabel = 'add';

    public static function getPermissionPrefixes(): array
    {
        return [];
    }
}
