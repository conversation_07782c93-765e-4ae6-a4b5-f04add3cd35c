<?php

namespace App\Filament\Resources\SourceResource\Pages;

use App\Enums\SourceType;
use App\Filament\Resources\AnthologyCollectionResource;
use App\Filament\Resources\PeriodicalCollectionResource;
use App\Filament\Resources\SeriesCollectionResource;
use App\Filament\Resources\SourceResource;
use App\Filament\Templates\ListRecords as BaseListRecords;
use App\Filament\Traits\HasFields;
use App\Models\Source;
use App\Services\YoutubeClient;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\HtmlString;

class ListRecords extends BaseListRecords
{
    use HasFields;

    protected static string $resource = SourceResource::class;

    protected static function getImportHeaderAction(): Action
    {
        return Action::make('import')
            ->extraAttributes(['id' => btn_id('import')])
            ->label('Import')
            ->icon('heroicon-s-cloud-arrow-down')
            ->outlined()
            ->requiresConfirmation()
            ->form([
                static::getFormFieldUrl()
                    ->required(),
            ])
            ->modalHeading('Import a Source from a Web URL')
            ->modalDescription("
                Submit a publicly accessible URL, and we'll try to get as much content as we we can from it.
            ")
            ->modalIcon('heroicon-s-cloud-arrow-down')
            ->action(function (array $data): void {
                $title = Source::getPrefixedName($data['url']);
                $type = YoutubeClient::isVideoUrl($data['url']) ? SourceType::YOUTUBE : SourceType::URL;
                $source = Source::create([
                    'name' => $title,
                    'type' => $type,
                    'url' => $data['url'],
                ]);
                if ($source) {
                    Notification::make()
                        ->title('URL Submitted to Import')
                        ->body(new HtmlString('
                            The import process may take several minutes to complete. '.
                            get_support_link('Reach out to us').
                            ' if the Source doesn\'t appear in the list below after 10 minutes.
                        '))
                        ->success()
                        ->send();
                } else {
                    Notification::make()
                        ->title('URL Failed to Import')
                        ->body(new HtmlString('
                            Please try again or '.get_support_link('reach out to us for support').'</a>.
                        '))
                        ->danger()
                        ->send();
                }
            });
    }

    protected static function getPeriodicalsHeaderAction(): Action
    {
        return Action::make('periodicals')
            ->extraAttributes(['id' => btn_id('periodicals')])
            ->label('Periodicals')
            ->icon('heroicon-s-newspaper')
            ->outlined()
            ->url(PeriodicalCollectionResource::getUrl());
    }

    protected static function getAnthologiesHeaderAction(): Action
    {
        return Action::make('anthologies')
            ->extraAttributes(['id' => btn_id('anthologies')])
            ->label('Anthologies')
            ->icon('heroicon-s-book-open')
            ->outlined()
            ->url(AnthologyCollectionResource::getUrl());
    }

    protected static function getSeriesHeaderAction(): Action
    {
        return Action::make('series')
            ->extraAttributes(['id' => btn_id('series')])
            ->label('Series')
            ->icon('heroicon-s-rectangle-stack')
            ->outlined()
            ->url(SeriesCollectionResource::getUrl());
    }
}
