<?php

namespace App\Filament\Resources\SourceResource\Pages;

use App\Enums\SourceType;
use App\Events\SourceSavedEvent;
use App\Filament\Pages\App\Analytics\SourceAnalytics;
use App\Filament\Resources\SourceResource;
use App\Filament\Templates\EditRecord as BaseEditRecord;
use App\Jobs\DownloadSourceYoutubeTranscriptJob;
use App\Jobs\TranscribeSourceMediaJob;
use App\Models\Source;
use App\Services\Gatekeeper;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Notifications\Notification;
use Illuminate\Support\HtmlString;

class EditRecord extends BaseEditRecord
{
    protected static string $resource = SourceResource::class;

    protected static ?string $contributeTemplate = 'components.app.contribute-source';

    /**
     * @return array|Action[]|ActionGroup[]
     */
    protected function getHeaderActions(): array
    {

        $actions = $this->getModerationHeaderActions();

        if (
            (
                ! empty($this->getRecord()->url) &&
                in_array(static::$resource::getSourceType(), [SourceType::URL->value, SourceType::EPISODE->value])
            ) ||
            (
                ! empty($this->getRecord()->external_id) &&
                in_array(static::$resource::getSourceType(), [SourceType::YOUTUBE->value, SourceType::BOOK->value])
            )
        ) {

            $actions[] = Action::make('reimport')
                ->extraAttributes(['id' => btn_id('reimport')])
                ->label('Re-Import')
                ->icon('heroicon-s-cloud-arrow-down')
                ->color('warning')
                ->outlined()
                ->requiresConfirmation()
                ->action(function ($record, $livewire) {
                    try {
                        $record->import(true);
                        Notification::make()
                            ->title('Import successful')
                            ->success()
                            ->send()
                        ;
                        $livewire->dispatch('$refresh');
                        $this->fillForm();
                    } catch (\Throwable $e) {
                        Notification::make()
                            ->title('Import failed')
                            ->body('Please try again later')
                            ->danger()
                            ->send()
                        ;
                    }
                    return true;
                })
                ->modalHeading('Re-Import Source')
                ->modalDescription('Note that this will overwrite any values that have been updated since initial import.')
                ->modalIcon('heroicon-s-cloud-arrow-down')
                ->visible(fn ($record) => Gatekeeper::userCan('import', $record::class, $record))
            ;

        }

        $actions[] = static::getAnalyzeHeaderAction(SourceAnalytics::class)->extraAttributes(['class' => 'ml-0']);
        $actions[] = static::decorateViewListingAction(Action::make('Preview'));
        $actions[] = static::decorateViewReferenceAction(Action::make('Reference'));

        $actions = array_merge(
            $actions,
            parent::getHeaderActions(),
        );

        foreach ($actions as $key => $action) {
            $actions[$key]->after(function (?Source $record) {
                SourceSavedEvent::dispatch($record);
            });
        }

        return $actions;

    }

    //    /**
    //     * @return array|Action[]|ActionGroup[]
    //     */
    //    protected function getFormActions(): array
    //    {
    //
    //        $actions = [
    //            $this->getSaveFormAction(),
    //            $this->getCancelFormAction(),
    //        ];
    //
    //        if (!$this->getRecord()->is_locked) {
    //            array_push(
    //                $actions,
    //                $this->getFormActionArchive(),
    //                $this->getFormActionRestore(),
    //                $this->getFormActionDelete(),
    //            );
    //        }
    //
    //        return $actions;
    //
    //    }

    protected function beforeValidate(): void
    {
        increase_timeout();
    }
}
