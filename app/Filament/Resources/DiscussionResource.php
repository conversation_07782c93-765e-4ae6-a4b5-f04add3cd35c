<?php

namespace App\Filament\Resources;

use App\Enums\OrganizationType;
use App\Filament\RelationManagers\HistoryRelationManager;
use App\Filament\Resources\DiscussionResource\Api\Transformers\DiscussionTransformer;
use App\Filament\Resources\Resource as BaseResource;
use App\Filament\Traits\CanBeAdministered;
use App\Filament\Traits\CanBeModerated;
use App\Models\Discussion;
use App\Services\Gatekeeper;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Exception;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class DiscussionResource extends BaseResource implements HasShieldPermissions
{
    use CanBeAdministered;
    use CanBeModerated;

    protected static ?string $model = Discussion::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-s-chat-bubble-left-right';

    public static function form(Form $form): Form
    {
        $canAdminister = Gatekeeper::userCanAdminister(Discussion::class);
        $restrictEditingClosure = fn ($record) => restrict_editing($record);

        return $form
            ->schema([

                Fieldset::make('Summary')
                    ->schema([
                        static::getFormNoticePendingApproval('Organization'),
                        static::getFormFieldName(),
                        static::getFormFieldType(OrganizationType::class),
                        static::getFormFieldTaxId(),
                    ])
                    ->columnSpan(2)
                    ->disabled($restrictEditingClosure),

                Fieldset::make('Listing')
                    ->schema([
                        Forms\Components\Group::make()
                            ->schema([
                                static::getFormFieldSlug(),
                                static::getFormFieldReferralUrl(),
                            ])
                            ->columnSpan(['default' => 2, 'md' => 1]),
                        Forms\Components\Group::make()
                            ->schema([
                                static::getFormFieldImage('organizations'),
                            ])
                            ->columnSpan(['default' => 2, 'md' => 1]),
                        static::getFormFieldBlockRichText(),
                        static::getFormFieldCategories(),
                    ])
                    ->disabled($restrictEditingClosure)
                    ->columnSpan(2),

                Fieldset::make('Administration')
                    ->visible($canAdminister)
                    ->schema([
                        static::getFormFieldTeam(),
                        static::getFormFieldOwner(),
                    ])
                    ->disabled($restrictEditingClosure)
                    ->columnSpan(2),

            ]);
    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                static::$isScopedToTenant = false;
                if (! Gatekeeper::userCanAdminister(static::getModel())) {
                    $query->whereBelongsTo(get_current_team());
                }
            })
            ->columns([
                static::getTableColumnName()
                    ->searchable(['first_name', 'last_name', 'title', 'suffix']),
                static::getTableColumnCount('authored', '# Authorships')
                    ->visibleFrom('md'),
                static::getTableColumnCount('nonauthored', '# Other')
                    ->visibleFrom('md'),
                static::getTableColumnApprovalStatus()
                    ->visibleFrom('md'),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterStatus(),
                static::getTableFilterArchived(),
                static::getTableFilterTeam(),
                static::getTableFilterOwner(),
            ])
            ->actions([
                static::getTableActionEditRow(),
            ])
            ->bulkActions(static::getTableBulkActions());
    }

    /**
     * @return string[]
     */
    public static function getRelations(): array
    {
        return [
            HistoryRelationManager::class,
        ];
    }

    public static function getApiTransformer(): string
    {
        return DiscussionTransformer::class;
    }
}
