<?php

namespace App\Filament\Resources\UrlSourceResource\Pages;

use App\Filament\Resources\RssCollectionResource;
use App\Filament\Resources\SourceResource\Pages\ListRecords as BaseListRecords;
use App\Filament\Resources\UrlSourceResource;
use App\Filament\Resources\WebsiteCollectionResource;
use Filament\Actions\Action;

class ListRecords extends BaseListRecords
{
    protected static string $resource = UrlSourceResource::class;

    protected function getHeaderActions(): array
    {
        return array_merge(
            [
                Action::make('rss_feeds')
                    ->extraAttributes(['id' => btn_id('rss-feeds')])
                    ->label('RSS Feeds')
                    ->icon('heroicon-s-rss')
                    ->outlined()
                    ->url(RssCollectionResource::getUrl()),
                Action::make('websites')
                    ->extraAttributes(['id' => btn_id('websites')])
                    ->label('Websites')
                    ->icon('heroicon-s-globe-alt')
                    ->outlined()
                    ->url(WebsiteCollectionResource::getUrl()),
                static::getImportHeaderAction(),
            ],
            parent::getHeaderActions()
        );
    }
}
