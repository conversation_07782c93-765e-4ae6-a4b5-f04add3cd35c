<?php

namespace App\Filament\Resources\CategoryResource\Api;

use App\Filament\Api\ApiService;
use App\Filament\Resources\CategoryResource;

class CategoryApiService extends ApiService
{
    protected static ?string $resource = CategoryResource::class;

    /**
     * @return string[]
     */
    protected static function getActions(): array
    {
        return [
            'Pagination',
            'Detail',
        ];
    }
}
