<?php

namespace App\Filament\Resources\CategoryResource\Api\Handlers;

use App\Filament\Api\Handlers\PaginationHandler as BasePaginationHandler;
use App\Filament\Resources\CategoryResource;

class PaginationHandler extends BasePaginationHandler
{
    public static ?string $resource = CategoryResource::class;

    public static bool $public = true;

    protected static bool $restrictToTeams = false;
}
