<?php

namespace App\Filament\Resources\CategoryResource\Pages;

use App\Filament\Resources\CategoryResource;
use App\Filament\Templates\EditRecord as BaseEditRecord;
use App\Filament\Traits\HasRestrictedAccess;

class EditRecord extends BaseEditRecord
{
    use HasRestrictedAccess;

    protected static string $resource = CategoryResource::class;

    /**
     * @return array|mixed[]
     */
    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data['description'] = strip_paragraphs($data['description']);

        return $data;
    }
}
