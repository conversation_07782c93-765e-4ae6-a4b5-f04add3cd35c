<?php

namespace App\Filament\Resources\CategoryResource\Pages;

use App\Filament\Resources\CategoryResource;
use App\Filament\Templates\CreateRecord as BaseCreateRecord;
use App\Filament\Traits\HasRestrictedAccess;

class CreateRecord extends BaseCreateRecord
{
    use HasRestrictedAccess;

    protected static string $resource = CategoryResource::class;

    /**
     * @return array|mixed[]
     */
    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data['description'] = strip_paragraphs($data['description']);

        return $data;
    }
}
