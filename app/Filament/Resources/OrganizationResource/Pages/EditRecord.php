<?php

namespace App\Filament\Resources\OrganizationResource\Pages;

use App\Filament\Pages\App\Analytics\OrganizationAnalytics;
use App\Filament\Resources\OrganizationResource;
use App\Filament\Templates\EditRecord as BaseEditRecord;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;

class EditRecord extends BaseEditRecord
{
    protected static string $resource = OrganizationResource::class;

    /**
     * @return array|Action[]|ActionGroup[]
     */
    protected function getHeaderActions(): array
    {
        return array_merge(
            $this->getModerationHeaderActions(),
            [
                static::getAnalyzeHeaderAction(OrganizationAnalytics::class)->extraAttributes(['class' => 'ml-0']),
                static::decorateViewListingAction(Action::make('Preview')),
                static::decorateViewReferenceAction(Action::make('Reference')),
            ],
            $this->getLockActions(),
        );
    }
}
