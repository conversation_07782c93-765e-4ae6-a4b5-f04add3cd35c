<?php

namespace App\Filament\Resources;

use App\Enums\OrganizationType;
use App\Filament\RelationManagers\HistoryRelationManager;
use App\Filament\Resources\OrganizationResource\Api\Transformers\OrganizationTransformer;
use App\Filament\Resources\Resource as BaseResource;
use App\Filament\Traits\CanBeAdministered;
use App\Filament\Traits\CanBeLocked;
use App\Filament\Traits\CanBeModerated;
use App\Filament\Traits\CanBePromoted;
use App\Models\Organization;
use App\Services\Gatekeeper;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Exception;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;

class OrganizationResource extends BaseResource implements HasShieldPermissions
{
    use CanBeAdministered;
    use CanBeLocked;
    use CanBeModerated;
    use CanBePromoted;

    protected static ?string $model = Organization::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-s-building-office';

    protected static ?string $pluralModelLabel = 'Organizations';

    public static function form(Form $form): Form
    {

        $canAdminister = Gatekeeper::userCanAdminister(Organization::class);
        $restrictEditingClosure = fn ($record) => restrict_editing($record);
        $restrictPromotionClosure = fn ($record) => restrict_editing($record) || ! Gatekeeper::userCan('promote', Organization::class);
        $restrictEditingOrAdminClosure = fn ($record) => restrict_editing($record) || ! $canAdminister;
        $socialUrl = env('SOCIAL_URL');

        return $form
            ->schema([

                Tabs::make('Tabs')
                    ->tabs([

                        Tabs\Tab::make('Summary')
                            ->schema([
                                static::getFormNoticePendingApproval('Organization'),
                                static::getFormFieldName(),
                                static::getFormFieldType(OrganizationType::class),
                                static::getFormFieldClassification(),
                                static::getFormFieldTaxId(),
                            ])
                            ->disabled($restrictEditingClosure)
                            ->columns(2),

                        Tabs\Tab::make('Listing')
                            ->schema([
                                Forms\Components\Group::make()
                                    ->schema([
                                        static::getFormFieldSocialActive($restrictPromotionClosure),
                                        static::getFormFieldSlug()
                                            ->columnSpan(1),
                                        static::getFormFieldReferralUrl()
                                            ->columnSpan(1),
                                    ])
                                    ->columnSpan(['default' => 2, 'md' => 1]),
                                Forms\Components\Group::make()
                                    ->schema([
                                        static::getSocialReindexAction($restrictPromotionClosure),
                                        static::getFormFieldImage('organizations')
                                            ->columnSpan(1),
                                    ])
                                    ->columnSpan(['default' => 2, 'md' => 1]),
                                static::getFormFieldBlockRichText(),
                            ])
                            ->disabled($restrictEditingClosure)
                            ->columns(2),

                        Tabs\Tab::make('Categories')
                            ->schema([
                                static::getFormFieldCategoriesNotice('Organization'),
                                static::getFormFieldCategories(true),
                            ])
                            ->disabled($restrictEditingClosure)
                            ->columns(2)
                            ->visible(fn (?Model $record) => ! is_null($record)),

                        Tabs\Tab::make('Admin')
                            ->schema([
                                static::getFormFieldTeam(),
                                static::getFormFieldOwner(),
                                static::getFormFieldFeatured(),
                            ])
                            ->disabled($restrictEditingOrAdminClosure)
                            ->visible($canAdminister)
                            ->columns(2),

                    ])
                    ->persistTabInQueryString()
                    ->id('organization'),

            ])
            ->columns(1);

    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                static::$isScopedToTenant = false;
                if (! Gatekeeper::userCanAdminister(static::getModel())) {
                    $query->whereBelongsTo(get_current_team());
                }
            })
            ->columns([
                static::getTableColumnName(),
                static::getTableColumnActive('social_active', 'Social Listing')
                    ->visibleFrom('md'),
                static::getTableColumnApprovalStatus()
                    ->visibleFrom('md'),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
                static::getTableColumnFeaturedSequence(),
            ])
            ->defaultSort('name')
            ->filters([
                static::getTableFilterStatus(),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
                static::getTableFilterBoolean('social_active', 'Apologist Social'),
                static::getTableFilterBoolean('is_locked', 'Locked', 'Unlocked'),
                static::getTableFilterFeatured(),
                static::getTableFilterArchived(),
                static::getTableFilterTeam(),
                static::getTableFilterOwner(),
            ])
            ->actions([
                static::decorateLockAction(Action::make('Lock')),
                static::decorateUnlockAction(Action::make('Unlock')),
                static::decorateViewListingAction(Tables\Actions\Action::make('Preview')),
                static::decorateViewReferenceAction(Tables\Actions\Action::make('Reference')),
                static::getTableActionEditRow(),
            ])
            ->bulkActions(static::getTableBulkActions())
            ->paginated(static::$pageOptions)
            ->defaultPaginationPageOption(static::$defaultPageOption);
    }

    /**
     * @return string[]
     */
    public static function getRelations(): array
    {
        return [
            HistoryRelationManager::class,
        ];
    }

    protected static function getFormFieldTaxId(): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make('tax_id')
            ->label('Tax ID')
            ->maxLength(100);
    }

    protected static function getTableBulkActions(): array
    {
        return array_merge(
            parent::getTableBulkActions(),
            [
                Tables\Actions\BulkActionGroup::make([
                    static::getTableBulkActionEnableSocial(static::$pluralModelLabel),
                    static::getTableBulkActionDisableSocial(static::$pluralModelLabel),

                ])
                    ->label('Social'),
            ]
        );
    }

    public static function getApiTransformer(): string
    {
        return OrganizationTransformer::class;
    }
}
