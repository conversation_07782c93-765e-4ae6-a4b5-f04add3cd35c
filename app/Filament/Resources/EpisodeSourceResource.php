<?php

namespace App\Filament\Resources;

use App\Enums\CollectionType;
use App\Enums\SourceType;

class EpisodeSourceResource extends SourceResource
{
    protected static ?string $navigationIcon = 'heroicon-s-microphone';

    protected static ?string $sourceType = SourceType::EPISODE->value;

    protected static ?array $allowedCollectionTypes = [
        CollectionType::LIST->value,
        CollectionType::PODCAST->value,
    ];

    protected static ?string $modelLabel = 'Podcast Episode';

    protected static ?string $pluralModelLabel = 'Podcast Episodes';

    protected static ?string $navigationLabel = 'Podcast Episodes';

    protected static ?string $slug = 'podcast_episodes';

    protected static string $createLabel = 'add';

    public static function getPermissionPrefixes(): array
    {
        return [];
    }
}
