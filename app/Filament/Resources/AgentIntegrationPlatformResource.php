<?php

namespace App\Filament\Resources;

use App\Filament\RelationManagers\HistoryRelationManager;
use App\Filament\Resources\AgentIntegrationPlatformResource\Api\Transformers\AgentIntegrationPlatformTransformer;
use App\Filament\Resources\Resource as BaseResource;
use App\Models\AgentIntegrationPlatform;
use Exception;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Table;

class AgentIntegrationPlatformResource extends BaseResource
{
    protected static ?string $model = AgentIntegrationPlatform::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-s-square-3-stack-3d';

    protected static bool $isScopedToTenant = false;

    protected static string $createLabel = 'add';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Fieldset::make('Agent Integration Platform')
                    ->schema([
                        static::getFormFieldName(),
                        static::getFormFieldActive(
                            'Only active integration platforms will be selectable for Agents.'
                        ),
                        TextInput::make('key')
                            ->label('Key')
                            ->maxLength(25)
                            ->required()
                        ,
                        static::getFormFieldImage('agent_integration_platforms')
                            ->required()
                        ,
                        Textarea::make('description')
                            ->label('Description')
                            ->maxLength(500)
                            ->columnSpan(2)
                            ->rows(5)
                        ,
                        TextInput::make('endpoint_url')
                            ->label('Endpoint URL')
                            ->maxLength(250)
                        ,
                        static::getFormFieldLanguages(),

                        Forms\Components\Group::make()
                            ->schema([
                                static::getFormFieldActive(null, 'has_account', 'Has Platform Account ID'),
                                static::getFormFieldActive(null, 'has_secret', 'Has Platform Secret'),
                                static::getFormFieldActive(null, 'has_cue', 'Can Have Cue Phrase'),
                                static::getFormFieldActive(null, 'has_welcome', 'Can Have Welcome Message'),
                                static::getFormFieldActive(null, 'has_cta', 'Can Have CTA Message'),
                                static::getFormFieldActive(null, 'can_auto_initialize', 'Can Auto-Initialize'),
                            ])
                        ,
                        Forms\Components\Group::make()
                            ->schema([
                                TextInput::make('param_1_label')
                                    ->label('Param 1 Label')
                                    ->maxLength(50)
                                ,
                                TextInput::make('param_2_label')
                                    ->label('Param 2 Label')
                                    ->maxLength(50)
                                ,
                                TextInput::make('param_3_label')
                                    ->label('Param 3 Label')
                                    ->maxLength(50)
                                ,
                            ])
                        ,

                    ]),
            ]);
    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                static::getTableColumnName(),
                static::getTableColumnActive(),
                ImageColumn::make('image_path')->height(24),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterArchived(),
            ])
            ->actions([
                static::getTableActionEditRow(),
            ])
            ->bulkActions(static::getTableBulkActions())
            ->paginated(static::$pageOptions)
            ->defaultPaginationPageOption(static::$defaultPageOption);
    }

    /**
     * @return string[]
     */
    public static function getRelations(): array
    {
        return [
            HistoryRelationManager::class,
        ];
    }

    public static function getApiTransformer(): string
    {
        return AgentIntegrationPlatformTransformer::class;
    }
}
