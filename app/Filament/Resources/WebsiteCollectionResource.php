<?php

namespace App\Filament\Resources;

use App\Enums\CollectionType;
use App\Enums\SourceType;

class WebsiteCollectionResource extends CollectionResource
{
    protected static ?string $navigationIcon = 'heroicon-s-globe-alt';

    protected static ?string $collectionType = CollectionType::WEBSITE->value;

    protected static ?array $allowedSourceTypes = [
        SourceType::URL->value,
    ];

    protected static ?string $modelLabel = 'Website';

    protected static ?string $pluralModelLabel = 'Websites';

    protected static ?string $navigationLabel = 'Websites';

    protected static ?string $slug = 'websites';

    protected static string $createLabel = 'add';

    public static function getPermissionPrefixes(): array
    {
        return [];
    }
}
