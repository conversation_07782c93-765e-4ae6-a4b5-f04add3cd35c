<?php

namespace App\Filament\Resources;

use App\Enums\CollectionType;
use App\Enums\SourceContributionRole;
use App\Enums\SourceImageHandling;
use App\Filament\RelationManagers\HistoryRelationManager;
use App\Filament\Resources\CollectionResource\Api\Transformers\CollectionTransformer;
use App\Filament\Resources\Resource as BaseResource;
use App\Filament\Traits\CanBeAdministered;
use App\Filament\Traits\CanBeImported;
use App\Filament\Traits\CanBeLocked;
use App\Filament\Traits\CanBeModerated;
use App\Filament\Traits\CanBePromoted;
use App\Models\Category;
use App\Models\Collection;
use App\Models\Contributor;
use App\Services\Gatekeeper;
use App\Services\Labeller;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Closure;
use Exception;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;

class CollectionResource extends BaseResource implements HasShieldPermissions
{
    use CanBeAdministered;
    use CanBeImported;
    use CanBeLocked;
    use CanBeModerated;
    use CanBePromoted;

    protected static ?string $model = Collection::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-s-document-duplicate';

    protected static string $createLabel = 'create';

    protected static ?string $pluralModelLabel = 'Collections';

    protected static ?string $collectionType = null;

    protected static ?array $allowedSourceTypes = [];

    public static function getEloquentQuery(): Builder
    {
        $qry = parent::getEloquentQuery();
        if (! is_null(static::$collectionType)) {
            $qry->where('type', static::$collectionType);
        }

        return $qry;
    }

    public static function form(Form $form): Form
    {

        $cannotConfigureSourcesClosure = fn (Get $get) => ! $get('auto_import_sources');
        $restrictEditingClosure = fn ($record) => restrict_editing($record);
        $socialUrl = env('SOCIAL_URL');
        $agentUrl = env('AGENT_URL');
        $canAdminister = Gatekeeper::userCanAdminister(Collection::class);

        return $form
            ->schema([

                Tabs::make('Tabs')
                    ->tabs([
                        static::getFormTabSummary($restrictEditingClosure, $canAdminister),
                        static::getFormTabListing($restrictEditingClosure),
                        static::getFormTabSources(
                            $restrictEditingClosure,
                            $cannotConfigureSourcesClosure,
                            $socialUrl,
                            $agentUrl
                        ),
                        static::getFormTabScraping($restrictEditingClosure, $cannotConfigureSourcesClosure),
                        static::getFormTabAdmin($restrictEditingClosure, $canAdminister),
                    ])
                    ->persistTabInQueryString()
                    ->id('collection'),

            ])
            ->columns(1);

    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                static::$isScopedToTenant = false;
                $query->tree();
                if (! Gatekeeper::userCanAdminister(static::getModel())) {
                    $query->whereBelongsTo(get_current_team());
                }
            })
            ->columns([
                static::getTableColumnNestedName(),
                static::getTableColumnParent()
                    ->visible(static::$collectionType == CollectionType::LIST->value)
                    ->visibleFrom('md'),
                static::getTableColumnActive('social_active', 'Social Listing')
                    ->visibleFrom('md'),
                static::getTableColumnApprovalStatus()
                    ->visibleFrom('md'),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
                static::getTableColumnFeaturedSequence(),
            ])
            ->defaultSort('name_nested')
            ->filters([
                static::getTableFilterStatus(),
                static::getTableFilterParent(),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
                static::getTableFilterBoolean('social_active', 'Apologist Social'),
                static::getTableFilterSelect('default_source_weight', config('agent.weights'), 'Default Source Weight')
                    ->visible(Gatekeeper::userCanAdminister(Collection::class)),
                static::getTableFilterBoolean('is_locked', 'Locked', 'Unlocked'),
                static::getTableFilterRelation('sources'),
                static::getTableFilterFeatured(),
                static::getTableFilterArchived(),
                static::getTableFilterTeam(),
                static::getTableFilterOwner(),
            ])
            ->filtersFormColumns(3)
            ->actions([
                static::decorateLockAction(Action::make('Lock')),
                static::decorateUnlockAction(Action::make('Unlock')),
                static::decorateViewListingAction(Tables\Actions\Action::make('Preview')),
                static::decorateViewReferenceAction(Tables\Actions\Action::make('Reference')),
                static::getTableActionEditRow(),
            ])
            ->bulkActions(array_merge(static::getTableBulkActions(), static::getTableBulkActionsCollections($table)))
            ->paginated(static::$pageOptions)
            ->defaultPaginationPageOption(static::$defaultPageOption);
    }

    protected static function getFormTabSummary(Closure $restrictEditingClosure, bool $canAdminister): Tabs\Tab
    {

        $fields = [
            static::getFormNoticePendingApproval('Collection'),
            static::getFormFieldName(),
            static::getFormFieldParent(['type' => CollectionType::LIST->value])
                ->label('Parent List'),
            static::getFormFieldClassification(),
            static::getFormFieldCollectionUrl()
                ->live(onBlur: true),
            static::getFormFieldLanguage(),
            static::getFormFieldExternalId('YouTube Channel or Playlist ID')
                ->visible(fn (Get $get) => in_array($get('type'), [CollectionType::CHANNEL->value]))
                ->helperText(fn (Get $get) =>
                    in_array($get('type'), [CollectionType::CHANNEL->value]) ?
                    new HtmlString("
                        <a href='https://www.youtube.com/t/terms' target='_blank' class='text-primary-500 underline'>YouTube Terms of Service ↗</a>
                    ") :
                    null
                )
                ->live(onBlur: true),
        ];

        if (!$canAdminister) {
            $fields[] = Forms\Components\Hidden::make('type')
                ->default(static::$collectionType);
        }

        return Tabs\Tab::make('Summary')
            ->schema($fields)
            ->disabled($restrictEditingClosure)
            ->columns(2);
    }

    protected static function getFormTabListing(Closure $restrictEditingClosure): Tabs\Tab
    {
        $restrictPromotionClosure = fn ($record) => (!is_null($record) && !restrict_editing($record)) || !Gatekeeper::userCan('promote', Collection::class);
        return Tabs\Tab::make('Listing')
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        static::getFormFieldSocialActive($restrictPromotionClosure),
                        static::getFormFieldSlug()
                            ->columnSpan(1),
                        static::getFormFieldReferralUrl()
                            ->columnSpan(1),
                    ])
                    ->columnSpan(['default' => 2, 'md' => 1]),
                Forms\Components\Group::make()
                    ->schema([
                        static::getSocialReindexAction($restrictEditingClosure),
                        static::getFormFieldImage('collections')
                            ->columnSpan(1),
                    ])
                    ->columnSpan(['default' => 2, 'md' => 1]),
                static::getFormFieldBlockRichText(),
            ])
            ->disabled($restrictEditingClosure)
            ->columns(2);
    }

    protected static function getFormTabSources(Closure $restrictEditingClosure, Closure $cannotConfigureSourcesClosure, string $socialUrl, string $agentUrl): Tabs\Tab
    {
        $transcriptionCredits = env_int(
            (static::getCollectionType() == CollectionType::CHANNEL->value) ?
                'TRANSCRIBE_YOUTUBE_CREDITS' :
                'TRANSCRIBE_MEDIA_CREDITS'
        );
        return Tabs\Tab::make('Sources')
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Toggle::make('auto_import_sources')
                            ->label('Auto-Import New Sources')
                            ->visible(fn (Get $get) => (in_array($get('type'), [CollectionType::RSS->value, CollectionType::WEBSITE->value, CollectionType::CHANNEL->value, CollectionType::PODCAST->value])))
                            ->disabled(fn (Get $get) => (empty($get('url')) && empty($get('external_id'))))
                            ->hintIcon(
                                fn (Get $get) => (empty($get('url')) && empty($get('external_id'))) ? 'heroicon-s-question-mark-circle' : null,
                                tooltip: function (Get $get) {
                                    $str = 'New Sources associated with this Collection will automatically be imported every 24 hours.';
                                    if (empty($get('url')) && empty($get('external_id'))) {
                                        $str .= 'Enter a URL to enable this option.';
                                    }
                                    return $str;
                                }
                            )
                            ->afterStateUpdated(function (Set $set, $state) {
                                if (!$state) {
                                    $set('auto_list_sources', false);
                                    $set('auto_index_sources', false);
                                    $set('auto_transcribe_sources', false);
                                    $set('auto_categorize_sources', false);
                                }
                            })
                            ->live()
                        ,
                        Forms\Components\Toggle::make('auto_index_sources')
                            ->label(new HtmlString("Index Auto-Imported Sources for <a href='{$agentUrl}' target='_blank' class='hover:underline text-gray-900 dark:text-white font-bold'>Apologist Agent ↗</a>"))
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Sources imported for this Collection will automatically be indexed for use by your Agent(s). They will still need to be approved to be added to the common corpus.'
                            )
                            ->visible(fn (Get $get) => (in_array($get('type'), [CollectionType::RSS->value, CollectionType::WEBSITE->value, CollectionType::CHANNEL->value, CollectionType::PODCAST->value])))
                            ->disabled($cannotConfigureSourcesClosure)
                            ->afterStateUpdated(function (Set $set, $state) {
                                if (!$state) {
                                    $set('auto_transcribe_sources', false);
                                }
                            })
                            ->live()
                        ,
                        Forms\Components\Toggle::make('auto_transcribe_sources')
                            ->label('Automatically Transcribe Sources')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Sources imported for this Collection will be automatically transcribed.'
                            )
                            ->helperText(new HtmlString("
                                Note that this will incur a cost of <strong>{$transcriptionCredits} credits</strong> per transcribed Source.
                            "))
                            ->visible(fn (Get $get) => (in_array($get('type'), [CollectionType::CHANNEL->value, CollectionType::PODCAST->value])))
                        ,
                        Forms\Components\Toggle::make('auto_list_sources')
                            ->label(new HtmlString("List Auto-Imported Sources on <a href='{$socialUrl}' target='_blank' class='hover:underline text-gray-900 dark:text-white font-bold'>Apologist Social ↗</a>"))
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Sources imported for this Collection will be automatically submitted to be listed on Apologist Social without the need to manually list them. However, they will still need to be approved.'
                            )
                            ->visible(fn (Get $get) => (in_array($get('type'), [CollectionType::RSS->value, CollectionType::WEBSITE->value, CollectionType::CHANNEL->value, CollectionType::PODCAST->value])))
                            ->disabled($cannotConfigureSourcesClosure)
                            ->afterStateUpdated(function (Set $set, $state) {
                                if (!$state) {
                                    $set('enforce_source_excerpts', false);
                                }
                            })
                            ->live()
                        ,
                        Forms\Components\Toggle::make('enforce_source_excerpts')
                            ->label(new HtmlString('Enforce Source Excerpt Length Limits'))
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Source descriptions will automatically be truncated so that the entirety of the Source content will not be displayed on Apologist Social.'
                            )
                            ->disabled(fn (Get $get) => ! $get('auto_list_sources'))
                        ,
                        Forms\Components\Toggle::make('auto_categorize_sources')
                            ->label(new HtmlString('Auto-Categorize Imported Sources'))
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Sources automatically imported for this Collection will also be automatically categorized using AI.'
                            )
                            ->default(true)
                            ->visible(fn (Get $get) => (in_array($get('type'), [CollectionType::RSS->value, CollectionType::WEBSITE->value, CollectionType::CHANNEL->value, CollectionType::PODCAST->value])))
                            ->disabled($cannotConfigureSourcesClosure)
                        ,
                    ]),
                Forms\Components\Group::make()
                    ->schema([
                        static::getFormFieldSelect(
                            'enforce_source_categories',
                            Category::where('is_active', true)->whereNotNull('parent_id')->orderBy('name', 'asc')->pluck('name', 'id')->toArray(),
                            'Enforce Source Categories'
                        )
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Sources automatically imported for this Collection will get the selected Categories.'
                            )
                            ->multiple()
                            ->maxItems(3)
                            ->required(false)
                            ->disabled($cannotConfigureSourcesClosure)
                        ,
                        static::getFormFieldSelect('source_image_handling', SourceImageHandling::asOptions())
                            ->label('Source Image Handling')
                            ->enum(SourceImageHandling::class)
                            ->required()
                            ->selectablePlaceholder(false)
                            ->default(SourceImageHandling::INHERIT->value)
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: '
                                    Inherit (default): Use Source image if available; otherwise use Collection image.
                                    Self: Use Source image if available; otherwise use icon placeholder.
                                    Override: Always use Collection image, even if Source image is available.
                                '
                            )
                            ->disabled($cannotConfigureSourcesClosure)
                        ,
                        Forms\Components\Select::make('source_contributor_id')
                            ->label('Override Source Contributor')
                            ->options(fn () => Contributor::approved()->get()->pluck('name', 'id')->toArray())
                            ->searchable()
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Sources will automatically be attributed to the selected Contributor.'
                            )
                            ->disabled($cannotConfigureSourcesClosure)
                            ->live()
                        ,
                        Forms\Components\Select::make('source_contributor_role')
                            ->label('Override Source Contributor Role')
                            ->visible(fn (Get $get) => ! empty($get('source_contributor_id')))
                            ->requiredWith('source_contributor_id')
                            ->options(SourceContributionRole::asOptions())
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'The Source Contributor selected in the above field will be given this role.'
                            )
                        ,
                    ]),
            ])
            ->visible(fn (Get $get) => (in_array($get('type'), ['rss', 'website', 'channel', 'podcast'])))
            ->disabled($restrictEditingClosure)
            ->columns(2);
    }

    protected static function getFormTabScraping(Closure $restrictEditingClosure, Closure $cannotConfigureSourcesClosure): Tabs\Tab
    {
        return Tabs\Tab::make('Web Scraping')
            ->schema([
                static::getFormFieldSourceUrlRestriction(static::$collectionType)
                    ->disabled($cannotConfigureSourcesClosure),
                static::getFormFieldWebUrlSelector('source_url_content_selector', 'content')
                    ->disabled($cannotConfigureSourcesClosure),
                static::getFormFieldWebUrlSelector('source_url_author_selector', 'author')
                    ->disabled($cannotConfigureSourcesClosure),
                static::getFormFieldWebUrlSelector('source_url_published_selector', 'published')
                    ->disabled($cannotConfigureSourcesClosure),
                static::getFormFieldWebUrlSelector('source_url_image_selector', 'image')
                    ->disabled($cannotConfigureSourcesClosure),
                static::getFormFieldSourceTitleStrip()
                    ->disabled($cannotConfigureSourcesClosure),
                static::getFormFieldSourceContentStrip()
                    ->disabled($cannotConfigureSourcesClosure),
            ])
            ->visible(fn (Get $get) => (in_array($get('type'), ['rss', 'website'])))
            ->disabled($restrictEditingClosure)
            ->columns(2);
    }

    protected static function getFormTabCategories(Closure $restrictEditingClosure): Tabs\Tab
    {
        return Tabs\Tab::make('Categories')
            ->schema([
                static::getFormFieldCategoriesNotice('Collection'),
                static::getFormFieldCategories(true),
            ])
            ->disabled($restrictEditingClosure)
            ->columns(2)
            ->visible(fn (?Model $record) => ! is_null($record));
    }

    protected static function getFormTabAdmin(Closure $restrictEditingClosure, bool $canAdminister): Tabs\Tab
    {

        return Tabs\Tab::make('Admin')
            ->schema([
                static::getFormFieldTeam(),
                static::getFormFieldOwner(),
                static::getFormFieldWeight('default_source_weight', 'Default Source Weight'),
                static::getFormFieldFeatured(),
                Forms\Components\Toggle::make('auto_approve_sources')
                    ->label('Auto-Approve Sources')
                    ->visible(fn (Get $get) => (in_array($get('type'), [CollectionType::RSS->value, CollectionType::WEBSITE->value, CollectionType::CHANNEL->value, CollectionType::PODCAST->value])))
                ,
                Forms\Components\Toggle::make('source_url_render_js')
                    ->label('Render Javascript when Scraping')
                    ->visible(fn (Get $get) => (in_array($get('type'), [CollectionType::RSS->value, CollectionType::WEBSITE->value])))
                ,
                Forms\Components\Toggle::make('override_source_restrictions')
                    ->label('Override Source Restrictions')
                ,
                static::getFormFieldType(CollectionType::class)
                    ->default(static::$collectionType)
                    ->live(),
            ])
            ->disabled($restrictEditingClosure)
            ->visible($canAdminister)
            ->columns(2);

    }

    protected static function getFormFieldSourceTitleStrip(): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make('source_title_strip')
            ->label('Remove from Source Titles')
            ->maxLength(100)
            ->hintIcon(
                'heroicon-s-question-mark-circle',
                tooltip: 'Whatever is entered here will automatically be removed from the title of any imported Source.'
            );
    }

    protected static function getFormFieldSourceContentStrip(): Forms\Components\Textarea
    {
        return Forms\Components\Textarea::make('source_url_content_strip')
            ->label('Strip from URL Content')
            ->rows(5)
            ->maxLength(5000)
            ->hintIcon(
                'heroicon-s-question-mark-circle',
                tooltip: 'Whatever is entered here will automatically be removed from the content of any imported Source.'
            );
    }

    protected static function getTableBulkActionsCollections(Table $table): array
    {

        $typePlural = static::$pluralModelLabel;

        $adminAgentActions = Gatekeeper::userCanAdminister(Collection::class) ? [
            static::getTableBulkActionSetAgentWeight('default_source_weight', 'Source Weight', $typePlural, $table),
        ] : [];

        $groups = [

            Tables\Actions\BulkActionGroup::make([
                static::getTableBulkActionEnableSocial($typePlural),
                static::getTableBulkActionDisableSocial($typePlural),
            ])
                ->label('Social'),

            Tables\Actions\BulkActionGroup::make($adminAgentActions)
                ->label('Agent'),

        ];

        if (Gatekeeper::userCanAdminister(static::getModel())) {
            $groups[] = Tables\Actions\BulkActionGroup::make([
                static::getTableBulkActionChangeTeam($typePlural, $table),
                static::getTableBulkActionChangeOwner($typePlural, $table),
            ])
                ->label('Owner');
        }

        return $groups;

    }

    /**
     * @return string[]
     */
    public static function getRelations(): array
    {
        return [
            HistoryRelationManager::class,
        ];
    }

    public static function getApiTransformer(): string
    {
        return CollectionTransformer::class;
    }

    protected static function getAllowedSourceTypes(): array
    {
        return static::$allowedSourceTypes;
    }

    public static function getCollectionType(): string
    {
        return static::$collectionType;
    }

}
