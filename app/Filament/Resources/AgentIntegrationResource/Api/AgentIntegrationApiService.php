<?php

namespace App\Filament\Resources\AgentIntegrationResource\Api;

use App\Filament\Api\ApiService;
use App\Filament\Resources\AgentIntegrationResource;

class AgentIntegrationApiService extends ApiService
{
    protected static ?string $resource = AgentIntegrationResource::class;
    protected static ?string $groupRouteName = 'models';

    /**
     * @return string[]
     */
    protected static function getActions(): array
    {
        return [
            'Pagination',
            'Detail',
        ];
    }
}
