<?php

namespace App\Filament\Resources\AgentIntegrationResource\Api\Handlers;

use App\Filament\Api\Handlers\DetailHandler as BaseDetailHandler;
use App\Filament\Resources\AgentIntegrationResource;

class DetailHandler extends BaseDetailHandler
{
    public static ?string $resource = AgentIntegrationResource::class;

    public static bool $public = true;

    protected static bool $restrictToTeams = false;
}
