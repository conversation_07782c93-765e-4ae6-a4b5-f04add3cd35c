<?php

namespace App\Filament\Resources\AgentIntegrationResource\Api\Handlers;

use App\Filament\Api\Handlers\PaginationHandler as BasePaginationHandler;
use App\Filament\Resources\AgentIntegrationResource;

class PaginationHandler extends BasePaginationHandler
{
    public static ?string $resource = AgentIntegrationResource::class;

    public static bool $public = true;

    protected static bool $restrictToTeams = false;
}
