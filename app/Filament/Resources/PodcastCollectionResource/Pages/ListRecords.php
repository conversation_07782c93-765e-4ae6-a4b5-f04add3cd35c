<?php

namespace App\Filament\Resources\PodcastCollectionResource\Pages;

use App\Filament\Resources\CollectionResource\Pages\ListRecords as BaseListRecords;
use App\Filament\Resources\EpisodeSourceResource;
use App\Filament\Resources\PodcastCollectionResource;
use Filament\Actions\Action;

class ListRecords extends BaseListRecords
{
    protected static string $resource = PodcastCollectionResource::class;

    protected function getHeaderActions(): array
    {
        return array_merge(
            [
                Action::make('podcast_episodes')
                    ->extraAttributes(['id' => btn_id('podcast-episodes')])
                    ->label('Back to Podcast Episodes')
                    ->icon('heroicon-o-arrow-left-circle')
                    ->outlined()
                    ->url(EpisodeSourceResource::getUrl()),
            ],
            parent::getHeaderActions()
        );
    }
}
