<?php

namespace App\Filament\Resources\WebsiteCollectionResource\Pages;

use App\Filament\Resources\CollectionResource\Pages\ListRecords as BaseListRecords;
use App\Filament\Resources\UrlSourceResource;
use App\Filament\Resources\WebsiteCollectionResource;
use Filament\Actions\Action;

class ListRecords extends BaseListRecords
{
    protected static string $resource = WebsiteCollectionResource::class;

    protected function getHeaderActions(): array
    {
        return array_merge(
            [
                Action::make('urls')
                    ->extraAttributes(['id' => btn_id('urls')])
                    ->label('Back to Web Pages')
                    ->icon('heroicon-o-arrow-left-circle')
                    ->outlined()
                    ->url(UrlSourceResource::getUrl()),
            ],
            parent::getHeaderActions()
        );
    }
}
