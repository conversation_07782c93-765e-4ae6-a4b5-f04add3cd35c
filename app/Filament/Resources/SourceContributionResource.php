<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SourceContributionResource\Api\Transformers\SourceContributionTransformer;
use App\Filament\Traits\CanBeLocked;
use App\Models\SourceContribution;
use <PERSON>zhanSalleh\FilamentShield\Contracts\HasShieldPermissions;

class SourceContributionResource extends Resource implements HasShieldPermissions
{
    use CanBeLocked;

    protected static ?string $model = SourceContribution::class;

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [];
    }

    public static function getApiTransformer(): string
    {
        return SourceContributionTransformer::class;
    }

    public static function getPermissionPrefixes(): array
    {
        return [];
    }
}
