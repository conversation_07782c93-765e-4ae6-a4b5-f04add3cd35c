<?php

namespace App\Filament\Resources;

use App\Enums\CollectionType;
use App\Enums\SourceType;

class ChannelCollectionResource extends CollectionResource
{
    protected static ?string $navigationIcon = 'heroicon-s-video-camera';

    protected static ?string $collectionType = CollectionType::CHANNEL->value;

    protected static ?array $allowedSourceTypes = [
        SourceType::YOUTUBE->value,
    ];

    protected static ?string $modelLabel = 'Video Channel / Playlist';

    protected static ?string $pluralModelLabel = 'Video Channels & Playlists';

    protected static ?string $navigationLabel = 'Video Channels & Playlists';

    protected static ?string $slug = 'channels';

    protected static string $createLabel = 'add';

    public static function getPermissionPrefixes(): array
    {
        return [];
    }
}
