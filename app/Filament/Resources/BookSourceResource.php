<?php

namespace App\Filament\Resources;

use App\Enums\CollectionType;
use App\Enums\SourceType;
use App\Filament\Resources\BookSourceResource\Pages\ImportBooks;
use Exception;
use Filament\Resources\Pages\PageRegistration;

class BookSourceResource extends SourceResource
{
    protected static ?string $navigationIcon = 'heroicon-s-book-open';

    protected static ?string $sourceType = SourceType::BOOK->value;

    protected static ?array $allowedCollectionTypes = [
        CollectionType::LIST->value,
        CollectionType::ANTHOLOGY->value,
        CollectionType::SERIES->value,
    ];

    protected static ?string $modelLabel = 'Book';

    protected static ?string $pluralModelLabel = 'Books';

    protected static ?string $navigationLabel = 'Books';

    protected static ?string $slug = 'books';

    protected static string $createLabel = 'add';

    public static function getPermissionPrefixes(): array
    {
        return [];
    }

    /**
     * @return array|PageRegistration[]
     *
     * @throws Exception
     */
    public static function getPages(): array
    {
        return array_merge(
            parent::getPages(),
            ['import' => ImportBooks::route('/import')]
        );
    }
}
