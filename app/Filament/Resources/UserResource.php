<?php

namespace App\Filament\Resources;

use App\Filament\RelationManagers\HistoryRelationManager;
use App\Filament\Resources\Resource as BaseResource;
use App\Filament\Resources\UserResource\Api\Transformers\UserTransformer;
use App\Filament\Resources\UserResource\RelationManagers\AuthenticationLogRelationManager;
use App\Filament\Resources\UserResource\RelationManagers\EmailLogRelationManager;
use App\Filament\Resources\UserResource\RelationManagers\MembershipRelationManager;
use App\Filament\Resources\UserResource\RelationManagers\RoleRelationManager;
use App\Filament\Traits\CanBeAdministered;
use App\Filament\Traits\CanBeLocked;
use App\Models\User;
use App\Services\Gatekeeper;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;

class UserResource extends BaseResource implements HasShieldPermissions
{
    use CanBeAdministered;
    use CanBeLocked;

    protected static ?string $model = User::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-s-user-circle';

    protected static array $customPermissions = [
        'impersonate',
    ];

    public static function form(Form $form): Form
    {
        $restrictEditingClosure = fn ($record) => restrict_editing($record);

        return $form
            ->schema([
                Fieldset::make('User Information')
                    ->schema([
                        Forms\Components\Group::make()
                            ->schema([
                                static::getFormFieldName(false, false),
                                static::getFormFieldEmail()
                                    ->unique(ignoreRecord: true),
                            ])
                        ,
                        Forms\Components\Group::make()
                            ->schema([
                                static::getFormFieldAvatar('users', 'Profile Photo'),
                            ])
                        ,
                        static::getFormFieldRelation('current_team_id', 'currentTeam', 'Current Team')
                            ->visible(Gatekeeper::userCanAdminister(User::class))
                            ->required()
                        ,
                        //                        static::getFormFieldPassword(),
                    ]),
            ])
            ->disabled($restrictEditingClosure);
    }

    /**
     * @throws \Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                static::getTableColumnName('SUSPENDED'),
                static::getTableColumnEmail()
                    ->visibleFrom('md'),
                Tables\Columns\TextColumn::make('currentTeam.name')
                    ->label('Current Team')
                    ->sortable()
                    ->searchable(),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterRelation('ownedTeams', 'Owner of Team(s)'),
                static::getTableFilterRelation('teams', 'Member of Team(s)'),
                static::getTableFilterTernary('email_verified_at', 'Verified'),
                static::getTableFilterSuspended(),
            ])
            ->actions([
                //                Impersonate::make()
                //                    ->redirectTo(route('filament.app.pages.dashboard', ['tenant'=>auth()->user()->currentTeam->id])),
                static::decorateLockAction(Action::make('Lock')),
                static::decorateUnlockAction(Action::make('Unlock')),
                static::getTableActionEditRow(),
            ])
            ->bulkActions(static::getTableBulkActions())
            ->paginated(static::$pageOptions)
            ->defaultPaginationPageOption(static::$defaultPageOption);
    }

    /**
     * @return string[]
     */
    public static function getRelations(): array
    {
        return [
            MembershipRelationManager::class,
            AuthenticationLogRelationManager::class,
            EmailLogRelationManager::class,
            HistoryRelationManager::class,
        ];
    }

    public static function getApiTransformer(): string
    {
        return UserTransformer::class;
    }
}
