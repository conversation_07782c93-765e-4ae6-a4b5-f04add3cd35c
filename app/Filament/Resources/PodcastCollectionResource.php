<?php

namespace App\Filament\Resources;

use App\Enums\CollectionType;
use App\Enums\SourceType;

class PodcastCollectionResource extends CollectionResource
{
    protected static ?string $navigationIcon = 'heroicon-s-microphone';

    protected static ?string $collectionType = CollectionType::PODCAST->value;

    protected static ?array $allowedSourceTypes = [
        SourceType::EPISODE->value,
    ];

    protected static ?string $modelLabel = 'Podcast Feed';

    protected static ?string $pluralModelLabel = 'Podcast Feeds';

    protected static ?string $navigationLabel = 'Podcast Feeds';

    protected static ?string $slug = 'podcasts';

    protected static string $createLabel = 'add';

    public static function getPermissionPrefixes(): array
    {
        return [];
    }
}
