<?php

namespace App\Filament\Resources\ContributorResource\Pages;

use App\Filament\Pages\App\Analytics\ContributorAnalytics;
use App\Filament\Resources\BookSourceResource\Pages\ImportBooks;
use App\Filament\Resources\ContributorResource;
use App\Filament\Templates\EditRecord as BaseEditRecord;
use App\Models\Contributor;
use App\Models\SourceContribution;
use App\Services\Gatekeeper;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Forms;
use Filament\Notifications\Notification;

class EditRecord extends BaseEditRecord
{
    protected static string $resource = ContributorResource::class;

    /**
     * @return array|Action[]|ActionGroup[]
     */
    protected function getHeaderActions(): array
    {
        return array_merge(
            $this->getModerationHeaderActions(),
            Gatekeeper::userCan('merge', static::$resource::getModel()) ? [static::getMergeHeaderAction()] : [],
            Gatekeeper::userCan('import', static::$resource::getModel()) ? [static::getImportBooksHeaderAction()] : [],
            [
                static::getAnalyzeHeaderAction(ContributorAnalytics::class),
                static::decorateViewListingAction(Action::make('Preview'))
                    ->extraAttributes(['class' => 'ml-0']),
                static::decorateViewReferenceAction(Action::make('Reference')),
            ],
            parent::getHeaderActions(),
        );
    }

    protected static function getImportBooksHeaderAction(): Action
    {
        return Action::make('import')
            ->extraAttributes(['id' => btn_id('import-books')])
            ->label('Import Bibliography')
            ->icon('heroicon-s-cloud-arrow-down')
            ->url(fn ($record) => ImportBooks::getUrl().($record ? '?contributor_id='.$record->id : ''))
            ->visible(fn ($record) => $record && $record->is_approved && Gatekeeper::userCan('import', static::$resource::getModel()));
    }

    protected static function getMergeHeaderAction(): Action
    {
        return Action::make('merge')
            ->extraAttributes(['id' => btn_id('merge-contributors')])
            ->label('Merge')
            ->icon('heroicon-s-document-arrow-down')
            ->color('warning')
            ->outlined()
            ->requiresConfirmation()
            ->form([
                Forms\Components\Select::make('contributor_id')
                    ->label('Merge to the Following Contributor:')
                    ->required()
                    ->options(fn ($record) => Contributor::where('id', '!=', $record->id)->get()->pluck('name', 'id')->toArray())
                    ->searchable(),
            ])
            ->modalHeading('Merge this Contributer to Another Contributor')
            ->modalIcon('heroicon-s-document-arrow-down')
            ->action(function (array $data, $record, $livewire): void {

                $fromId = $record->id;
                $toId = $data['contributor_id'];

                SourceContribution::where('contributor_id', $fromId)->update(['contributor_id' => $toId]);
                $record->update(['merged_contributor_id' => $toId]);
                $record->delete();

                Notification::make()
                    ->title('Merge successful')
                    ->body("This Contributor's Contributions have been moved to the new Contributor, and this Contributor is now archived.")
                    ->warning()
                    ->icon('heroicon-s-document-arrow-down')
                    ->send();

                $livewire->dispatch('$refresh');

            })
            ->visible(fn () => Gatekeeper::userCan('merge', static::$resource::getModel()));
    }
}
