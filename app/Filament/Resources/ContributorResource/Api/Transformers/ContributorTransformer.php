<?php

namespace App\Filament\Resources\ContributorResource\Api\Transformers;

use App\Filament\Api\Transformer;
use Illuminate\Http\Request;

class ContributorTransformer extends Transformer
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    /*public function toArray(Request $request): array
    {
        $attrs = $this->resource->toArray();
        $attrs['full_name'] = $this->resource->name;
        return $this->transformResource($attrs, $this->resource);
    }*/

}
