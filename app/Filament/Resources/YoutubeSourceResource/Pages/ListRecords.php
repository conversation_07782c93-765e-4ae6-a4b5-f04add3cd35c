<?php

namespace App\Filament\Resources\YoutubeSourceResource\Pages;

use App\Filament\Resources\ChannelCollectionResource;
use App\Filament\Resources\SourceResource\Pages\ListRecords as BaseListRecords;
use App\Filament\Resources\YoutubeSourceResource;
use Filament\Actions\Action;

class ListRecords extends BaseListRecords
{
    protected static string $resource = YoutubeSourceResource::class;

    protected function getHeaderActions(): array
    {
        return array_merge(
            [
                Action::make('channels')
                    ->extraAttributes(['id' => btn_id('channels')])
                    ->label('Channels & Playlists')
                    ->icon('heroicon-s-video-camera')
                    ->outlined()
                    ->url(ChannelCollectionResource::getUrl()),
                static::getImportHeaderAction(),
            ],
            parent::getHeaderActions()
        );
    }
}
