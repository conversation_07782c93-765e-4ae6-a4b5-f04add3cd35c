<?php

namespace App\Filament\Resources\MediaSourceResource\Pages;

use App\Filament\Resources\MediaSourceResource;
use App\Filament\Resources\SourceResource\Pages\ListRecords as BaseListRecords;

class ListRecords extends BaseListRecords
{
    protected static string $resource = MediaSourceResource::class;

    protected function getHeaderActions(): array
    {
        return array_merge(
            [
                static::getSeriesHeaderAction(),
            ],
            parent::getHeaderActions()
        );
    }
}
