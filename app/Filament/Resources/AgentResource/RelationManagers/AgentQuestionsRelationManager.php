<?php

namespace App\Filament\Resources\AgentResource\RelationManagers;

use App\Filament\Templates\RelationManager;
use App\Filament\Traits\HasActions;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFields;
use App\Filament\Traits\HasFilters;
use Exception;
use Filament\Facades\Filament;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\Concerns\Translatable;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;
use Livewire\Attributes\Reactive;
use Illuminate\Database\Eloquent\Model;

class AgentQuestionsRelationManager extends RelationManager
{
    use HasActions;
    use HasColumns;
    use HasFields;
    use HasFilters;
    use Translatable;

    protected static string $relationship = 'questions';

    protected static ?string $title = 'Questions';

    protected static bool $isLazy = false;

    #[Reactive]
    public ?string $activeLocale = null;

    protected $listeners = ['refreshRelation' => '$refresh'];

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return get_current_team()->getPlanOption('agent.custom_questions');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('question')
                    ->label('Question')
                    ->required()
                    ->columnSpan(2)
                    ->suffixAction(
                        Action::make('translate')
                            ->extraAttributes(['class' => btn_id('translate-question')])
                            ->icon('heroicon-s-language')
                            ->color('white')
                            ->action(function (Set $set, $state) {
                                $set('question', translate_text($state, $this->getActiveActionsLocale()));
                            })
                    )
                ,
                static::getFormFieldActive()
                    ->columnSpan(2),
            ]);
    }

    /**
     * @throws Exception
     */
    public function table(Table $table): Table
    {
        return $table
            ->heading(new HtmlString(Blade::render('Sample Questions <x-heroicon-s-language class="w-5 h-5 inline text-gray-400 dark:text-gray-500" />')))
            ->reorderable('seq')
            ->defaultSort('seq')
            ->recordTitleAttribute('question.question')
            ->columns([
                TextColumn::make('question'),
                static::getTableColumnActive(),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterArchived(),
            ])
            ->headerActions([
                Tables\Actions\Action::make('translate')
                    ->extraAttributes(['id' => btn_id('translate-all-questions')])
                    ->label('Translate Questions')
                    ->icon('heroicon-s-language')
                    ->outlined()
                    ->action(function () {
                        $this->getOwnerRecord()->questions->each(function ($question) {
                            $previousTranslation = $question->getTranslation('question', $this->getActiveActionsLocale());
                            $newTranslation = translate_text($previousTranslation, $this->getActiveActionsLocale());
                            $question->setTranslation('question', $this->getActiveActionsLocale(), $newTranslation);
                            $question->save();
                        });
                        Notification::make()
                            ->title('Questions translated')
                            ->success()
                            ->send();
                    })
                    ->after(function ($livewire) {
                        $livewire->dispatch('refreshRelation');
                    })
                    ->hidden(restrict_editing($this->getOwnerRecord())),
                static::getTableCreateAction('Question')
                    ->hidden(restrict_editing($this->getOwnerRecord())),
            ])
            ->actions(static::getRelationManagerTableActions('Question', $this->getOwnerRecord()))
            ->modifyQueryUsing(fn (Builder $query) => $query->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]))
        ;
    }
}
