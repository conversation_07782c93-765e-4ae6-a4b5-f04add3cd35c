<?php

namespace App\Filament\Resources\AgentResource\Actions;

use App\Models\Agent;
use Filament\Actions\LocaleSwitcher as BaseLocaleSwitcher;

class LocaleSwitcher extends BaseLocaleSwitcher
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->label(__('filament-spatie-laravel-translatable-plugin::actions.active_locale.label'));

        $this->setTranslatableLocaleOptions();
    }

    public function setTranslatableLocaleOptions(): static
    {
        $this->options(function (): array {

            $livewire = $this->getLivewire();

            if (! method_exists($livewire, 'getTranslatableLocales')) {
                return [];
            }

            $data = @$livewire->form->getRawState();

            return Agent::getSupportedLanguages(
                $data['supported_languages'] ?? null,
                $data['auto_translate'] ?? null,
                $data['model_id'] ?? null,
                $livewire->form->getRecord()
            );

        });

        return $this;
    }
}
