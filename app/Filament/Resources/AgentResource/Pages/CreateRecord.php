<?php

namespace App\Filament\Resources\AgentResource\Pages;

use App\Filament\Resources\AgentResource;
use App\Filament\Templates\CreateRecord as BaseCreateRecord;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Resources\Pages\CreateRecord\Concerns\Translatable;

class CreateRecord extends BaseCreateRecord
{
    use Translatable;

    protected static string $resource = AgentResource::class;

    protected static bool $canCreateAnother = false;

    /**
     * @return array|Action[]|ActionGroup[]
     */
    protected function getHeaderActions(): array
    {
        return array_merge(parent::getHeaderActions(), [Actions\LocaleSwitcher::make()]);
    }
}
