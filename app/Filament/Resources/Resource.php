<?php

namespace App\Filament\Resources;

use App\Filament\Traits\CanBeAdministered;
use App\Filament\Traits\CanBeImported;
use App\Filament\Traits\CanBeLocked;
use App\Filament\Traits\CanBeModerated;
use App\Filament\Traits\CanBeOrdered;
use App\Filament\Traits\CanBePromoted;
use App\Filament\Traits\CanBeReplicated;
use App\Filament\Traits\HasActions;
use App\Filament\Traits\HasBulkActions;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFields;
use App\Filament\Traits\HasFilters;
use Exception;
use Filament\Panel;
use Filament\Resources\Pages\PageRegistration;
use Filament\Resources\Resource as BaseResource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class Resource extends BaseResource
{
    use HasActions;
    use HasBulkActions;
    use HasColumns;
    use HasFields;
    use HasFilters;

    protected array $listeners = ['refresh' => '$refresh'];

    protected static string $createLabel = 'add';

    protected static string $createLabelPast = 'added';

    protected static array $pageOptions = [10, 25, 50, 100];

    protected static int $defaultPageOption = 25;

    protected const TRAIT_PERMISSIONS = [
        CanBePromoted::class => 'promote',
        CanBeModerated::class => 'moderate',
        CanBeOrdered::class => 'reorder',
        CanBeImported::class => 'import',
        CanBeReplicated::class => 'replicate',
        CanBeAdministered::class => 'administer',
        CanBeLocked::class => 'lock',
    ];

    protected static array $customPermissions = [];

    public static function getCreateLabel(): string
    {
        return static::$createLabel;
    }

    public static function getCreateLabelPast(): string
    {
        return static::$createLabelPast;
    }

    /**
     * @return array|PageRegistration[]
     *
     * @throws Exception
     */
    public static function getPages(): array
    {
        $reflect = new \ReflectionClass(get_called_class());
        $resourceDir = $reflect->getShortName();
        $path = __NAMESPACE__."\\{$resourceDir}\\Pages\\";
        if ($resourceDir != 'Resource') {
            return [
                'index' => call_user_func("{$path}ListRecords::route", '/', ['tenant' => get_current_team_id()]),
                'create' => call_user_func("{$path}CreateRecord::route", '/create', ['tenant' => get_current_team_id()]),
                'edit' => call_user_func("{$path}EditRecord::route", '/{record}/edit', ['tenant' => get_current_team_id()]),
                'history' => call_user_func("{$path}ShowHistory::route", '/{record}/history', ['tenant' => get_current_team_id()]),
            ];
        }

        return [];

    }

    /**
     * @throws Exception
     */
    public static function routes(Panel $panel): void
    {
        if ($panel->getId() != env('APP_API_VERSION')) {
            parent::routes($panel);
        }
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getPermissionPrefixes(): array
    {
        $prefixes = config('filament-shield.permission_prefixes.resource');
        foreach (static::TRAIT_PERMISSIONS as $traitClass => $permission) {
            if (uses_trait(static::class, $traitClass)) {
                $prefixes[] = $permission;
            }
        }

        return array_merge(
            $prefixes,
            static::$customPermissions
        );
    }
}
