<?php

namespace App\Filament\Resources\ClassificationResource\Pages;

use App\Filament\Resources\ClassificationResource;
use App\Filament\Templates\ListRecords as BaseListRecords;
use App\Filament\Traits\HasRestrictedAccess;

class ListRecords extends BaseListRecords
{
    use HasRestrictedAccess;

    protected static string $resource = ClassificationResource::class;

    protected static string $createLabel = 'create';
}
