<?php

namespace App\Filament\Resources\ClassificationResource\Pages;

use App\Filament\Resources\ClassificationResource;
use App\Filament\Templates\CreateRecord as BaseCreateRecord;
use App\Filament\Traits\HasRestrictedAccess;

class CreateRecord extends BaseCreateRecord
{
    use HasRestrictedAccess;

    protected static string $resource = ClassificationResource::class;

    /**
     * @return array|mixed[]
     */
    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data['description'] = strip_paragraphs($data['description']);

        return $data;
    }
}
