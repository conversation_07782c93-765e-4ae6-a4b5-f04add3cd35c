<?php

namespace App\Filament\Resources\ClassificationResource\Api\Handlers;

use App\Filament\Api\Handlers\DetailHandler as BaseDetailHandler;
use App\Filament\Resources\ClassificationResource;

class DetailHandler extends BaseDetailHandler
{
    public static ?string $resource = ClassificationResource::class;

    public static bool $public = true;

    protected static bool $restrictToTeams = false;
}
