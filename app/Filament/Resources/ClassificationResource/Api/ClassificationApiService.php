<?php

namespace App\Filament\Resources\ClassificationResource\Api;

use App\Filament\Api\ApiService;
use App\Filament\Resources\ClassificationResource;

class ClassificationApiService extends ApiService
{
    protected static ?string $resource = ClassificationResource::class;

    /**
     * @return string[]
     */
    protected static function getActions(): array
    {
        return [
            'Pagination',
            'Detail',
        ];
    }
}
