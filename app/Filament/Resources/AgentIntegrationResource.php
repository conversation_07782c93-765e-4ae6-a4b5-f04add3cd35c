<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AgentIntegrationResource\Api\Transformers\AgentIntegrationTransformer;
use App\Models\AgentIntegration;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;

class AgentIntegrationResource extends Resource implements HasShieldPermissions
{

    protected static ?string $model = AgentIntegration::class;

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [];
    }

    public static function getApiTransformer(): string
    {
        return AgentIntegrationTransformer::class;
    }

    public static function getPermissionPrefixes(): array
    {
        return [];
    }
}
