<?php

namespace App\Filament\Resources\AgentIntegrationPlatformResource\Pages;

use App\Filament\Resources\AgentIntegrationPlatformResource;
use App\Filament\Templates\CreateRecord as BaseCreateRecord;
use App\Filament\Traits\HasRestrictedAccess;

class CreateRecord extends BaseCreateRecord
{
    use HasRestrictedAccess;

    protected static string $resource = AgentIntegrationPlatformResource::class;

}
