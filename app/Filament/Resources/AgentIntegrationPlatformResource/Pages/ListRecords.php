<?php

namespace App\Filament\Resources\AgentIntegrationPlatformResource\Pages;

use App\Filament\Resources\AgentIntegrationPlatformResource;
use App\Filament\Templates\ListRecords as BaseListRecords;
use App\Filament\Traits\HasRestrictedAccess;

class ListRecords extends BaseListRecords
{
    use HasRestrictedAccess;

    protected static string $resource = AgentIntegrationPlatformResource::class;

    protected static string $createLabel = 'add';
}
