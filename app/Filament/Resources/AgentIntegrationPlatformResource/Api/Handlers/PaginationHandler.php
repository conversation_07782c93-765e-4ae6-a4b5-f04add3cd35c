<?php

namespace App\Filament\Resources\AgentIntegrationPlatformResource\Api\Handlers;

use App\Filament\Api\Handlers\PaginationHandler as BasePaginationHandler;
use App\Filament\Resources\AgentIntegrationPlatformResource;

class PaginationHandler extends BasePaginationHandler
{
    public static ?string $resource = AgentIntegrationPlatformResource::class;

    public static bool $public = true;

    protected static bool $restrictToTeams = false;
}
