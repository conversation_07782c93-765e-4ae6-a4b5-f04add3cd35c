<?php

namespace App\Filament\Resources\AgentIntegrationPlatformResource\Api\Handlers;

use App\Filament\Api\Handlers\DetailHandler as BaseDetailHandler;
use App\Filament\Resources\AgentIntegrationPlatformResource;

class DetailHandler extends BaseDetailHandler
{
    public static ?string $resource = AgentIntegrationPlatformResource::class;

    public static bool $public = true;

    protected static bool $restrictToTeams = false;
}
