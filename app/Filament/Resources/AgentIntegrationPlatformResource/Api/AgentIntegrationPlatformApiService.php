<?php

namespace App\Filament\Resources\AgentIntegrationPlatformResource\Api;

use App\Filament\Api\ApiService;
use App\Filament\Resources\AgentIntegrationPlatformResource;

class AgentIntegrationPlatformApiService extends ApiService
{
    protected static ?string $resource = AgentIntegrationPlatformResource::class;

    /**
     * @return string[]
     */
    protected static function getActions(): array
    {
        return [
            'Pagination',
            'Detail',
        ];
    }
}
