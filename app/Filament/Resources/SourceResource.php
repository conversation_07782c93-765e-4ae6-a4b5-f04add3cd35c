<?php

namespace App\Filament\Resources;

use App\Enums\SourceMediaStatus;
use App\Enums\SourceType;
use App\Filament\RelationManagers\HistoryRelationManager;
use App\Filament\Resources\Resource as BaseResource;
use App\Filament\Resources\SourceResource\Api\Transformers\SourceTransformer;
use App\Filament\Resources\SourceResource\RelationManagers\ContributionsRelationManager;
use App\Filament\Traits\CanBeAdministered;
use App\Filament\Traits\CanBeImported;
use App\Filament\Traits\CanBeLocked;
use App\Filament\Traits\CanBeModerated;
use App\Filament\Traits\CanBePromoted;
use App\Jobs\ManageListingSearchDocumentJob;
use App\Jobs\ManageSourceRagDocumentJob;
use App\Models\Category;
use App\Models\Collection;
use App\Models\Contributor;
use App\Models\Model;
use App\Models\Source;
use App\Models\SourceContribution;
use App\Models\Tag;
use App\Services\Gatekeeper;
use App\Services\Labeller;
use Awcodes\Shout\Components\Shout;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Closure;
use Exception;
use Filament\Forms;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\SpatieTagsInput;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\SpatieTagsColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;
use Throwable;

class SourceResource extends BaseResource implements HasShieldPermissions
{
    use CanBeAdministered;
    use CanBeImported;
    use CanBeLocked;
    use CanBeModerated;
    use CanBePromoted;

    protected static ?string $model = Source::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-s-document-text';

    protected static ?string $sourceType = null;

    protected static ?string $pluralModelLabel = 'Sources';

    protected static ?array $allowedCollectionTypes = [];

    protected static array $customPermissions = [
        'categorize',
        'categorize_unlimited',
    ];

    public static function getSourceType(): ?string
    {
        return static::$sourceType;
    }

    public static function getEloquentQuery(): Builder
    {
        $qry = parent::getEloquentQuery();
        if (! is_null(static::$sourceType)) {
            $qry->where('type', static::$sourceType);
        }

        return $qry;
    }

    public static function form(Form $form): Form
    {

        $restrictEditingClosure = fn ($record) => restrict_editing($record);
        $canAdminister = Gatekeeper::userCanAdminister(Source::class);

        return $form
            ->schema([

                Tabs::make('Tabs')
                    ->tabs([
                        static::getFormTabSummary($restrictEditingClosure, $canAdminister),
                        static::getFormTabListing($restrictEditingClosure),
                        static::getFormTabCategories($restrictEditingClosure),
                        static::getFormTabAgent($restrictEditingClosure),
                        static::getFormTabScraping($restrictEditingClosure),
                        static::getFormTabAdmin($restrictEditingClosure, $canAdminister),
                    ])
                    ->persistTabInQueryString()
                    ->id('source'),

            ])
            ->columns(1);

    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                static::$isScopedToTenant = false;
                if (! Gatekeeper::userCanAdminister(static::getModel())) {
                    $query->whereBelongsTo(get_current_team());
                }
            })
            ->columns([
                static::getTableColumnName(),
                static::getTableColumnType()
                    ->visible(is_null(static::$sourceType))
                    ->visibleFrom('md'),
                static::getTableColumnActive('social_active', 'Social Status')
                    ->visibleFrom('md'),
                static::getTableColumnActive('agent_active', 'Agent Status')
                    ->visibleFrom('md'),
                static::getTableColumnApprovalStatus()
                    ->visibleFrom('xl'),
                SpatieTagsColumn::make('tags')
                    ->visibleFrom('xl')
                ,
                Tables\Columns\TextColumn::make('published_on')
                    ->label('Published')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
                static::getTableColumnFeaturedSequence(),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                static::getTableFilterStatus(),
                static::getTableFilterType(SourceType::asOptions())
                    ->visible(is_null(static::$sourceType)),
                static::getTableFilterLanguage(),
                static::getTableFilterRelation('collections')
                    ->relationship(
                        'collections',
                        'name',
                        fn (Builder $query) => $query->whereIn('type', static::$allowedCollectionTypes)
                    ),
                static::getTableFilterRelation('tags')
                    ->relationship(
                        'tags',
                        'name',
                        fn (Builder $query) => $query->team()
                    ),
                static::getTableFilterRelation('categories'),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
                static::getTableFilterContributors(),
                static::getTableFilterBoolean('is_locked', 'Locked', 'Unlocked'),
                static::getTableFilterBoolean('agent_active', 'Apologist Agent'),
                static::getTableFilterIndexed(),
                static::getTableFilterSelect('weight', config('agent.weights'), 'Weight')
                    ->visible(Gatekeeper::userCanAdminister(Source::class)),
                static::getTableFilterBoolean('social_active', 'Apologist Social'),
                static::getTableFilterFeatured(),
                static::getTableFilterArchived(),
                static::getTableFilterTeam(),
                static::getTableFilterOwner(),
            ])
            ->filtersFormColumns(4)
            ->actions([
                static::decorateLockAction(Tables\Actions\Action::make('Lock')),
                static::decorateUnlockAction(Tables\Actions\Action::make('Unlock')),
                static::decorateViewListingAction(Tables\Actions\Action::make('Preview')),
                static::decorateViewReferenceAction(Tables\Actions\Action::make('Reference')),
                static::getTableActionEditRow(),
            ])
            ->bulkActions(array_merge(static::getTableBulkActions(), static::getTableBulkActionsSources($table)))
            ->paginated(static::$pageOptions)
            ->defaultPaginationPageOption(static::$defaultPageOption);
    }

    protected static function getFormTabSummary(Closure $restrictEditingClosure, bool $canAdminister): Tabs\Tab
    {

        $fields = array_merge(

            [
                static::getFormNoticePendingApproval('Source'),
                static::getFormFieldName(),
                static::getFormFieldWebUrl(),
                static::getFormFieldLanguage(),
                static::getFormFieldSubtitle(),
                static::getFormFieldCollections(static::getAllowedCollectionTypes()),
            ],

            static::getFormFieldsDateOrYear('written', 'Written', [SourceType::URL->value, SourceType::MEDIA->value, SourceType::YOUTUBE->value, SourceType::EPISODE->value]),
            static::getFormFieldsDateOrYear('published', 'Published'),

            [
                static::getFormFieldExternalId('ASIN')
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'Amazon.com Product ID'
                    )
                    ->visible(static::$sourceType == SourceType::BOOK->value),
                static::getFormFieldClassification(),
                SpatieTagsInput::make('tags')
                    ->reorderable()
                    ->suggestions(fn () => Tag::query()->team()->pluck('name')->all())
                ,
            ],

        );

        if (!$canAdminister) {
            $fields[] = Forms\Components\Hidden::make('type')
                ->default(static::$sourceType);
        }

        return Tabs\Tab::make('Summary')
            ->schema($fields)
            ->disabled($restrictEditingClosure)
            ->columns(2);

    }

    protected static function getFormTabListing(Closure $restrictEditingClosure): Tabs\Tab
    {
        $restrictPromotionClosure = fn (?Model $record) => ! Gatekeeper::userCan('promote', Source::class, $record);
        $socialUrl = env('SOCIAL_URL');

        return Tabs\Tab::make('Listing')
            ->schema([

                Forms\Components\Group::make()
                    ->schema([
                        static::getFormFieldSocialActive($restrictPromotionClosure),
                        static::getFormFieldSlug()
                            ->columnSpan(1),
                        static::getFormFieldReferralUrl()
                            ->columnSpan(1),
                    ])
                    ->columnSpan(['default' => 2, 'md' => 1]),
                Forms\Components\Group::make()
                    ->schema([
                        static::getSocialReindexAction($restrictPromotionClosure),
                        static::getFormFieldImage('sources/images')
                            ->disabled(fn (?Model $record) => $record ? $record->imageIsOverridden() : false)
                            ->helperText(function (?Model $record) {

                                if ($record && ($parentCollection = $record->getImageHandlingCollection())) {
                                    $collectionUrl = CollectionResource::getUrl('edit', ['record' => $parentCollection->id]);

                                    return $record->imageIsOverridden() ?
                                        new HtmlString("This Source's image is overridden by the <a href='{$collectionUrl}' class='underline text-white'>{$parentCollection->name}</a> Collection and cannot be changed here.") :
                                        new HtmlString("This Source's image will be inherited from the <a href='{$collectionUrl}' class='underline text-white'>{$parentCollection->name}</a> Collection if not set here.");
                                } else {
                                    return null;
                                }
                            })
                            ->columnSpan(1),
                    ])
                    ->columnSpan(['default' => 2, 'md' => 1]),
                static::getFormFieldBlockRichText(),
                static::getFormFieldDescriptionActions(static::$sourceType),
            ])
            ->disabled($restrictEditingClosure)
            ->columns(2);
    }

    protected static function getFormTabCategories(Closure $restrictEditingClosure): Tabs\Tab
    {
        $restrictCategorizeClosure = fn ($record) => ! Gatekeeper::userCan('categorize', Source::class);

        return Tabs\Tab::make('Categories')
            ->schema([
                static::getFormFieldCategoryActions($restrictCategorizeClosure),
                static::getFormFieldCategories()
                    ->label(false)
                    ->disabled($restrictCategorizeClosure),
            ])
            ->disabled($restrictEditingClosure)
            ->columns(2);
    }

    protected static function getFormTabAgent(Closure $restrictEditingClosure): Tabs\Tab
    {
        $disableImportSettingsClosure = fn (Get $get) => $get('inherit_importer_settings');
        return Tabs\Tab::make('Agent')
            ->schema([

                static::getFormNoticeAgent(),
                static::getFormNoticeAgentMediaStatus(),

                static::getFormFieldAgentTerms(),
                static::getFormActionsAgent(),
                static::getFormFieldAgentActiveToggle(),

                static::getFormFieldMediaUrl(),
                static::getFormFieldMedia(),
                static::getFormFieldText(),

                static::getFormFieldAgentHiddenToggle(),

            ])
            ->disabled($restrictEditingClosure)
            ->columns(2);
    }

    protected static function getFormTabScraping(Closure $restrictEditingClosure): Tabs\Tab
    {
        $disableImportSettingsClosure = fn (Get $get) => $get('inherit_importer_settings');
        return Tabs\Tab::make('Web Scraping')
            ->schema([

                static::getFormFieldActive(
                    '
                        Use the import settings of the Collection that this Source belongs to.
                        Disable this option to override import settings for this specific Source.
                        Be sure to re-index this Source after making changes.
                    ',
                    'inherit_importer_settings',
                    'Inherit Import Settings from Collection'
                )
                    ->visible(fn ($record) => $record?->importerCollection)
                    ->live()
                ,

                static::getFormFieldWebUrlSelector('url_content_selector', 'content')
                    ->visible(fn (Get $get) => in_array($get('type'), ['url']))
                    ->disabled($disableImportSettingsClosure)
                    ->formatStateUsing(fn ($record) => !is_null($record) ? $record->getRawOriginal('url_content_selector') : null)
                ,

                static::getFormFieldWebUrlSelector('url_author_selector', 'author')
                    ->visible(fn (Get $get) => in_array($get('type'), ['url']))
                    ->disabled($disableImportSettingsClosure)
                    ->formatStateUsing(fn ($record) => !is_null($record) ? $record->getRawOriginal('url_author_selector') : null)
                ,

                static::getFormFieldWebUrlSelector('url_published_selector', 'published')
                    ->visible(fn (Get $get) => in_array($get('type'), ['url']))
                    ->disabled($disableImportSettingsClosure)
                    ->formatStateUsing(fn ($record) => !is_null($record) ? $record->getRawOriginal('url_published_selector') : null)
                ,

                static::getFormFieldWebUrlSelector('url_image_selector', 'image')
                    ->visible(fn (Get $get) => in_array($get('type'), ['url']))
                    ->disabled($disableImportSettingsClosure)
                    ->formatStateUsing(fn ($record) => !is_null($record) ? $record->getRawOriginal('url_image_selector') : null)
                ,

                static::getFormFieldWebUrlContentStrip()
                    ->visible(fn (Get $get) => in_array($get('type'), ['url']))
                    ->disabled($disableImportSettingsClosure)
                    ->formatStateUsing(fn ($record) => !is_null($record) ? $record->getRawOriginal('url_content_strip') : null)
                    ->columnSpan(2)
                ,

            ])
            ->disabled($restrictEditingClosure)
            ->visible(static::$sourceType == SourceType::URL->value)
            ->columns(2);
    }

    protected static function getFormTabAdmin(Closure $restrictEditingClosure, bool $canAdminister): Tabs\Tab
    {
        return Tabs\Tab::make('Admin')
            ->schema([
                static::getFormFieldTeam(),
                static::getFormFieldOwner(),
                static::getFormFieldWeight(),
                Forms\Components\Toggle::make('url_render_js')
                    ->label('Render Javascript when Scraping')
                    ->visible(fn (Get $get) => (in_array($get('type'), [SourceType::URL->value])))
                ,
                static::getFormFieldType(SourceType::class)
                    ->live()
                    ->default(static::$sourceType),
                static::getFormFieldFeatured(),
            ])
            ->disabled($restrictEditingClosure)
            ->visible($canAdminister)
            ->columns(2);
    }

    protected static function getFormFieldSubtitle(): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make('subtitle')
            ->label('Subtitle')
            ->maxLength(500)
            ->visible(static::$sourceType == SourceType::BOOK->value);
    }

    protected static function getFormFieldWebUrl(): Forms\Components\TextInput
    {
        return static::getFormFieldUrl()
            ->label(fn (Get $get) => (in_array($get('type'), [SourceType::YOUTUBE->value, SourceType::EPISODE->value]) ? Labeller::mutate($get('type')).' ' : '').'URL')
            ->visible(fn (Get $get) => in_array($get('type'), [SourceType::URL->value, SourceType::YOUTUBE->value, SourceType::EPISODE->value]))
            ->helperText(fn (Get $get) => in_array($get('type'), [SourceType::YOUTUBE->value]) ? new HtmlString("
                <a href='https://www.youtube.com/t/terms' target='_blank' class='text-primary-500 underline'>YouTube Terms of Service ↗</a>
            ") : null)
            ->required()
            ->startsWith(
                [substr(env('YOUTUBE_VIDEO_URL'), 0, strpos(env('YOUTUBE_VIDEO_URL'), '/', 9))],
                fn (Get $get) => in_array($get('type'), [SourceType::YOUTUBE->value])
            )
            ->live(onBlur: true)
        ;
    }

    protected static function getFormFieldMediaUrl(): Forms\Components\TextInput
    {
        $typeName = static::$sourceType == 'media' ? 'Media' : 'Document';
        return static::getFormFieldUrl('media_url')
            ->label(fn (Get $get) =>
                $get('type') == 'media' ?
                'Media URL (audio or video)' :
                'Document URL (PDF or Word Doc)'
            )
            ->visible(fn (Get $get) => in_array($get('type'), ['media', 'book', 'article']))
            ->hintIcon(
                'heroicon-s-question-mark-circle',
                tooltip: fn (Get $get) => "Either {$typeName} URL or {$typeName} File is required"
            )
            ->requiredWithout(fn (Get $get) => in_array($get('type'), ['media']) ? 'media_path' : null)
            ->live(onBlur: true)
        ;
    }

    protected static function getFormFieldCollections(?array $allowedCollectionTypes = []): Forms\Components\Select
    {
        return static::getFormFieldMultiRelation('collection_id', 'collections')
            ->getSearchResultsUsing(function (string $search, ?Model $record) use ($allowedCollectionTypes): array {
                $qry = Collection::where('name', 'like', "%{$search}%");
                if (! is_null($record)) {
                    $qry->whereNotIn('id', $record->collections->pluck('id'));
                }
                if (! Gatekeeper::userCanAdminister(static::getModel())) {
                    $qry->whereBelongsTo(get_current_team());
                }
                if (! empty($allowedCollectionTypes)) {
                    $qry->whereIn('collections.type', $allowedCollectionTypes);
                }

                return $qry->limit(50)
                    ->pluck('name', 'id')
                    ->toArray();
            });
    }

    protected static function getFormNoticeAgent(): Shout
    {
        return Shout::make('agent_notice')
            ->content(new HtmlString("
                By submitting this content to be indexed, you agree that you have legal permission to do so by the
                originator and/or publisher. It will be made available immediately for use by your team's Agent(s).
                This content will <strong>not</strong> be considered for use in 3rd party Agents unless / until it is submitted and
                approved. You do not need to submit the content for review to use it in your own team's Agent(s).
            "))
            ->type('warning')
            ->columnSpan(2)
        ;
    }

    protected static function getFormFieldAgentTerms(): Checkbox
    {
        return Forms\Components\Checkbox::make('agent_terms_accepted')
            ->label('I accept the above Apologist Agent Terms of Use.')
            ->helperText('You cannot make this Source available to Agent until you agree to these terms.')
            ->afterStateUpdated(function ($state, Get $get, Set $set) {
                if (! $state && $get('agent_active')) {
                    $set('agent_active', false);
                }
            })
            ->live()
        ;
    }

    protected static function getFormNoticeAgentMediaStatus(): Shout
    {
        return Shout::make('agent_media_status')
            ->visible(fn ($record) => ! is_null($record) && in_array($record->media_status, [SourceMediaStatus::FAILED, SourceMediaStatus::PROCESSING, SourceMediaStatus::PENDING]))
            ->content(fn ($record) => new HtmlString("
                <strong class='mb-1 block'>Media Transcription Status: {$record->getFormattedMediaStatus()}</strong>
                ".($record->hasMediaError() ? $record->getMediaError() : '')
            ))
            ->type(fn ($record) => $record->hasMediaError() ? 'danger' : 'warning')
            ->columnSpan(2);
    }

    protected static function getFormFieldAgentActiveToggle(): Forms\Components\Toggle
    {
        $label = 'Index for <a href="'.env('AGENT_URL').'" target="_blank" class="hover:underline text-gray-900 dark:text-white font-bold">Apologist Agent ↗</a>';
        $hint = '
            The Source will be used as context by the Apologist Agent chatbot when responding to relevant prompts by users.
            If the source is used in a given answer, it will be cited and linked to the Referral URL.
            You must accept the Apologist Agent Terms of Use above to activate this Source on Apologist Agent.
        ';

        return Forms\Components\Toggle::make('agent_active')
            ->label(new HtmlString($label))
            ->helperText(new HtmlString($hint))
            ->disabled(fn (?Model $record, Get $get) =>
                is_null($record) ||
                ! $get('agent_terms_accepted') ||
                (
                    ! $record->canIndexRag(! empty($get('text_path'))) &&
                    ! $record->canTranscribe(! empty($get('media_url')), ! empty($get('media_path'))) &&
                    ($record->type != SourceType::YOUTUBE)
                )
            )
            ->live()
            ->columnSpan(['default' => 2, 'md' => 1]);
    }

    protected static function getFormFieldAgentHiddenToggle(): Forms\Components\Toggle
    {
        return Forms\Components\Toggle::make('agent_hidden')
            ->label('Hide from End Users')
            ->helperText('The source will be included in the corpus, but will not appear in the list of references.')
            ->hidden(! Gatekeeper::userCanAdminister(Source::class))
            ->live()
            ->columnSpan(['default' => 2, 'md' => 1]);
    }

    protected static function getFormFieldText(): Forms\Components\FileUpload
    {
        return Forms\Components\FileUpload::make('text_path')
            ->label('Source Text (plain text)')
            ->acceptedFileTypes(['text/plain'])
            ->directory('sources/texts')
            ->visibility('private')
            ->afterStateUpdated(function ($state, Get $get, Set $set) {
                if (empty($state) && $get('agent_active')) {
                    $set('agent_active', false);
                }
            })
            ->maxSize(1048576)
            ->hintIcon(
                'heroicon-s-question-mark-circle',
                tooltip: 'This plain text is what will get indexed.'
            )
            ->live()
        ;
    }

    protected static function getFormFieldMedia(): Forms\Components\FileUpload
    {
        return Forms\Components\FileUpload::make('media_path')
            ->label(fn (Get $get) =>
                $get('type') == 'media' ?
                'Media File (audio or video)' :
                'Document File (PDF or Word Doc)'
            )
            ->visible(fn (Get $get) => in_array($get('type'), ['media', 'book', 'article']))
//            ->acceptedFileTypes([
//                'audio/aac',
//                'audio/amr',
//                'audio/ape',
//                'audio/flac',
//                'audio/m4a',
//                'audio/mp3',
//                'audio/ogg',
//                'audio/opus',
//                'audio/wav',
//                'audio/wma',
//                'video/3gp',
//                'video/asf',
//                'video/avi',
//                'video/flv',
//                'video/mkv',
//                'video/mov',
//                'video/mp4',
//                'video/mpeg',
//                'video/mpg',
//                'video/webm',
//                'video/wmv',
//                'video/rm',
//                'video/rmvb',
//            ])
            ->directory('sources/media')
            ->visibility('private')
            ->maxSize(1073741824)
            ->requiredWithout(fn (Get $get) => in_array($get('type'), ['media']) ? 'media_url' : null)
            ->hintIcon(
                'heroicon-s-question-mark-circle',
                tooltip: 'The file uploaded here will be converted to plain text for indexing.'
            )
            ->live()
        ;
    }

    protected static function getFormActionsAgent(): Forms\Components\Actions
    {

        $transcriptionCredits = env_int(
            (static::getSourceType() == SourceType::YOUTUBE->value) ?
                'TRANSCRIBE_YOUTUBE_CREDITS' :
                'TRANSCRIBE_MEDIA_CREDITS'
        );

        $canReindexClosure = fn ($record, Get $get) =>
            !is_null($record) &&
            !$record->is_locked &&
            $get('agent_active') &&
            !empty($get('text_path'))
        ;

        $canTranscribeClosure = fn ($record, Get $get) =>
            !is_null($record) &&
            $record->canTranscribe(!empty($get('media_url')), !empty($get('media_path'))) &&
            Gatekeeper::userCan('promote', Source::class, $record)
        ;

        return Forms\Components\Actions::make([

            Forms\Components\Actions\Action::make('agent_learn')
                ->extraAttributes(['id' => btn_id('agent_learn')])
                ->label('Learn More ↗')
                ->icon('heroicon-s-book-open')
                ->color('primary')
                ->outlined()
                ->url(env('AGENT_LEARN_URL'))
                ->openUrlInNewTab()
            ,

            Forms\Components\Actions\Action::make('transcribe')
                ->extraAttributes(['id' => btn_id('transcribe')])
                ->label(fn (Get $get) => !empty($get('text_path')) ? 'Re-Transcribe' : 'Transcribe')
                ->icon('heroicon-s-bars-arrow-down')
                ->color('primary')
                ->outlined(fn ($record, Get $get) => !empty($get('text_path')))
                ->requiresConfirmation()
                ->action(function ($record, $livewire) {
                    $record->transcribeAsync();
                    Notification::make()
                        ->title('Transcription initiated successfully')
                        ->body('Transcription may take up to 30 minutes.')
                        ->success()
                        ->send()
                    ;
                    $livewire->dispatch('$refresh');
                    return true;
                })
                ->modalHeading('Transcribe Source Media')
                ->modalDescription(new HtmlString("Note that this will incur a cost of <strong>{$transcriptionCredits} credits</strong>."))
                ->modalIcon('heroicon-s-bars-arrow-down')
                ->visible($canTranscribeClosure)
                ->disabled(fn ($record) => !$record->team->credits()->canSpend($transcriptionCredits))
                ->tooltip(fn ($record) =>
                    !$record->team->credits()->canSpend($transcriptionCredits) ?
                    'You do not have enough credits to transcribe Source media.' :
                    null
                )
            ,

            Forms\Components\Actions\Action::make('agent_reindex')
                ->extraAttributes(['id' => btn_id('agent_reindex')])
                ->label('Reindex')
                ->tooltip('Manually reindex this source on Apologist Agent if it has become out of sync.')
                ->color('primary')
                ->outlined()
                ->icon('heroicon-s-circle-stack')
                ->visible($canReindexClosure)
                ->action(function (?Model $record) {
                    dispatch(function () use ($record) {
                        $record->reindexRagDocument();
                    })->catch(function (Throwable $e) {
                        // This job has failed...
                    });
                    Notification::make()
                        ->title('Submitted for Reindexing')
                        ->success()
                        ->send();
                    return true;
                })
                ->requiresConfirmation()
                ->modalIcon('heroicon-s-circle-stack')
            ,

        ])
            ->alignRight()
        ;

    }

    protected static function getFormFieldCategoryActions(Closure $disabledClosure): Forms\Components\Actions
    {
        return Forms\Components\Actions::make([
            Forms\Components\Actions\Action::make('suggest_categories')
                ->extraAttributes(['id' => btn_id('suggest-categories')])
                ->label('Suggest Categories')
                ->icon('heroicon-s-sparkles')
                ->outlined()
                ->action(function (Source $record, Get $get, Set $set) {
                    $suggestedCategoryIds = array_values($record->suggestCategories(env_int('MAX_CATEGORIES', 3)));
                    $set('categories', array_map('strval', $suggestedCategoryIds));
                })
                ->disabled($disabledClosure),
        ])
            ->columnSpan(2)
            ->alignment(Alignment::Center);
    }

    protected static function getFormFieldWebUrlContentStrip(): Textarea
    {
        return Textarea::make('url_content_strip')
            ->label('Strip from URL Content')
            ->rows(5)
            ->maxLength(5000)
            ->hintIcon(
                'heroicon-s-question-mark-circle',
                tooltip: 'Whatever is entered here will automatically be removed from the imported content.'
            )
        ;
    }

    protected static function getTableBulkActionsSources(Table $table): array
    {

        $maxCategories = env_int('MAX_CATEGORIES');
        $typePlural = static::$pluralModelLabel;

        $adminAgentActions = Gatekeeper::userCanAdminister(Source::class) ? [
            static::getTableBulkActionTranscribe($typePlural),
            static::getTableBulkActionHideAgent($typePlural),
            static::getTableBulkActionShowAgent($typePlural),
            static::getTableBulkActionSetAgentWeight('weight', 'Weight', $typePlural, $table),
        ] : [];

        $groups = [

            Tables\Actions\BulkActionGroup::make([
                static::getTableBulkActionEnableSocial($typePlural),
                static::getTableBulkActionDisableSocial($typePlural),
                static::getTableBulkActionReimport($typePlural),
                static::getTableBulkActionReindexSocial($typePlural),
            ])
                ->label('Social'),

            Tables\Actions\BulkActionGroup::make(array_merge(
                [
                    static::getTableBulkActionIndexAgent($typePlural),
                    static::getTableBulkActionDeleteAgent($typePlural),
                    static::getTableBulkActionReindexAgent($typePlural),
                ],
                $adminAgentActions
            ))
                ->label('Agent'),

            Tables\Actions\BulkActionGroup::make([
                static::getTableBulkActionAddCategory($typePlural, $table, $maxCategories),
                static::getTableBulkActionAddCollection($typePlural, $table),
                static::getTableBulkActionRemoveCategory($typePlural, $table),
                static::getTableBulkActionRemoveCollection($typePlural, $table),
            ])
                ->label('Taxonomy'),

            Tables\Actions\BulkActionGroup::make([
                static::getTableBulkActionAddContribution($typePlural, $table),
                static::getTableBulkActionRemoveContribution($typePlural, $table),
            ])
                ->label('Contributor'),

        ];

        if (Gatekeeper::userCanAdminister(static::getModel())) {
            $groups[] = Tables\Actions\BulkActionGroup::make([
                static::getTableBulkActionChangeTeam($typePlural, $table),
                static::getTableBulkActionChangeOwner($typePlural, $table),
            ])
                ->label('Owner');
        }

        return $groups;

    }

    protected static function getTableBulkActionTranscribe(string $typePlural): Tables\Actions\BulkAction
    {
        return static::getTableBulkActionBase(
            'Transcribe',
            'heroicon-s-bars-arrow-down',
            'info',
            "Transcribe {$typePlural} for Agent",
            "Selected {$typePlural} will be transcribed for Apologist Agent.",
            fn ($model) => $model->transcribeAsync(),
            "Selected {$typePlural} Will be Transcribed"
        )
            ->visible(fn () => Gatekeeper::userCan('promote', static::class))
        ;
    }

    protected static function getTableBulkActionHideAgent(string $typePlural): Tables\Actions\BulkAction
    {
        return static::getTableBulkActionBase(
            'Hide on Agent',
            'heroicon-s-document',
            'info',
            "Hide Selected {$typePlural} on Agent",
            "Approved content for the selected {$typePlural} will be hidden by Apologist Agent.",
            ['agent_hidden' => true],
            "Sources Hidden for Selected {$typePlural}"
        );
    }

    protected static function getTableBulkActionShowAgent(string $typePlural): BulkAction
    {
        return static::getTableBulkActionBase(
            'Show on Agent',
            'heroicon-s-document-magnifying-glass',
            'info',
            "Show Selected {$typePlural} on Agent",
            "Approved content for the selected {$typePlural} will be shown by Apologist Agent.",
            ['agent_hidden' => false],
            "Sources Shown for Selected {$typePlural}"
        );
    }

    protected static function getTableBulkActionReimport(string $typePlural): BulkAction
    {
        return static::getTableBulkActionBase(
            'Re-Import',
            'heroicon-s-cloud-arrow-down',
            'warning',
            "Re-Import Selected {$typePlural}",
            'This will overwrite any existing fields previously imported.',
            fn ($model) => $model->importAsync(true, true),
            "Selected {$typePlural} Re-Imported"
        )
            ->visible(fn () => Gatekeeper::userCan('import', static::class));
    }

    protected static function getTableBulkActionReindexSocial(string $typePlural): BulkAction
    {
        return static::getTableBulkActionBase(
            'Reindex on Social',
            'heroicon-s-circle-stack',
            'warning',
            "Reindex Selected {$typePlural} on Social",
            "Approved content for the selected {$typePlural} will be reindexed on Apologist Social.",
            fn ($model) => dispatch(new ManageListingSearchDocumentJob($model)),
            "Sources Reindexed for Selected {$typePlural}"
        );
    }

    protected static function getTableBulkActionIndexAgent(string $typePlural): BulkAction
    {
        return static::getTableBulkActionBase(
            'Index for Agent',
            'heroicon-s-document-plus',
            'success',
            "Index Selected {$typePlural} for Agent",
            "Approved content for the selected {$typePlural} will be indexed for Apologist Agent.",
            ['agent_active' => true],
            "Sources Indexed for Selected {$typePlural}"
        );
    }

    protected static function getTableBulkActionDeleteAgent(string $typePlural): BulkAction
    {
        return static::getTableBulkActionBase(
            'Remove from Agent',
            'heroicon-s-document-minus',
            'danger',
            "Remove {$typePlural} from Agent",
            "Content for the selected {$typePlural} will be removed from Apologist Agent.",
            ['agent_active' => false],
            "Sources Removed from Agent for Selected {$typePlural}"
        );
    }

    protected static function getTableBulkActionReindexAgent(string $typePlural): BulkAction
    {
        return static::getTableBulkActionBase(
            'Reindex on Agent',
            'heroicon-s-circle-stack',
            'warning',
            "Reindex Selected {$typePlural} on Agent",
            "Approved content for the selected {$typePlural} will be reindexed by Apologist Agent.",
            fn ($model) => dispatch(new ManageSourceRagDocumentJob($model)),
            "Sources Reindexed for Selected {$typePlural}"
        );
    }

    protected static function getTableBulkActionAddCategory(string $typePlural, Table $table, int $maxCategories): BulkAction
    {
        return static::getTableBulkActionBase(
            'Add Category',
            'heroicon-s-folder-plus',
            'success',
            "Add Category to Selected {$typePlural}",
            "Note that the category will not be added to any {$typePlural} that already have {$maxCategories} categories."
        )
            ->form(function () {
                return [
                    Forms\Components\Select::make('category_id')
                        ->label('Category')
                        ->options(fn () => Category::active()
                            ->whereNotNull('parent_id')
                            ->orderBy('categories.name')
                            ->pluck('categories.name', 'categories.id')
                            ->toArray()
                        )
                        ->searchable()
                        ->required(),
                ];
            })
            ->action(function ($records) use ($table, $maxCategories, $typePlural) {

                increase_timeout('HIGH');

                $data = $table->getLivewire()->getMountedTableBulkActionForm()->getState();
                $categoryId = $data['category_id'];
                $query = Source::whereIn('sources.id', $records->modelKeys())
                    ->whereDoesntHave('categories', fn (Builder $qry) => $qry->where('categories.id', $categoryId));
                if (! Gatekeeper::userCan('categorize_unlimited', Source::class)) {
                    $query->has('categories', '<', $maxCategories);
                }
                $sources = $query->get();

                static::runOperationOnModels($sources, fn ($model) => $model->categories()->attach($categoryId));

                Notification::make()
                    ->title("Category Added to Selected {$typePlural}")
                    ->success()
                    ->icon('heroicon-s-folder-plus')
                    ->send();

            });
    }

    protected static function getTableBulkActionAddCollection(string $typePlural, Table $table): BulkAction
    {
        return static::getTableBulkActionBase(
            'Add to Collection',
            'heroicon-s-plus-circle',
            'success',
            "Add Collection to Selected {$typePlural}"
        )
            ->form(function () {
                return [
                    Forms\Components\Select::make('collection_id')
                        ->label('Collection')
                        ->options(fn () => Collection::orderBy('name')
                            ->pluck('name', 'id')
                            ->toArray()
                        )
                        ->searchable()
                        ->required(),
                ];
            })
            ->action(function ($records) use ($table, $typePlural) {

                increase_timeout('HIGH');

                $data = $table->getLivewire()->getMountedTableBulkActionForm()->getState();
                $collectionId = $data['collection_id'];
                $sources = Source::whereIn('sources.id', $records->modelKeys())
                    ->whereDoesntHave('collections', fn (Builder $qry) => $qry->where('collections.id', $collectionId))
                    ->get();

                static::runOperationOnModels($sources, fn ($model) => $model->collections()->attach($collectionId));

                Notification::make()
                    ->title("Collection Added to Selected {$typePlural}")
                    ->success()
                    ->icon('heroicon-s-plus-circle')
                    ->send();

            });
    }

    protected static function getTableBulkActionRemoveCategory(string $typePlural, Table $table): BulkAction
    {
        return static::getTableBulkActionBase(
            'Remove Category',
            'heroicon-s-folder-minus',
            'danger',
            "Remove Category from Selected {$typePlural}",
            "The category will be removed from any {$typePlural} that have it."
        )
            ->form(function () {
                return [
                    Forms\Components\Select::make('category_id')
                        ->label('Category')
                        ->options(fn () => Category::active()
                            ->whereNotNull('parent_id')
                            ->orderBy('categories.name')
                            ->pluck('categories.name', 'categories.id')
                            ->toArray()
                        )
                        ->searchable()
                        ->required(),
                ];
            })
            ->action(function ($records) use ($table, $typePlural) {

                increase_timeout('HIGH');

                $data = $table->getLivewire()->getMountedTableBulkActionForm()->getState();
                $categoryId = $data['category_id'];
                $sources = Source::whereIn('sources.id', $records->modelKeys())
                    ->whereHas('categories', fn (Builder $qry) => $qry->where('categories.id', $categoryId))
                    ->get();

                static::runOperationOnModels($sources, fn ($model) => $model->categories()->detach($categoryId));

                Notification::make()
                    ->title("Category Removed from Selected {$typePlural}")
                    ->success()
                    ->icon('heroicon-s-folder-minus')
                    ->send();

            });
    }

    protected static function getTableBulkActionRemoveCollection(string $typePlural, Table $table): BulkAction
    {
        return static::getTableBulkActionBase(
            'Remove from Collection',
            'heroicon-s-minus-circle',
            'danger',
            "Remove Collection from Selected {$typePlural}",
            "The Collection will be removed from any {$typePlural} that have it.",
        )
            ->form(function () {
                return [
                    Forms\Components\Select::make('collection_id')
                        ->label('Collection')
                        ->options(fn () => Collection::orderBy('name')
                            ->pluck('name', 'id')
                            ->toArray()
                        )
                        ->searchable()
                        ->required(),
                ];
            })
            ->action(function ($records) use ($table, $typePlural) {

                increase_timeout('HIGH');

                $data = $table->getLivewire()->getMountedTableBulkActionForm()->getState();
                $collectionId = $data['collection_id'];
                $sources = Source::whereIn('sources.id', $records->modelKeys())
                    ->whereHas('collections', fn (Builder $qry) => $qry->where('collections.id', $collectionId))
                    ->get();

                static::runOperationOnModels($sources, fn ($model) => $model->collections()->detach($collectionId));

                Notification::make()
                    ->title("Collection Removed from Selected {$typePlural}")
                    ->success()
                    ->icon('heroicon-s-minus-circle')
                    ->send();

            });
    }

    protected static function getTableBulkActionAddContribution(string $typePlural, Table $table): BulkAction
    {
        return static::getTableBulkActionBase(
            'Add Contribution',
            'heroicon-s-user-plus',
            'success',
            "Add Contribution to Selected {$typePlural}"
        )
            ->form(function () {
                return [
                    Forms\Components\Select::make('contributor_id')
                        ->label('Contributor')
                        ->options(fn () => Contributor::orderBy('name')
                            ->pluck('name', 'id')
                            ->toArray()
                        )
                        ->searchable()
                        ->required(),
                    static::getFormFieldContributionRole(),
                ];
            })
            ->action(function ($records) use ($table, $typePlural) {

                increase_timeout('HIGH');

                $data = $table->getLivewire()->getMountedTableBulkActionForm()->getState();
                $contributorId = $data['contributor_id'];
                $role = $data['role'];
                $sources = Source::whereIn('sources.id', $records->modelKeys())
                    ->whereDoesntHave('contributions', fn (Builder $qry) => $qry->where('contributor_id', $contributorId))
                    ->get();

                static::runOperationOnModels(
                    $sources,
                    fn ($model) => SourceContribution::create([
                        'contributor_id' => $contributorId,
                        'source_id' => $model->id,
                        'role' => $role,
                    ])
                );

                Notification::make()
                    ->title("Contributor Added to Selected {$typePlural}")
                    ->success()
                    ->icon('heroicon-s-user-plus')
                    ->send();

            });
    }

    protected static function getTableBulkActionRemoveContribution(string $typePlural, Table $table): BulkAction
    {
        return static::getTableBulkActionBase(
            'Remove Contribution',
            'heroicon-s-user-minus',
            'danger',
            "Remove Contribution for Selected {$typePlural}",
            "The Contributor will be removed from any {$typePlural} that have it."
        )
            ->form(function () {
                return [
                    Forms\Components\Select::make('contributor_id')
                        ->label('Contributor')
                        ->options(fn () => Contributor::orderBy('name')
                            ->pluck('name', 'id')
                            ->toArray()
                        )
                        ->searchable()
                        ->required(),
                ];
            })
            ->action(function ($records) use ($table, $typePlural) {

                increase_timeout('HIGH');

                $data = $table->getLivewire()->getMountedTableBulkActionForm()->getState();
                $contributorId = $data['contributor_id'];
                $sourceContributions = SourceContribution::whereIn('source_id', $records->modelKeys())
                    ->where('contributor_id', $contributorId)
                    ->get();

                static::runOperationOnModels($sourceContributions, fn ($model) => $model->delete());

                Notification::make()
                    ->title("Contributor Removed from Selected {$typePlural}")
                    ->success()
                    ->icon('heroicon-s-user-minus')
                    ->send();
            });
    }

    /**
     * @return string[]
     */
    public static function getRelations(): array
    {
        return [
            ContributionsRelationManager::class,
            HistoryRelationManager::class,
        ];
    }

    public static function getApiTransformer(): string
    {
        return SourceTransformer::class;
    }

    protected static function getAllowedCollectionTypes(): array
    {
        return static::$allowedCollectionTypes;
    }

}
