<?php

namespace App\Filament\Resources\EpisodeSourceResource\Pages;

use App\Filament\Resources\EpisodeSourceResource;
use App\Filament\Resources\PodcastCollectionResource;
use App\Filament\Resources\SourceResource\Pages\ListRecords as BaseListRecords;
use Filament\Actions\Action;

class ListRecords extends BaseListRecords
{
    protected static string $resource = EpisodeSourceResource::class;

    protected function getHeaderActions(): array
    {
        return array_merge(
            [
                Action::make('podcast_feeds')
                    ->extraAttributes(['id' => btn_id('podcast-feeds')])
                    ->label('Podcast Feeds')
                    ->icon('heroicon-s-microphone')
                    ->outlined()
                    ->url(PodcastCollectionResource::getUrl()),
            ],
            parent::getHeaderActions()
        );
    }
}
