<?php

namespace App\Filament\Resources;

use App\Enums\CollectionType;
use App\Enums\SourceType;

class MediaSourceResource extends SourceResource
{
    protected static ?string $navigationIcon = 'heroicon-s-film';

    protected static ?string $sourceType = SourceType::MEDIA->value;

    protected static ?array $allowedCollectionTypes = [
        CollectionType::LIST->value,
        CollectionType::SERIES->value,
    ];

    protected static ?string $modelLabel = 'Media File';

    protected static ?string $pluralModelLabel = 'Media Files';

    protected static ?string $navigationLabel = 'Media Files';

    protected static ?string $slug = 'Media';

    protected static string $createLabel = 'add';

    public static function getPermissionPrefixes(): array
    {
        return [];
    }
}
