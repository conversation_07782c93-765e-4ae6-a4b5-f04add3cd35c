<?php

namespace App\Filament\Resources;

use App\Enums\CollectionType;
use App\Enums\SourceType;

class UrlSourceResource extends SourceResource
{
    protected static ?string $navigationIcon = 'heroicon-s-link';

    protected static ?string $sourceType = SourceType::URL->value;

    protected static ?array $allowedCollectionTypes = [
        CollectionType::LIST->value,
        CollectionType::RSS->value,
        CollectionType::WEBSITE->value,
    ];

    protected static ?string $modelLabel = 'Web Page';

    protected static ?string $pluralModelLabel = 'Web Pages';

    protected static ?string $navigationLabel = 'Web Pages';

    protected static ?string $slug = 'urls';

    protected static string $createLabel = 'add';

    public static function getPermissionPrefixes(): array
    {
        return [];
    }
}
