<?php

namespace App\Filament\Resources\TeamResource\Pages;

use App\Filament\Resources\TeamResource;
use App\Filament\Templates\EditRecord as BaseEditRecord;
use App\Filament\Traits\HasProductTour;
use App\Services\Gatekeeper;
use Filament\Actions;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\HtmlString;
use JibayMcs\FilamentTour\Tour\HasTour;

class EditRecord extends BaseEditRecord
{
    use HasProductTour;
    use HasTour;

    protected static string $resource = TeamResource::class;

    /**
     * @return string[]
     */
    public function getBreadcrumbs(): array
    {
        if (Gatekeeper::userCanAdminister(static::$resource::getModel())) {
            return parent::getBreadcrumbs();
        } else {
            $url = url()->current();
            return ["{$url}" => 'Edit Team Profile'];
        }
    }

    protected function getFormActionArchive(): Actions\DeleteAction
    {
        return $this->getFormActionSuspend()
            ->modalDescription(new HtmlString("
                <span class='text-red-500'>
                    By suspending this Team, you and all its members will lose access to it and all that it contains.
                    You will need to contact us to if you would like to recover it or anything in it.
                </span>
            "))
            ->successRedirectUrl(fn () => auth()->user()->hasTeams() ? route('filament.app.tenant.registration') : route('filament.app.tenant.switch', ['tenant' => auth()->user()->getFirstActiveTeam()->id]))
        ;
    }

    protected function getFormActionRestore(): Actions\RestoreAction
    {
        return $this->getFormActionReactivate();
    }
}
