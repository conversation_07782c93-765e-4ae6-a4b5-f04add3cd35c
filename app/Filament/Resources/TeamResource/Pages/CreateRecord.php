<?php

namespace App\Filament\Resources\TeamResource\Pages;

use App\Filament\Resources\TeamResource;
use App\Filament\Templates\CreateRecord as BaseCreateRecord;
use App\Services\Gatekeeper;

class CreateRecord extends BaseCreateRecord
{
    protected static string $resource = TeamResource::class;

    protected static bool $canCreateAnother = false;

    /**
     * @return string[]
     */
    public function getBreadcrumbs(): array
    {
        if (Gatekeeper::userCanAdminister(static::$resource::getModel())) {
            return parent::getBreadcrumbs();
        } else {
            $url = url()->current();

            return ["{$url}" => 'Create New Team'];
        }
    }

    public function getRedirectUrl(): string
    {
        return TeamResource::getUrl(
            'edit',
            [
                'tenant' => $this->getRecord()->id,
                'record' => $this->getRecord()->id,
                'new' => true,
            ]
        );
    }
}
