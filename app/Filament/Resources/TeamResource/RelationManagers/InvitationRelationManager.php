<?php

namespace App\Filament\Resources\TeamResource\RelationManagers;

use App\Filament\RelationManagers\TeamRelationManager;
use App\Filament\Traits\HasActions;
use Exception;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Support\Enums\IconPosition;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Validation\Rules\Unique;

class InvitationRelationManager extends TeamRelationManager
{
    use HasActions;

    protected static string $relationship = 'invitations';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return 'Invitations';
    }

    public function form(Form $form): Form
    {
        $userName = auth()->user()->name;

        return $form
            ->schema([
                static::getFormFieldEmail()
                    ->unique(modifyRuleUsing: function (Unique $rule) {
                        return $rule->where('team_id', $this->getOwnerRecord()->id);
                    })
                    ->validationMessages([
                        'unique' => "This email has already been invited. Check cancelled invitations if you don't see it.",
                    ]),
                static::getFormFieldRole(),
                Forms\Components\Textarea::make('body')
                    ->label('Invitation Email Body')
                    ->autosize()
                    ->maxLength(500)
                    ->default("Hello!

Please join my team on Apologist Ignite: {$this->getOwnerRecord()->name}.

Apologist Ignite is the premier Christian evangelism and apologetics platform that helps ministries and apologetics organizations build custom conversational AIs.

Sincerely,
{$userName}")
                    ->columnSpan(2),
            ]);
    }

    /**
     * @throws Exception
     */
    public function table(Table $table): Table
    {
        $restrictEditing = restrict_editing($this->getOwnerRecord());

        return $table
            ->recordTitleAttribute('email')
            ->columns([
                static::getTableColumnName('CANCELLED', 'email', 'Email')
                    ->copyable()
                    ->copyMessage('Copied!')
                    ->copyMessageDuration(2500)
                    ->icon('heroicon-o-clipboard')
                    ->iconPosition(IconPosition::After),
                static::getTableColumnRole(),
                static::getTableColumnTimestamp('created_at', 'Invited', true)
                    ->visibleFrom('xl'),
                static::getTableColumnTimestamp('accepted_at', 'Accepted', true)
                    ->visibleFrom('xl'),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterRelation('role'),
                static::getTableFilterTernary('created_at', 'Invited'),
                static::getTableFilterTernary('accepted_at', 'Accepted')
                    ->default(false)
                    ->indicateUsing(static::getTernaryIndicators('Accepted', 'Pending')),
                static::getTableFilterArchived()
                    ->label('Cancelled')
                    ->indicateUsing(static::getTernaryIndicators('Cancelled', 'Active')),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make('invite')
                    ->extraAttributes(['id' => btn_id('invite')])
                    ->label('Invite New Team Member')
                    ->modalHeading('Invite New Team Member')
                    ->modalSubmitActionLabel('Invite New Team Member')
                    ->extraModalFooterActions(function ($action): array {
                        return [
                            $action->makeModalSubmitAction('createAnother', ['another' => true])
                                ->label('Invite and Invite Another'),
                        ];
                    })
                    ->successNotificationTitle('Team Member Invited')
                    ->hidden($restrictEditing),
            ])
            ->actions([
                static::getTableActionResend()
                    ->hidden($restrictEditing),
                static::getTableActionCancel()
                    ->hidden($restrictEditing),
                static::getTableActionRestore()
                    ->hidden($restrictEditing),
                static::getTableActionDelete()
                    ->hidden($restrictEditing),
            ])
            ->modifyQueryUsing(function (Builder $query) {
                $query->withoutGlobalScopes([
                    SoftDeletingScope::class,
                ]);
            });
    }

    protected static function getTableActionResend(): Tables\Actions\Action
    {
        return Tables\Actions\Action::make('resend')
            ->extraAttributes(['class' => btn_id('resend-invitation')])
            ->label('Resend')
            ->icon('heroicon-s-envelope')
            ->requiresConfirmation()
            ->color('primary')
            ->visible(fn (?Model $record) => ! $record->trashed())
            ->modalIcon('heroicon-s-envelope')
            ->modalHeading(fn (?Model $record) => "Resend Invitation to {$record->email}")
            ->action(function (?Model $record) {
                $record->send();
                Notification::make()
                    ->title('Invitation resent')
                    ->success()
                    ->send();

                return true;
            });
    }

    protected static function getTableActionCancel(): Tables\Actions\DeleteAction
    {
        return Tables\Actions\DeleteAction::make('cancel')
            ->extraAttributes(['id' => btn_id('cancel-invitation')])
            ->label('Cancel')
            ->icon('heroicon-s-x-mark')
            ->requiresConfirmation()
            ->modalIcon('heroicon-s-x-mark')
            ->modalHeading(fn (?Model $record) => "Cancel Invitation to {$record->email}")
            ->successNotificationTitle('Invitation canceled');
    }
}
