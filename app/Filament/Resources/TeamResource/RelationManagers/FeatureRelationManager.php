<?php

declare(strict_types=1);

namespace App\Filament\Resources\TeamResource\RelationManagers;

use App\Models\Team;
use App\Services\Gatekeeper;
use Illuminate\Database\Eloquent\Model;
use Maartenpaauw\Filament\Pennant\Resources\FeatureResource\RelationManagers\FeatureRelationManager as BaseFeatureRelationManager;

class FeatureRelationManager extends BaseFeatureRelationManager
{
    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return Gatekeeper::userCanAdminister(Team::class);
    }
}
