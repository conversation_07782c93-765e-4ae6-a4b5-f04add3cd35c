<?php

namespace App\Filament\Resources\AnthologyCollectionResource\Pages;

use App\Filament\Resources\AnthologyCollectionResource;
use App\Filament\Resources\CollectionResource\Pages\ListRecords as BaseListRecords;

class ListRecords extends BaseListRecords
{
    protected static string $resource = AnthologyCollectionResource::class;

    protected function getHeaderActions(): array
    {
        return array_merge(
            [
                static::getBooksHeaderAction(),
            ],
            parent::getHeaderActions()
        );
    }
}
