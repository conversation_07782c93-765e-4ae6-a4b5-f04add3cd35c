<?php

namespace App\Filament\Widgets;

use App\Models\Model;
use Carbon\Carbon;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;

abstract class FrontendListingAreaChart extends AreaChart
{

    protected static bool $isDiscovered = false;

    protected string|int|array $columnSpan = 'full';

    public ?Model $record;

    public ?string $model = null;

    public ?string $modelLabel = null;

    protected function getChartId(): ?string
    {
        return replace_tokens(static::$chartId, ['model' => class_basename($this->model)]);
    }

    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     */
    protected function getOptions(): array
    {

        $qry = $this->getChartBaseQuery();
        $listings = $this->getListingsQuery($qry)->pluck('name', 'id');
        $minDateQry = clone $qry;
        $minDate = new Carbon($minDateQry->min(static::$dateColumn));

        $datasets = [];
        foreach ($listings as $id => $name) {
            $clonedQry = clone $qry;
            if (isset($this->record)) {
                $frontends = ['Agent', 'Social'];
                foreach ($frontends as $frontend) {
                    $data = $this->setInterval(
                        Trend::query($clonedQry->where("{$this->getModelTable()}.id", $id)->where('frontend', strtolower($frontend)))->dateColumn(static::$dateColumn),
                        "{$this->getModelTable()}.id",
                        $minDate
                    );
                    $datasets[] = [
                        'name' => $frontend,
                        'data' => $data->map(fn (TrendValue $value) => $value->aggregate),
                    ];
                }
            } else {
                $data = $this->setInterval(
                    Trend::query($clonedQry->where("{$this->getModelTable()}.id", $id))->dateColumn(static::$dateColumn),
                    "{$this->getModelTable()}.id",
                    $minDate
                );
                $datasets[] = [
                    'name' => $name,
                    'data' => $data->map(fn (TrendValue $value) => $value->aggregate),
                ];
            }
        }

        return $this->getChartOptions(
            $datasets,
            isset($data) ? $data->map(fn (TrendValue $value) => $value->date) : collect()
        );

    }

    protected function getListingsQuery($qry): Builder
    {
        $listingsQuery = $qry->select("{$this->getModelTable()}.id", "{$this->getModelTable()}.name")->groupBy("{$this->getModelTable()}.id");
        if (isset($this->record)) {
            $listingsQuery->where("{$this->getModelTable()}.id", $this->record->id);
        } else {
            $listingsQuery->orderBy(DB::raw("COUNT({$this->getModelTable()}.id)"), 'DESC')
                ->limit(static::MAX_ITEMS);
        }

        return $listingsQuery;
    }

    public static function canView(): bool
    {
        return (new static)->getChartBaseQuery()->count() > 0;
    }

    protected function getFooter(): string
    {
        $modelPhrase = $this->modelLabel . (str_ends_with($this->modelLabel, 's') ? ' have' : ' has');
        $description = replace_tokens($this->description, ['modelPhrase' => $modelPhrase]);
        return new HtmlString("
            <p class='text-center text-gray-500 text-sm mx-auto max-w-3xl'>
                {$description}
            </p>
        ");
    }

    protected function getModelTable(): ?string
    {
        return get_model_table($this->model);
    }

}
