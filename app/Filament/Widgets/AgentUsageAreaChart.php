<?php

namespace App\Filament\Widgets;

use App\Models\Agent;
use App\Services\Gatekeeper;
use Carbon\Carbon;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;

abstract class AgentUsageAreaChart extends AreaChart
{

    protected static bool $isDiscovered = false;

    protected string|int|array $columnSpan = 'full';

    public ?Agent $agent = null;

    protected static ?string $metricColumn = null;

    protected static ?string $metricAggregation = 'count';

    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     */
    protected function getOptions(): array
    {

        $qry = $this->getChartBaseQuery()->where('agents.team_id', get_current_team_id());
        $agents = $this->getAgentsQuery($qry)->pluck('name', 'id');
        $minDateQry = clone $qry;
        $minDate = new Carbon($minDateQry->min('agent_prompts.prompted_at'));

        $datasets = [];
        foreach ($agents as $id => $name) {
            $clonedQry = clone $qry;
            $data = $this->setInterval(
                Trend::query($clonedQry->where("agents.id", $id))->dateColumn('agent_prompts.prompted_at'),
                static::getMetricColumn(),
                $minDate,
                static::$metricAggregation
            );
            $datasets[] = [
                'name' => $name,
                'data' => $data->map(fn (TrendValue $value) => $value->aggregate),
            ];
        }

        return $this->getChartOptions(
            $datasets,
            isset($data) ? $data->map(fn (TrendValue $value) => $value->date) : collect()
        );

    }

    protected function getAgentsQuery($qry): Builder
    {
        $column = static::getMetricColumn();
        $aggregation = static::$metricAggregation;
        $agentsQuery = $qry->select('agents.id', 'agents.name', 'agent_prompts.conversation_id', 'agent_prompts.session_id')
            ->groupBy('agents.id')
            ->orderBy(DB::raw("{$aggregation}({$column})"), 'DESC')
            ->limit(static::MAX_ITEMS)
        ;
        if (!is_null($this->agent)) {
            $agentsQuery->where('agents.id', $this->agent->id);
        }
        return $agentsQuery;
    }

    protected static function getMetricColumn(): string
    {
        return static::$metricColumn;
    }

    public static function canView(): bool
    {
        return (new static)->getChartBaseQuery()->count() > 0;
    }

    protected function getFooter(): string
    {
        $plural = $this->agent ? '' : '(s)';
        return new HtmlString("
            <p class='text-center text-gray-500 text-sm mx-auto max-w-3xl'>
                {$this->description}{$plural}.
            </p>
        ");
    }

}
