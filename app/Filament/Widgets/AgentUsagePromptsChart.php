<?php

namespace App\Filament\Widgets;

use App\Models\Agent;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;

class AgentUsagePromptsChart extends AgentUsageAreaChart
{
    protected static string $view = 'filament-apex-charts::widgets.apex-chart-widget';

    protected static ?int $sort = 31;

    protected ?string $description = 'The number of prompts submitted to your Agent';

    protected static ?string $metricColumn = 'agents.id';

    protected static ?string $chartId = 'agentUsagePromptsChart';

    protected static ?string $heading = 'Prompts Submitted';

    protected function getChartBaseQuery(): Builder
    {
        return Agent::join('agent_prompts', 'agents.id', '=', 'agent_prompts.agent_id');
    }

}
