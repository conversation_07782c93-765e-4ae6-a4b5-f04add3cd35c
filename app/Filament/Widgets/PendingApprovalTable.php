<?php

namespace App\Filament\Widgets;

use App\Filament\Traits\HasActions;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFilters;
use App\Models\Model;
use App\Services\Gatekeeper;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use RingleSoft\LaravelProcessApproval\Enums\ApprovalActionEnum;

abstract class PendingApprovalTable extends BaseWidget
{
    use HasActions;
    use HasColumns;
    use HasFilters;

    public static bool $isDiscovered = false;

    protected int|string|array $columnSpan = 'full';

    public static function canView(): bool
    {
        return static::getTableBaseQuery()->count() > 0;


    }

    /**
     * @throws \Exception
     */
    public function table(Table $table): Table
    {

        $urlClosure = function (Model $record) {
            if (! empty($this->getTypeOptions())) {
                $resourceClass = str_replace(
                    class_basename($this->resource),
                    Str::title($record->type->value).class_basename($this->resource),
                    $this->resource
                );
            } else {
                $resourceClass = $this->resource;
            }

            return call_user_func(
                "{$resourceClass}::getUrl",
                'edit',
                [
                    'tenant' => get_current_team_id(),
                    'record' => $record->id,
                ],
                panel: 'app'
            );
        };

        return $table
            ->heading($this->title)
            ->query(static::getTableBaseQuery())
            ->columns([
                static::getTableColumnApprovalStatus(),
                static::getTableColumnName(),
                static::getTableColumnType()
                    ->visible(! empty($this->getTypeOptions())),
                static::getTableColumnSince('updated_at', 'Last Updated')
                    ->visibleFrom('md'),
                static::getTableColumnTeam()
                    ->visibleFrom('md'),
            ])
            ->filters([
                static::getTableFilterStatus(),
                static::getTableFilterType($this->getTypeOptions())
                    ->visible(! empty($this->getTypeOptions())),
                static::getTableFilterRelation('collections'),
                static::getTableFilterRelation('categories'),
                static::getTableFilterBoolean('is_locked', 'Locked', 'Unlocked'),
                static::getTableFilterBoolean('agent_active', 'Apologist Agent'),
                static::getTableFilterBoolean('social_active', 'Apologist Social'),
                static::getTableFilterArchived(),
                static::getTableFilterTeam(Gatekeeper::canModerateContent()),
                static::getTableFilterOwner(),
            ])
            ->filtersFormColumns(3)
            ->actions([
                static::getTableActionEditRow()
                    ->url($urlClosure),
            ])
            ->recordUrl($urlClosure);

    }

    protected static function getTableBaseQuery(): Builder
    {
        $model = static::$model;
        $qry = $model::nonApproved()->whereHas('approvalStatus', static function ($q) {
            return $q->whereIn('status', [ApprovalActionEnum::SUBMITTED->value, ApprovalActionEnum::RETURNED->value]);
        });
        if (!Gatekeeper::canModerateContent()) {
            $qry->where('team_id', get_current_team_id());
        }
        return $qry;
    }

    protected function getTypeOptions(): array
    {
        return [];
    }

    public static function getModel(): string
    {
        return static::$model;
    }
}
