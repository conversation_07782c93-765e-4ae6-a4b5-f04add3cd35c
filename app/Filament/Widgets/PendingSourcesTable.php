<?php

namespace App\Filament\Widgets;

use App\Enums\SourceType;
use App\Filament\Resources\SourceResource;
use App\Models\Source;

class PendingSourcesTable extends PendingApprovalTable
{
    protected static ?int $sort = 81;

    protected string $title = 'Sources Pending Approval';

    protected string $resource = SourceResource::class;

    protected static ?string $model = Source::class;

    protected function getTypeOptions(): array
    {
        return SourceType::asOptions();
    }
}
