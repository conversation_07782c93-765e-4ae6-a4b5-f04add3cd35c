<?php

namespace App\Filament\Widgets;

use App\Models\Agent;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;

class AgentUsageCreditsChart extends AgentUsageAreaChart
{
    protected static string $view = 'filament-apex-charts::widgets.apex-chart-widget';

    protected static ?int $sort = 31;

    protected ?string $description = 'The number of credits used by your Agent';
    protected static ?string $metricColumn = 'agent_models.num_credits + IF(agent_prompts.is_translated, {realTimeTranslationCredits}, 0)';

    protected static ?string $metricAggregation = 'sum';

    protected static ?string $chartId = 'agentUsageCreditsChart';

    protected static ?string $heading = 'Credits Used';

    protected function getChartBaseQuery(): Builder
    {
        return Agent::join('agent_prompts', 'agents.id', '=', 'agent_prompts.agent_id')
            ->join('agent_models', 'agents.model_id', '=', 'agent_models.id')
        ;
    }

    protected static function getMetricColumn(): string
    {
        return replace_tokens(
            static::$metricColumn,
            [
                'realTimeTranslationCredits' => env_int('REALTIME_TRANSLATION_CREDITS')
            ]
        );
    }

}
