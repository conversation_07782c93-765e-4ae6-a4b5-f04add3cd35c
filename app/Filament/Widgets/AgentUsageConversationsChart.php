<?php

namespace App\Filament\Widgets;

use App\Models\Agent;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;

class AgentUsageConversationsChart extends AgentUsageAreaChart
{

    protected static string $view = 'filament-apex-charts::widgets.apex-chart-widget';

    protected static ?int $sort = 31;

    protected ?string $description = 'The number of user conversations with your Agent';
    protected static ?string $metricColumn = 'DISTINCT agent_prompts.conversation_id';

    protected static ?string $chartId = 'agentUsageConversationsChart';

    protected static ?string $heading = 'Conversations';

    protected function getChartBaseQuery(): Builder
    {
        return Agent::join('agent_prompts', 'agents.id', '=', 'agent_prompts.agent_id');
    }

}
