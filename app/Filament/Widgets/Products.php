<?php

namespace App\Filament\Widgets;

use App\Filament\Traits\HasActions;
use App\Filament\Traits\HasColumns;
use App\Models\Model;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

abstract class Products extends BaseWidget
{
    use HasActions;
    use HasColumns;

    public static bool $isDiscovered = false;

    public static function canView(): bool
    {
        return static::getTableBaseQuery()->count() > 0;
    }

    public function table(Table $table): Table
    {
        $urlClosure = function (Model $record) {
            return route(
                $this->resourceRoute,
                [
                    'tenant' => get_current_team_id(),
                    'record' => $record->id,
                ]
            );
        };

        return $table
            ->heading($this->title)
            ->query(static::getTableBaseQuery())
            ->columns([
                static::getTableColumnApprovalStatus(),
                static::getTableColumnName(),
                static::getTableColumnSince('updated_at', 'Last Updated')
                    ->visibleFrom('md'),
            ])
            ->actions([
                static::getTableActionEditRow(),
            ])
            ->recordUrl($urlClosure);
    }
}
