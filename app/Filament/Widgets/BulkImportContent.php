<?php

namespace App\Filament\Widgets;

use Alaouy\Youtube\Facades\Youtube;
use App\Enums\SourceType;
use App\Enums\CollectionType;
use App\Filament\Pages\App\Dashboard;
use App\Filament\Resources\BookSourceResource;
use App\Filament\Traits\HasFields;
use App\Jobs\ImportBookSourceDataJob;
use App\Models\Collection;
use App\Models\Source;
use App\Services\AmazonClient;
use App\Services\YoutubeClient;
use Awcodes\Shout\Components\Shout;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\Alignment;
use Filament\Widgets\Widget;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use PhanAn\Poddle\Poddle;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;

class BulkImportContent extends Widget implements HasForms
{
    use InteractsWithForms;
    use InteractsWithFormActions;
    use HasFields;

    public ?array $data = [];

    protected static string $view = 'components.app.bulk-import-content';

    protected static ?int $sort = 1;

    protected int|string|array $columnSpan = 'full';

    public static bool $isDiscovered = false;

    protected array $collections = [];

    protected array $sources = [];

    protected array $errors = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Wizard::make($this->getSteps())
                    ->previousAction(fn (Action $action) => $action->label('Back'))
                    ->nextAction(fn (Action $action) => $action->label('Next Step'))
                    ->submitAction(new HtmlString(Blade::render(<<<'BLADE'
                            <x-filament::button
                                type="submit"
                                size="sm"
                                x-data="{ clicked: false }"
                                @click="clicked = true"
                            >
                                Import Content
                                <x-filament::loading-indicator class="h-5 w-5 inline" x-show="clicked" />
                            </x-filament::button>
                        BLADE))
                    )
                ,
            ])
            ->columns(1)
            ->statePath('data');
    }

    protected function getSteps(): array
    {
        return [
            $this->getStep1(),
            $this->getStep2(),
        ];
    }

    protected function getStep1(): Step
    {
        return Step::make('Upload Files or Enter URLs')
            ->icon('heroicon-s-document-arrow-up')
            ->schema([
                $this->getStep1Intro(),
                $this->getFormFieldFiles()
                    ->columnSpan(['default'=>7, 'md'=>3])
                ,
                ViewField::make('or')->view('components.app.import-content-or')
                    ->columnSpan(['default'=>7, 'md'=>1])
                ,
                $this->getFormFieldUrls()
                    ->columnSpan(['default'=>7, 'md'=>3])
                ,
            ])
            ->columns(7)
            ->afterValidation(fn (Get $get, Set $set) => $this->processStep1($get, $set))
        ;
    }

    protected function getFormFieldFiles(): FileUpload
    {
        return FileUpload::make('files')
            ->label('Upload Documents or Media Files')
            ->directory('sources/media')
            ->visibility('private')
            ->maxSize(1073741824)
            ->requiredWithout('urls')
            ->hintIcon(
                'heroicon-s-question-mark-circle',
                tooltip: 'PDFs, Word Documents, videos, audio files, etc.'
            )
            ->requiredWithout('url')
            ->multiple()
            ->panelLayout('grid')
            ->helperText('You can also upload a CSV of URLs to populate the URLs list.')
            ->live()
            ->afterStateUpdated(function (Get $get, Set $set) {
                $files = $get('files');
                foreach ($files as $key => $file)
                {
                    if ($file->getMimeType() == 'text/csv') {
                        $parsedUrls = parse_all_urls($file->get());
                        $newUrls = array_map(fn ($url) => ['url'=>$url], $parsedUrls);
                        $existingUrls = $get('urls');
                        $urls = array_unique(array_merge($newUrls, $existingUrls), SORT_REGULAR);
                        $set('urls', $urls);
                    }
                }
            })
        ;
    }

    protected function getFormFieldUrls(): Repeater
    {
        return Repeater::make('urls')
            ->label('Enter URLs')
            ->simple(
                static::getFormFieldUrl('url', false)
                    ->distinct()
                ,
            )
            ->hintIcon(
                'heroicon-s-question-mark-circle',
                tooltip: 'Web pages, RSS feeds, podcast feeds, podcast audio, YouTube channels, YouTube videos, etc.'
            )
            ->requiredWithout('files')
            ->addActionLabel('+ Add URL')
            ->reorderable(false)
            ->minItems(1)
            ->addActionAlignment(Alignment::End)
            ->live()
        ;
    }

    protected function processStep1(Get $get, Set $set): void
    {

        $files = $get('files');
        $fileIndex = 0;
        foreach ($files as $key => $file)
        {
            $mime = $file->getMimeType();
            if ($mime != 'text/csv') {
                $this->addFileImport($file, $fileIndex);
            }
            ++$fileIndex;
        }

        $urls = array_map(fn ($row) => $row['url'], $get('urls'));
        foreach ($urls as $url)
        {
            if (!empty($url)) {
                $this->addUrlImport($url);
            }
        }

//        Log::debug(json_encode($this->collections, JSON_PRETTY_PRINT));
//        Log::debug(json_encode($this->sources, JSON_PRETTY_PRINT));
//        Log::debug(json_encode($this->errors, JSON_PRETTY_PRINT));

        $set('collections', $this->collections);
        $set('sources', $this->sources);
        $set('errors', $this->errors);

    }

    protected function addFileImport(TemporaryUploadedFile $file, int $index): void
    {
        $mimeCategory = get_mime_category($file->getMimeType());
        $this->sources[] = [
            'name' => $file->getClientOriginalName(),
            'type' => in_array($mimeCategory, ['audio', 'video']) ? SourceType::MEDIA->value : SourceType::ARTICLE->value,
            'file_index' => $index,
        ];
    }

    protected function addUrlImport(string $url): void
    {

        $name = $url;
        $mime = get_remote_mime_type($url) ?? 'text/html';
        $mimeCategory = get_mime_category($mime);

        if ($mime == 'text/html') {

            // YOUTUBE CHANNEL OR PLAYLIST
            if (YoutubeClient::isValidUrl($url)) {

                if (YoutubeClient::isVideoUrl($url)) {
                    $videoId = YoutubeClient::parseVideoIdFromUrl($url);
                    $this->sources[] = [
                        'name' => $name,
                        'type' => SourceType::YOUTUBE->value,
                        'url' => $url,
                        'external_id' => $videoId,
                    ];
                } else {
                    try {
                        $channel = YoutubeClient::getChannelByUrl($url);
                        $this->collections[] = [
                            'name' => $name,
                            'type' => CollectionType::CHANNEL->value,
                            'url' => $url,
                            'external_id' => $channel->id,
                        ];
                    } catch (\Exception $e) {
                        Log::error($e->getTraceAsString());
                        $this->errors[] = $name;
                    }
                }

            // AMAZON (BOOK)
            } else if (AmazonClient::isBookUrl($url)) {
                $asin = AmazonClient::getAsinFromURL($url);
                if (!empty($asin)) {
                    $this->sources[] = [
                        'name' => $name,
                        'type' => SourceType::BOOK->value,
                        'url' => $url,
                        'external_id' => $asin,
                    ];
                } else {
                    $this->errors[] = $name;
                }

            } else if (is_website_root($url)) {
                $this->collections[] = [
                    'name' => $name,
                    'type' => CollectionType::WEBSITE->value,
                    'url' => $url,
                ];

            // Otherwise treat as general web page
            } else {
                $this->sources[] = [
                    'name' => $name,
                    'type' => SourceType::URL->value,
                    'url' => $url,
                    'external_id' => $url,
                ];
            }

        // MEDIA URLs
        } else if (in_array($mimeCategory, ['video', 'audio'])) {
            $this->sources[] = [
                'name' => $name,
                'type' => SourceType::MEDIA->value,
                'media_url' => $url,
            ];

        // SITEMAP?
        } else if ($mime == 'text/xml') {
            $this->collections[] = [
                'name' => $name,
                'type' => CollectionType::WEBSITE->value,
                'url' => $url,
            ];

        // RSS FEED
        } else if ($mime == 'application/rss+xml') {

            // PODCAST
            $feed = Poddle::fromUrl($url);
            try {
                $channel = $feed->getChannel();
                $this->collections[] = [
                    'name' => $channel->title,
                    'type' => CollectionType::PODCAST->value,
                    'url' => $url,
                ];

            // RSS FEED
            } catch (\Exception|\Throwable $e) {
                $this->collections[] = [
                    'name' => $name,
                    'type' => CollectionType::RSS->value,
                    'url' => $url,
                ];
            }

        // Plain Text
        } else if ($mime == 'text/plain') {
            $this->sources[] = [
                'name' => $name,
                'type' => SourceType::ARTICLE->value,
                'url' => $url,
            ];

        // Otherwise just assume this is some kind of document
        } else {
            $this->sources[] = [
                'name' => $name,
                'type' => SourceType::ARTICLE->value,
                'media_url' => $url,
            ];
        }

    }

    protected function getStep1Intro(): Shout
    {
        return Shout::make('step1_intro')
            ->content(new HtmlString("
                Browse files or drag them into the box below.
                You may also upload a CSV with URLs in any column to automatically populate the URLs list.
                Any URLs populated will be automatically analyzed to determine whether they are websites, podcast feeds, YouTube channels etc.
                You will have a chance to configure each import in the next step.
            "))
            ->color('primary')
            ->icon(false)
            ->columnSpan(7)
        ;
    }

    protected function getStep2(): Step
    {
        $socialUrl = env('SOCIAL_URL');
        $youtubeCredits = env_int('TRANSCRIBE_YOUTUBE_CREDITS');
        $mediaCredits = env_int('TRANSCRIBE_MEDIA_CREDITS');
        $transcriptionText = new HtmlString("
            <p class='mt-4'>
                <strong>Please Note:</strong> YouTube videos and media files must be transcribed in order to be indexed for use in your Agent(s).
                Each YouTube transcription will incur a cost of <strong>{$youtubeCredits} credits</strong>,
                and each media file transcription will incur a cost of <strong>{$mediaCredits} credits</strong>.
            </p>
        ");
        return Step::make('Configure Imports')
            ->icon('heroicon-s-cog-6-tooth')
            ->schema([
                $this->getImportErrors(),
                $this->getCollectionsIntro($socialUrl, $transcriptionText),
                $this->getCollectionImportRepeater(),
                $this->getSourcesIntro($socialUrl, $transcriptionText),
                $this->getSourceImportRepeater(),
            ])
            ->columns(1)
            ;
    }

    protected function getCollectionsIntro(string $socialUrl, string $transcriptionText): Shout
    {
        return Shout::make('collections_intro')
            ->content(new HtmlString("
                <p class='mb-4'>
                    The following URLs were identified as <strong>Collections</strong>.
                    Collections are groups of Sources, such as a website, podcast, or YouTube channel.
                    Collections have powerful bulk data ingestion capabilities, such as:
                </p>
                <ul class='pl-4 list-disc'>
                    <li><strong>Import Sources:</strong> automatically import new Sources on a daily basis</li>
                    <li><strong>Sync to Agent:</strong> automatically index imported Sources for use in your Agent(s)</li>
                    <li>
                        <strong>Sync to Social:</strong> automatically list imported Sources on
                        <a href='{$socialUrl}' target='_blank' class='underline'>Apologist Social ↗</a> (if approved)
                    </li>
                </ul>
                {$transcriptionText}
            "))
            ->color('primary')
            ->icon(false)
            ->visible(fn (Get $get) => count($get('collections')) > 0)
            ->columnSpan(2)
        ;
    }

    protected function getSourcesIntro(string $socialUrl, string $transcriptionText): Shout
    {
        return Shout::make('sources_intro')
            ->content(new HtmlString("
                <p class='mb-4'>
                    The following files and URLs were identified as <strong>Sources</strong>.
                    Sources are individual pieces of content, such as a PDF book / article, blog post, podcast episode, or YouTube video.
                    Sources can be configured to:
                </p>
                <ul class='pl-4 list-disc'>
                    <li><strong>Index for Agent:</strong> index for use in your Agent(s)</li>
                    <li>
                        <strong>List on Social:</strong> automatically list on
                        <a href='{$socialUrl}' target='_blank' class='underline'>Apologist Social ↗</a> (if approved)
                    </li>
                </ul>
                {$transcriptionText}
            "))
            ->color('primary')
            ->icon(false)
            ->visible(fn (Get $get) => count($get('sources')) > 0)
            ->columnSpan(2)
        ;
    }

    protected function getImportErrors(): Shout
    {
        return Shout::make('errors')
            ->visible(fn () => count($this->errors) > 0)
            ->content(fn () => new HtmlString("
                <p class='mb-4'>The following files and URLs cannot be imported:</p>
                <ul class='pl-4 list-disc font-semibold mb-4'>
                    " . implode('', array_map(fn ($name) => "<li>{$name}</li>", $this->errors)) . "
                </ul>
                <p>You may go back and try again, or proceed without them.</p>
            "))
            ->color('danger')
            ->icon(false)
            ->columnSpan(2)
        ;
    }

    protected function getCollectionImportRepeater(): Repeater
    {
        return TableRepeater::make('collections')
            ->label('Collections')
            ->headers([
                Header::make('Name')->markAsRequired(),
                Header::make('Type')->markAsRequired(),
                Header::make('URL')->label('URL')->markAsRequired(),
                Header::make('Import Sources'),
                Header::make('Sync to Agent'),
                Header::make('Sync to Social'),
            ])
            ->schema([
                static::getFormFieldName(),
                static::getFormFieldType(CollectionType::class)
                    ->options(fn ($state) => $state ? Arr::only(CollectionType::asOptions(), [$state]) : [])
                    ->selectablePlaceholder(false)
                ,
                static::getFormFieldUrl()->extraInputAttributes(['readonly' => true]),
                Toggle::make('auto_import_sources')
                    ->disabled(fn (Get $get) => (!in_array($get('type'), [CollectionType::RSS->value, CollectionType::WEBSITE->value, CollectionType::CHANNEL->value, CollectionType::PODCAST->value])))
                    ->afterStateUpdated(function (Set $set, $state) {
                        if (!$state) {
                            $set('sync_agent', false);
                            $set('sync_social', false);
                        }
                    })
                    ->live()
                ,
                Toggle::make('sync_agent')
                    ->disabled(fn (Get $get) => (!$get('auto_import_sources') || !in_array($get('type'), [CollectionType::RSS->value, CollectionType::WEBSITE->value, CollectionType::CHANNEL->value, CollectionType::PODCAST->value])))
                ,
                Toggle::make('sync_social')
                    ->disabled(fn (Get $get) => (!$get('auto_import_sources') || !in_array($get('type'), [CollectionType::RSS->value, CollectionType::WEBSITE->value, CollectionType::CHANNEL->value, CollectionType::PODCAST->value])))
                ,
                Hidden::make('slug'),
                Hidden::make('external_id'),
            ])
            ->addable(false)
            ->reorderable(false)
            ->itemLabel(fn ($state) => $state['name'])
            ->required()
            ->hintIcon(
                'heroicon-s-question-mark-circle',
                tooltip: '
                    Collections are groups of Sources, such as a website, podcast, or YouTube channel.
                    You may configure Collections to automatically import new Sources on a daily basis.
                '
            )
            ->visible(fn ($state) => count($state) > 0)
            ->grid(2)
            ->columnSpan(2)
        ;
    }

    protected function getSourceImportRepeater(): Repeater
    {
        return TableRepeater::make('sources')
            ->label('Sources')
            ->headers([
                Header::make('Name')->markAsRequired(),
                Header::make('Type')->markAsRequired(),
                Header::make('URL')->label('URL')->markAsRequired(),
                Header::make('Index for Agent'),
                Header::make('List on Social'),
            ])
            ->schema([
                static::getFormFieldName(),
                static::getFormFieldType(SourceType::class)
                    ->options(fn ($state) =>
                        in_array($state, [SourceType::BOOK->value, SourceType::ARTICLE->value]) ?
                        Arr::only(SourceType::asOptions(), [SourceType::BOOK->value, SourceType::ARTICLE->value]) :
                        ($state ? Arr::only(SourceType::asOptions(), [$state]) : []),
                    )
                    ->selectablePlaceholder(false)
                ,
                static::getFormFieldUrl()
                    ->extraInputAttributes(['readonly' => true])
                    ->disabled(fn (Get $get) => in_array($get('type'), [SourceType::BOOK->value, SourceType::ARTICLE->value]))
                ,
                static::getFormFieldActive(
                    null,
                    'agent_active'
                ),
                static::getFormFieldActive(
                    null,
                    'social_active'
                ),
                Hidden::make('slug'),
                Hidden::make('external_id'),
                Hidden::make('media_url'),
                Hidden::make('file_index'),
            ])
            ->addable(false)
            ->reorderable(false)
            ->itemLabel(fn ($state) => $state['name'])
            ->required()
            ->hintIcon(
                'heroicon-s-question-mark-circle',
                tooltip: '
                    Sources are the content items themselves, such as a PDF book / article, blog post, podcast episode, or YouTube video.
                '
            )
            ->visible(fn ($state) => count($state) > 0)
            ->grid(2)
            ->columnSpan(2)
        ;
    }

    public function create(): void
    {

        $data = $this->form->getState();
//        Log::debug(json_encode($data, JSON_PRETTY_PRINT));

        // Loop through collections
        $numCollections = 0;
        $numCollectionsImported = 0;
        if (isset($data['collections'])) {
            $numCollections = count($data['collections']);
            foreach ($data['collections'] as $collection)
            {
                $attrs = [
                    'name' => $collection['name'],
                    'type' => $collection['type'],
                    'url' => $collection['url'] ?? null,
                    'referral_url' => $collection['url'] ?? null,
                    'social_active' => $collection['sync_social'] ?? false,
                    'auto_import_sources' => $collection['auto_import_sources'] ?? false,
                    'auto_transcribe_sources' => $collection['sync_agent'] ?? false,
                    'auto_index_sources' => $collection['sync_agent'] ?? false,
                    'auto_list_sources' => $collection['sync_social'] ?? false,
                    'auto_categorize_sources' => true,
                    'external_id' => $collection['external_id'] ?? null,
                    'slug' => Str::slug($collection['name']),
                ];
                if (Collection::create($attrs)) {
                    ++$numCollectionsImported;
                }
            }
        }

        // Loop through sources
        $numSources = 0;
        $numSourcesImported = 0;
        if (isset($data['sources'])) {

            $numSources = count($data['sources']);
            foreach ($data['sources'] as $source)
            {

                $attrs = [
                    'name' => $source['name'],
                    'type' => $source['type'],
                    'url' => $source['url'] ?? null,
                    'referral_url' => $source['url'] ?? null,
                    'agent_active' => $source['agent_active'] ?? false,
                    'social_active' => $source['social_active'] ?? false,
                    'media_url' => $source['media_url'] ?? null,
                    'external_id' => $source['external_id'] ?? null,
                    'slug' => Str::slug($source['name']),
                ];

                if (isset($source['file_index'])) {
                    $file = $data['files'][intval($source['file_index'])];
                    $fileField = str_ends_with($file, '.txt') ? 'text_path' : 'media_path';
                    $attrs[$fileField] = $file;
                }

//                Log::debug(json_encode($attrs, JSON_PRETTY_PRINT));
                if (Source::create($attrs)) {
                    ++$numSourcesImported;
                }

            }
        }

        Notification::make()
            ->title('Importing ...')
            ->success()
            ->icon('heroicon-s-arrow-up-on-square-stack')
            ->body("
                {$numCollectionsImported}/{$numCollections} Collection(s) and {$numSourcesImported}/{$numSources} Source(s) importing.
                This may take a few minutes, but you can continue working in the meantime.
            ")
            ->send()
        ;

        $this->redirect(Dashboard::getUrl(), true);

    }

}
