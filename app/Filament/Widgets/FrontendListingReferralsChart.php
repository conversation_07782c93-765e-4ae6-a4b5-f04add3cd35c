<?php

namespace App\Filament\Widgets;

use App\Models\FrontendListingReferral;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;

class FrontendListingReferralsChart extends FrontendListingAreaChart
{
    protected static ?int $sort = 32;

    protected ?string $description = "The number of times the 'Learn More' button of your {modelPhrase} been clicked on.";

    protected static string $dateColumn = 'referred_at';

    protected static ?string $chartId = 'frontend{model}ReferralsChart';

    protected static ?string $heading = 'Referrals';

    protected function getChartBaseQuery(): Builder
    {
        return FrontendListingReferral::join($this->getModelTable(), 'frontend_listing_referrals.item_id', '=', "{$this->getModelTable()}.id")
            ->where("{$this->getModelTable()}.team_id", get_current_team_id());
    }

}
