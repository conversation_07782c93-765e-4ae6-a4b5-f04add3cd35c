<?php

namespace App\Filament\Widgets;

use Alaouy\Youtube\Facades\Youtube;
use App\Enums\SourceType;
use App\Enums\CollectionType;
use App\Filament\Pages\App\Dashboard;
use App\Filament\Resources\BookSourceResource;
use App\Filament\Traits\HasFields;
use App\Jobs\ImportBookSourceDataJob;
use App\Models\Source;
use App\Services\AmazonClient;
use App\Services\YoutubeClient;
use Awcodes\Shout\Components\Shout;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Support\Enums\Alignment;
use Filament\Widgets\Widget;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use PhanAn\Poddle\Poddle;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;

class DashboardIntro extends Widget
{

    protected static string $view = 'components.app.dashboard-intro';

    protected static ?int $sort = 1;

    protected int|string|array $columnSpan = 'full';

    public static bool $isDiscovered = false;

}
