<?php

namespace App\Filament\Widgets;

use App\Enums\CollectionType;
use App\Filament\Resources\CollectionResource;
use App\Models\Collection;

class PendingCollectionsTable extends PendingApprovalTable
{
    protected static ?int $sort = 82;

    protected string $title = 'Collections Pending Approval';

    protected string $resource = CollectionResource::class;

    protected static ?string $model = Collection::class;

    protected function getTypeOptions(): array
    {
        return CollectionType::asOptions();
    }
}
