<?php

namespace App\Filament\Widgets;

use Illuminate\Support\Str;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

abstract class ApexChart extends ApexChartWidget
{
    protected int|string|array $columnSpan = 'full';

    public ?string $filter = '30';

    protected const MAX_ITEMS = 10;

    /**
     * @var array|string[]
     */
    protected array $colors = [
        '#7137ff',
        '#cc00db',
        '#fe00b2',
        '#ff0088',
        '#ff0d62',
        '#ff5840',
        '#ff8320',
        '#ffa600',
    ];

    /**
     * @var array|string[]
     */
    protected array $filters = [
        '7' => 'Last 7 Days',
        '30' => 'Last 30 Days',
        'month' => 'This Month',
        'last_month' => 'Last Month',
        '90' => 'Last 90 Days',
        '183' => 'Last 6 Months',
        'year' => 'This Year',
        'last_year' => 'Last Year',
        'all' => 'All Time',
    ];

    protected static ?string $loadingIndicator = 'Loading...';

    /**
     * @return string[]|null
     */
    protected function getFilters(): ?array
    {
        return $this->filters;
    }

    /**
     * @return array|string[]
     */
    protected function getColors(): array
    {
        return $this->colors;
    }

    protected function getChartId(): ?string
    {
        return Str::snake(class_basename(static::class), '-');
    }

}
