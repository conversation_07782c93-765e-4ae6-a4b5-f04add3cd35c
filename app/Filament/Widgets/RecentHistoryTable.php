<?php

namespace App\Filament\Widgets;

use Altek\Accountant\Models\Ledger;
use App\Filament\Traits\HasActions;
use App\Filament\Traits\HasColumns;
use App\Models\Agent;
use App\Models\Collection;
use App\Models\Contributor;
use App\Models\Discussion;
use App\Models\Organization;
use App\Models\Source;
use App\Services\Auditor;
use App\Services\Formatter;
use App\Services\Gatekeeper;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Carbon;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class RecentHistoryTable extends BaseWidget
{
    use HasActions;
    use HasColumns;

    public static bool $isDiscovered = false;

    protected static ?int $sort = 90;

    protected int|string|array $columnSpan = 'full';

    public static function canView(): bool
    {
        return static::getTableBaseQuery()->count() > 0;
    }

    public function table(Table $table): Table
    {

        $urlClosure = function ($record) {
            $model = $record->recordable;
            $resource = '\\App\\Filament\\Resources\\'.class_basename($model).'Resource';
            if (! empty($model->type) && ($model::class != Organization::class)) {
                $resourceClass = str_replace(
                    class_basename($resource),
                    Str::title($model->type->value).class_basename($resource),
                    $resource
                );
            } else {
                $resourceClass = $resource;
            }

            return call_user_func(
                "{$resourceClass}::getUrl",
                'edit',
                [
                    'tenant' => get_current_team_id(),
                    'record' => $model->id,
                ],
                panel: 'app'
            );
        };

        return $table
            ->heading(false)
            ->query(static::getTableBaseQuery())
            ->columns([
                Tables\Columns\TextColumn::make('recordable.name')
                    ->label(new HtmlString("<span class='text-base'>Recent Activity</span>"))
                    ->formatStateUsing(function (string $state, ?Model $record) {
                        $since = Carbon::parse($record->created_at)->diffForHumans();
                        $datetime = Formatter::datetime($record->created_at);
                        $event = strtolower(Auditor::getActionLabel($record->event));
                        $recordableType = strtolower(implode(' ', Str::ucsplit(class_basename($record->recordable_type))));

                        return new HtmlString("
                            <span class='text-gray-500 dark:text-gray-400'>
                                <span class='text-base block'>
                                    <span class='text-black dark:text-white font-bold'>{$record->recordable->name}</span>
                                    ({$recordableType})
                                </span>
                                <span class='block'>
                                    {$event} by {$record->user->name}
                                    <time title='{$datetime}' datetime='{$datetime}'>{$since}</time>
                                </span>
                            </span>
                        ");
                    })
                ,
            ])
            ->actions([
                static::getTableActionEditRow()
                    ->url($urlClosure),
            ])
            ->recordUrl($urlClosure);

    }

    protected static function getTableBaseQuery(): Builder
    {
        return Ledger::with([
            'recordable' => function (MorphTo $morphTo) {
                $morphTo->withTrashed();
            },
            'user' => function (BelongsTo $belongsTo) {
                $belongsTo->withTrashed();
            },
        ])
            ->whereIn(
                'recordable_type',
                [
                    Agent::class,
                    Collection::class,
                    Contributor::class,
                    Discussion::class,
                    Organization::class,
                    Source::class,
                ]
            )
            ->whereHasMorph(
                'recordable',
                '*',
                function (Builder $query, string $type) {
                    if (
                        !Gatekeeper::canModerateContent() &&
                        in_array(
                            $type,
                            [
                                Agent::class,
                                Collection::class,
                                Contributor::class,
                                Discussion::class,
                                Organization::class,
                                Source::class,
                            ]
                        )
                    ) {
                        $query->where('team_id', get_current_team_id());
                    }
                })
            ->orderBy('created_at', 'desc');
    }
}
