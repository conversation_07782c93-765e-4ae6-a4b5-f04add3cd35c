<?php

namespace App\Filament\Widgets;

use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

abstract class AreaChart extends ApexChart
{

    protected function setInterval($data, $column, $minDate, $aggregate = 'count'): mixed
    {

        if (is_numeric($this->filter)) {

            $numDays = intval($this->filter);
            $data->between(
                start: now()->subtract('day', $numDays),
                end: now(),
            );
            $data->perDay();

        } else {

            $start = match ($this->filter) {
                'month' => now()->startOfMonth(),
                'last_month' => now()->addMonths(-1)->startOfMonth(),
                'year' => now()->startOfYear(),
                'last_year' => now()->addYear(-1)->startOfYear(),
                'all' => new Carbon($minDate),
            };

            $end = match ($this->filter) {
                'month' => now(),
                'last_month' => now()->addMonths(-1)->endOfMonth(),
                'year' => now(),
                'last_year' => now()->addYear(-1)->endOfYear(),
                'all' => now(),
            };

            $data->between(
                start: $start,
                end: $end,
            );

            if ($start->diffInDays($end, true) <= 183) {
                $data->perDay();
            } else {
                $data->perMonth();
            }

        }

        return $data->aggregate($column, $aggregate);

    }

    protected function getChartOptions(array $datasets, Collection $labels): array
    {
        return [
            'chart' => [
                'type' => 'area',
                'height' => 400,
                'stacked' => true,
            ],
            'series' => $datasets,
            'xaxis' => [
                'categories' => $labels,
                'type' => 'datetime',
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'yaxis' => [
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'colors' => $this->getColors(),
            'stroke' => [
                'curve' => 'smooth',
            ],
            'dataLabels' => [
                'enabled' => false,
            ],

        ];
    }

}
