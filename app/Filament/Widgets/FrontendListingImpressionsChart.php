<?php

namespace App\Filament\Widgets;

use App\Models\FrontendListingImpression;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;

class FrontendListingImpressions<PERSON>hart extends FrontendListingAreaChart
{
    protected static string $view = 'filament-apex-charts::widgets.apex-chart-widget';

    protected static ?int $sort = 31;

    protected ?string $description = 'The number of times your {modelPhrase} appeared in search results.';

    protected static string $dateColumn = 'viewed_at';

    protected static ?string $chartId = 'frontend{model}ImpressionsChart';

    protected static ?string $heading = 'Impressions';

    protected function getChartBaseQuery(): Builder
    {
        return FrontendListingImpression::join($this->getModelTable(), 'frontend_listing_impressions.item_id', '=', "{$this->getModelTable()}.id")
            ->where("{$this->getModelTable()}.team_id", get_current_team_id());
    }

}
