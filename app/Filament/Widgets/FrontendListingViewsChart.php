<?php

namespace App\Filament\Widgets;

use App\Models\FrontendListingView;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;

class FrontendListingViewsChart extends FrontendListingAreaChart
{
    protected static ?int $sort = 33;

    protected ?string $description = 'The number of times your {modelPhrase} been viewed.';

    protected static string $dateColumn = 'viewed_at';

    protected static ?string $chartId = 'frontend{model}ViewsChart';

    protected static ?string $heading = 'Views';

    protected function getChartBaseQuery(): Builder
    {
        return FrontendListingView::join($this->getModelTable(), 'frontend_listing_views.item_id', '=', "{$this->getModelTable()}.id")
            ->where("{$this->getModelTable()}.team_id", get_current_team_id());
    }

}
