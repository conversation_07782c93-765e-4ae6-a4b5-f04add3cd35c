<?php

namespace App\Filament\Api;

use App\Filament\Resources\SourceResource;
use App\Services\Formatter;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Rupadana\ApiService\ApiService as BaseApiService;

class ApiService extends BaseApiService
{
    protected static ?string $resource = SourceResource::class;

    public static function handlers(): array
    {
        $reflect = new \ReflectionClass(get_called_class());
        $resourceDir = substr($reflect->getName(), 0, -1 * strlen($reflect->getShortName()));
        $path = "\\{$resourceDir}Handlers\\";

        return Arr::map(static::getActions(), function (string $value) use ($path) {
            return "{$path}{$value}Handler";
        });
    }

    /**
     * @return string[]
     */
    protected static function getActions(): array
    {
        return [
            'Create',
            'Update',
            'Delete',
            'Pagination',
            'Detail',
        ];
    }

    public static function transformResource(array $attrs, ?Model $resource = null): array
    {

        if (! is_null($resource)) {
            $attrs = Arr::only(
                $attrs,
                array_merge(
                    $resource::$allowedFields,
                    $resource::$allowedIncludes
                )
            );
        }

        foreach ($attrs as $key => $val) {

            // If this is a sub-resource, transform the sub-resource
            if (is_array($val)) {
                $attrs[$key] = static::transformResource($val);
            } else {

                // Convert paths to public URLs
                if (str_ends_with($key, '_path')) {
                    $newKey = substr($key, 0, -5).'_url';
                    $attrs[$newKey] = get_path_url($val);
                    unset($attrs[$key]);
                }

                // Strip millisecond precision from timestamps
                if (str_ends_with($key, '_at')) {
                    if (is_int($val)) {
                        $val = Carbon::createFromTimestamp($val)->toISOString();
                    }
                    $attrs[$key] = str_replace('.000000', '', $val);
                }

                // Strip anything but days from dates
                if (str_ends_with($key, '_on')) {
                    $attrs[$key] = Formatter::date($val);
                }

                // Paper over any blank strings ;(
                if (empty($val)) {
                    $attrs[$key] = null;
                }

            }

        }

        return $attrs;

    }
}
