<?php

namespace App\Filament\Api\Handlers;

use App\Filament\Api\ApiService;
use Illuminate\Http\Request;
use Rupadana\ApiService\Http\Handlers;

class CreateHandler extends Handler
{
    public static ?string $uri = '/';

    public static ?string $resource = null;

    public static function getMethod()
    {
        return Handlers::POST;
    }

    public static function getModel()
    {
        return static::$resource::getModel();
    }

    public function handler(Request $request)
    {
        $model = new (static::getModel());

        if (static::$restrictToTeams) {
            if (! is_null(static::$parentTeamModel)) {
                abort_if(
                    ! in_array(
                        $request->get((new static::$parentTeamModel)->getForeignKey()),
                        static::$parentTeamModel::whereIn('team_id', static::getAllowedTeamIds())->select('id')->pluck('id')->toArray()
                    ),
                    403,
                );
            } else {
                abort_if(
                    ! in_array($request->get('team_id'), static::getAllowedTeamIds()),
                    403,
                );
            }
        }

        $model->fill($request->all());

        $model->save();

        return static::sendSuccessResponse(ApiService::transformResource($model->toArray()), 'Resource successfully created');
    }
}
