<?php

namespace App\Filament\Api\Handlers;

use App\Models\Membership;
use App\Services\Gatekeeper;
use Illuminate\Database\Eloquent\Model;
use Rupadana\ApiService\Http\Handlers;

class Handler extends Handlers
{
    protected static bool $restrictToTeams = true;

    protected static ?string $parentTeamModel = null;

    protected static function getEloquentQuery(): mixed
    {

        $query = parent::getEloquentQuery();

        // Restrict access to user's teams if the resource is restricted and this isn't a superadmin
        if (static::$restrictToTeams && ! Gatekeeper::isSuperadmin(auth()->user())) {

            $teamIds = static::getAllowedTeamIds();

            if (! is_null(static::$parentTeamModel)) {
                $query->whereIn(
                    (new static::$parentTeamModel)->getForeignKey(),
                    static::$parentTeamModel::whereIn('team_id', $teamIds)->select('id')
                );
            } else {
                $query->whereIn('team_id', $teamIds);
            }

        }

        return $query;

    }

    protected static function getAllowedTeamIds(): array
    {
        return Membership::where('model_id', auth()->user()->id)
            ->whereNotNull('team_id')
            ->pluck('team_id')
            ->toArray();
    }

    protected static function abortIfModelNotAllowed(Model $model): void
    {
        if (! Gatekeeper::isSuperadmin(auth()->user())) {
            if (static::$restrictToTeams) {
                if (! is_null(static::$parentTeamModel)) {
                    $foreignKey = (new static::$parentTeamModel)->getForeignKey();
                    abort_if(
                        ! in_array(
                            $model->$foreignKey,
                            static::$parentTeamModel::whereIn('team_id', static::getAllowedTeamIds())->select('id')->pluck('id')->toArray()
                        ),
                        403,
                    );
                } else {
                    abort_if(
                        ! in_array($model->team_id, static::getAllowedTeamIds()),
                        403,
                    );
                }
            }
        }
    }
}
