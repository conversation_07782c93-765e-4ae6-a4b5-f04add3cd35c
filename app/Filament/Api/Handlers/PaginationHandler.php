<?php

namespace App\Filament\Api\Handlers;

use App\Models\Source;
use App\Sorts\NumViewsSort;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\QueryBuilder\AllowedFilter;
use <PERSON><PERSON>\QueryBuilder\AllowedSort;
use <PERSON><PERSON>\QueryBuilder\Enums\SortDirection;
use Spatie\QueryBuilder\QueryBuilder;

class PaginationHandler extends Handler
{
    public static ?string $uri = '/';

    public static ?string $resource = null;

    public function handler()
    {

        //        Log::debug(print_r(request()->all(), true));

        $query = static::getEloquentQuery();
        $model = static::getModel();

        if (in_array('categories.id', $model::$allowedFilters)) {
            $query->with('categories');
        }

        $filters = [];
        foreach ($model::$allowedFilters as $field) {
            if (
                str_ends_with($field, '.id') ||
                str_ends_with($field, '_id') ||
                str_starts_with($field, 'is_') ||
                str_ends_with($field, 'slug')
            ) {
                $filters[] = AllowedFilter::exact($field)->nullable();
            } else {
                $filters[] = AllowedFilter::partial($field)->nullable();
            }
        }

        $sorts = [];
        foreach ($model::$allowedSorts as $field) {
            if ($field == 'num_views') {
                $sorts[] = AllowedSort::custom('num_views', new NumViewsSort, $model)->defaultDirection(SortDirection::DESCENDING);
            } else {
                $sorts[] = $field;
            }
        }

        $query = QueryBuilder::for($query)
            ->defaultSort($model::$defaultSort)
            ->allowedFields($model::$allowedFields ?? [])
            ->allowedSorts($sorts)
            ->allowedFilters($filters)
            ->allowedIncludes($model::$allowedIncludes ?? null);

        $groupBy = request()->input('group_by');
        if (! empty($groupBy)) {

            if (
                ($groupBy == 'collection_source.collection_id') &&
                ($model == Source::class)
            ) {
                $type = request()->input('filter')['type'];
                $query->whereRaw("
                    sources.id IN (
                        SELECT
			                MAX(s2.id)
		                FROM
			                sources s2 LEFT JOIN collection_source cs ON s2.id = cs.source_id
                        WHERE
                            s2.is_approved = 1 AND
                            s2.social_active = 1 AND
                            s2.weight < 5 AND
                            s2.type = '{$type}'
		                GROUP BY
			                IFNULL(cs.collection_id,UUID())
                    )
                ");
            }
        }

        //        Log::debug($query->toRawSql());

        $paginatedResults = $query
            ->paginate(request()->query('page_size'), page: request()->query('page'))
            ->appends(request()->query());

        return static::getApiTransformer()::collection($paginatedResults);
    }

    protected function addGroupSort(QueryBuilder $query, string $table, string $sort): QueryBuilder
    {
        if (str_starts_with($sort, '-')) {
            $direction = 'desc';
            $aggFn = 'MAX';
            $sort = substr($sort, 1);
        } else {
            $direction = 'asc';
            $aggFn = 'MIN';
        }

        return $query->reorder()
            ->orderBy(DB::raw("{$aggFn}({$table}.{$sort})"), $direction);
    }
}
