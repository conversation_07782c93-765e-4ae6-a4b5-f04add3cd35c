<?php

namespace App\Filament\Api\Handlers;

use App\Filament\Api\ApiService;
use Illuminate\Http\Request;
use Rupadana\ApiService\Http\Handlers;

class UpdateHandler extends Handler
{
    public static ?string $uri = '/{id}';

    public static ?string $resource = null;

    public static function getMethod()
    {
        return Handlers::PUT;
    }

    public static function getModel()
    {
        return static::$resource::getModel();
    }

    public function handler(Request $request)
    {
        $id = $request->route('id');

        $model = static::getModel()::find($id);

        if (! $model) {
            return static::sendNotFoundResponse();
        }

        static::abortIfModelNotAllowed($model);

        $model->fill($request->all());

        $model->save();

        return static::sendSuccessResponse(ApiService::transformResource($model->toArray()), 'Resource successfully updated');
    }
}
