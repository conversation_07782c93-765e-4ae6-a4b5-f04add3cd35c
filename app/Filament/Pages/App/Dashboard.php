<?php

namespace App\Filament\Pages\App;

use App\Filament\Traits\HasProductTour;
use App\Filament\Widgets\DashboardIntro;
use App\Filament\Widgets\PendingAgentsTable;
use App\Filament\Widgets\PendingCollectionsTable;
use App\Filament\Widgets\PendingContributorsTable;
use App\Filament\Widgets\PendingOrganizationsTable;
use App\Filament\Widgets\PendingSourcesTable;
use App\Filament\Widgets\RecentHistoryTable;
use Filament\Pages\Dashboard as BaseDashboard;
use JibayMcs\FilamentTour\Tour\HasTour;

class Dashboard extends BaseDashboard
{
    use HasProductTour;
    use HasTour;

    protected array $extraBodyAttributes = [
        'class' => 'page-dashboard',
    ];

    public function getHeaderWidgets(): array
    {
        return [
            DashboardIntro::class,
            PendingAgentsTable::class,
            PendingSourcesTable::class,
            PendingCollectionsTable::class,
            PendingContributorsTable::class,
            PendingOrganizationsTable::class,
            RecentHistoryTable::class,
        ];
    }

}
