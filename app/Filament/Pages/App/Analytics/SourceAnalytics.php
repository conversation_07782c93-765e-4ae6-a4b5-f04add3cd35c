<?php

namespace App\Filament\Pages\App\Analytics;

use App\Models\Source;

class SourceAnalytics extends Analytics
{

    protected static string $routePath = 'sources/analytics';
    protected static ?string $title = 'Content Analytics';
    protected static ?string $navigationLabel = 'Content';
//    protected static string $view = 'pages.app.analytics.listings';

    protected function getViewData(): array
    {
        return [
            'modelClass' => Source::class,
            'modelId' => request()->get('model_id', null),
            'modelLabel' => 'Content'
        ];
    }

}
