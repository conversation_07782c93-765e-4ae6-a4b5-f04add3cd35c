<?php

namespace App\Filament\Pages\App\Analytics;

use App\Livewire\AgentsTimeSeriesSwitcher;
use App\Models\Collection;

class AgentAnalytics extends Analytics
{

    protected static string $routePath = 'agents/analytics';
    protected static ?string $title = 'Agent Analytics';
    protected static ?string $navigationLabel = 'Agents';
//    protected static string $view = 'pages.app.analytics.agents';

    protected function getViewData(): array
    {
        return [
            'modelClass' => Collection::class,
            'modelId' => request()->get('model_id', null),
            'modelLabel' => request()->has('model_id') ? 'Collection' : 'Collections'
        ];
    }

    public function getFooterWidgets(): array
    {
        return [
            AgentsTimeSeriesSwitcher::class,
        ];
    }

}
