<?php

namespace App\Filament\Pages\App\Analytics;

use App\Models\Contributor;

class ContributorAnalytics extends Analytics
{

    protected static string $routePath = 'contributors/analytics';
    protected static ?string $title = 'Contributor Analytics';
    protected static ?string $navigationLabel = 'Contributor';
//    protected static string $view = 'pages.app.analytics.listings';

    protected function getViewData(): array
    {
        return [
            'modelClass' => Contributor::class,
            'modelId' => request()->get('model_id', null),
            'modelLabel' => request()->has('model_id') ? 'Contributor' : 'Contributors'
        ];
    }

}
