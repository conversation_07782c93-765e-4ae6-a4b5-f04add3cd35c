<?php

namespace App\Filament\Pages\App\Analytics;

use App\Filament\Widgets\DonutChartWidget;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Pages\Dashboard;
use Filament\Pages\Dashboard\Concerns\HasFiltersForm;
use Filament\Pages\Page;

class Analytics extends Dashboard
{
    use HasFiltersForm;

    protected static ?string $navigationIcon = 'heroicon-o-bar-chart-square';

    protected array $extraBodyAttributes = [
        'class' => 'page-analytics',
    ];

    public function filtersForm(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        DatePicker::make('startDate'),
                        DatePicker::make('endDate'),
                        // ...
                    ])
                    ->columns(3),
            ])
        ;
    }

    public function getFooterWidgets(): array
    {
        return [
//            DonutChartWidget::class,
        ];
    }

}
