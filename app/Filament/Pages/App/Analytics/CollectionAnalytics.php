<?php

namespace App\Filament\Pages\App\Analytics;

use App\Models\Collection;

class CollectionAnalytics extends Analytics
{

    protected static string $routePath = 'collections/analytics';
    protected static ?string $title = 'Collection Analytics';
    protected static ?string $navigationLabel = 'Collections';
//    protected static string $view = 'pages.app.analytics.listings';

    protected function getViewData(): array
    {
        return [
            'modelClass' => Collection::class,
            'modelId' => request()->get('model_id', null),
            'modelLabel' => request()->has('model_id') ? 'Collection' : 'Collections'
        ];
    }

}
