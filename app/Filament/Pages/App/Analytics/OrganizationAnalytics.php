<?php

namespace App\Filament\Pages\App\Analytics;

use App\Models\Organization;

class OrganizationAnalytics extends Analytics
{

    protected static string $routePath = 'organizations/analytics';
    protected static ?string $title = 'Organization Analytics';
    protected static ?string $navigationLabel = 'Organization';
//    protected static string $view = 'pages.app.analytics.listings';

    protected function getViewData(): array
    {
        return [
            'modelClass' => Organization::class,
            'modelId' => request()->get('model_id', null),
            'modelLabel' => request()->has('model_id') ? 'Organization' : 'Organizations'
        ];
    }

}
