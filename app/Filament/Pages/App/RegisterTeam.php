<?php

namespace App\Filament\Pages\App;

use App\Filament\Resources\TeamResource;
use App\Filament\Traits\HasFields;
use App\Models\Team;
use Filament\Forms\Form;
use Filament\Pages\Tenancy\RegisterTenant;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class RegisterTeam extends RegisterTenant
{
    use HasFields;

    protected static string $view = 'pages.app.register-team';

    public static function getLabel(): string
    {
        return 'Create Your Team';
    }

    public function getSubheading(): string|Htmlable|null
    {
        return new HtmlString(view('components.app.register-team-intro'));
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                static::getFormFieldName('Team Name')
                    ->unique(ignoreRecord: true),
            ])
            ->columns(1);
    }

    protected function handleRegistration(array $data): Team
    {
        $team = Team::create($data);

        return $team;
    }

    public static function canView(): bool
    {
        return auth()->check();
    }

    public function hasLogo(): bool
    {
        return true;
    }

    protected function getRedirectUrl(): ?string
    {
        return TeamResource::getUrl(
            'edit',
            [
                'tenant' => $this->tenant->id,
                'record' => $this->tenant->id,
                'new' => true,
            ]
        );
    }
}
