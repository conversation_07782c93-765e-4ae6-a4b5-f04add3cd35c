<?php

namespace App\Filament\Pages\Id;

use App\Filament\Widgets\Products;
use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static string $view = 'pages.id.dashboard';

    public function getTitle(): string
    {
        return '';
    }

    public function getHeaderWidgets(): array
    {
        return [
            Products::class,
        ];
    }
}
