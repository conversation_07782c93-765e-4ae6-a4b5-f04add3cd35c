<?php

namespace App\Filament\Pages\Id;

use Awcodes\Shout\Components\Shout;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Form;
use Filament\Pages\Auth\Login as BaseLogin;

class Login extends BaseLogin
{
    public function mount(): void
    {

        parent::mount();
    }

    /**
     * @return array<int | string, string | Form>
     */
    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema([
                        $this->getLogoutNotice(),
                        $this->getRedirectNotice(),
                        $this->getEmailFormComponent(),
                        $this->getPasswordFormComponent(),
                        $this->getRememberFormComponent(),
                        $this->getRedirectFormComponent(),
                    ])
                    ->statePath('data'),
            ),
        ];
    }

    protected static function getRedirectNotice(): Shout
    {
        return Shout::make('redirect_notice')
            ->visible(fn () => ! empty(request()->get('redirect')))
            ->content('You must sign in to the Apologist platform first.')
            ->type('warning');
    }

    protected static function getLogoutNotice(): Shout
    {
        return Shout::make('logout_notice')
            ->visible(fn () => ! empty(request()->get('logout')))
            ->content('You have been signed out of the Apologist platform.')
            ->type('warning');
    }

    protected function getRedirectFormComponent(): Hidden
    {
        return Hidden::make('redirect_url')
            ->afterStateHydrated(function (Hidden $component) {
                $component->state(request()->get('redirect_url'));
            });
    }
}
