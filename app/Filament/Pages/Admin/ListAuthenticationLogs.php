<?php

namespace App\Filament\Pages\Admin;

use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasProductTour;
use Exception;
use Filament\Forms\Components\DatePicker;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use JibayMcs\FilamentTour\Tour\HasTour;
use Tapp\FilamentAuthenticationLog\Resources\AuthenticationLogResource\Pages\ListAuthenticationLogs as BaseListAuthenticationLogs;

class ListAuthenticationLogs extends BaseListAuthenticationLogs
{
    use HasColumns;
    use HasProductTour;
    use HasTour;

    /**
     * @throws Exception
     */
    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\IconColumn::make('login_successful')
                    ->label('')
                    ->boolean(),
                Tables\Columns\TextColumn::make('authenticatable.name'),
                static::getTableColumnSince('login_at', 'Attempted At', true),
                static::getTableColumnSince('logout_at', 'Logout At', true)
                    ->visibleFrom('md'),
                Tables\Columns\TextColumn::make('ip_address')
                    ->label(trans('filament-authentication-log::filament-authentication-log.column.ip_address'))
                    ->searchable()
                    ->sortable()
                    ->visibleFrom('md'),
                Tables\Columns\TextColumn::make('user_agent')
                    ->label(trans('filament-authentication-log::filament-authentication-log.column.user_agent'))
                    ->searchable()
                    ->sortable()
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= $column->getCharacterLimit()) {
                            return null;
                        }

                        return $state;
                    })
                    ->visibleFrom('xl'),
            ])
            ->actions([
                //
            ])
            ->filters([
                Filter::make('login_successful')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->where('login_successful', true)),
                Filter::make('login_at')
                    ->form([
                        DatePicker::make('login_from'),
                        DatePicker::make('login_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['login_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('login_at', '>=', $date),
                            )
                            ->when(
                                $data['login_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('login_at', '<=', $date),
                            );
                    }),
                Filter::make('cleared_by_user')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->where('cleared_by_user', true)),
            ])
            ->defaultSort('login_at', 'desc');
    }
}
