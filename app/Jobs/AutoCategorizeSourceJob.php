<?php

namespace App\Jobs;

use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasHighTimeout;
use App\Jobs\Traits\HasTags;
use App\Jobs\Traits\IsUnique;
use App\Models\Source;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class AutoCategorizeSourceJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use HasHighTimeout, HasTags, IsUnique;

    /**
     * Create a new job instance.
     */
    public function __construct(public Source $source, public array $categories = [])
    {
        $this->onQueue(env('QUEUE_LOW', 'default'));
    }

    /**
     * Execute the job.
     *
     * @throws GuzzleException
     */
    public function handle(): void
    {
        Log::debug(class_basename(static::class));
        $categoryIds = array_merge(
            $this->source->subcategories()->pluck('categories.id')->toArray(),
            $this->source->suggestCategories(null, $this->categories)
        );
        $this->source->categories()->sync($categoryIds);
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::CATEGORIZE, JobCategoryTag::IMPORT], $this->source);
    }

    public function uniqueId(): string
    {
        return $this->generateUniqueKey($this->source->id);
    }

}
