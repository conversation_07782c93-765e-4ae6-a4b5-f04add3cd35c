<?php

namespace App\Jobs;

use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasTags;
use App\Jobs\Traits\IsUnique;
use App\Models\Model;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Spatie\Translatable\HasTranslations;

class TranslateModelJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use HasTags, IsUnique;

    /**
     * Create a new job instance.
     */
    public function __construct(public Model $model, public array $languages = [], public array $relations = [])
    {
        $this->onQueue(env('QUEUE_DEFAULT', 'default'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        Log::debug(class_basename(static::class));
        if (uses_trait($this->model, HasTranslations::class)) {

            $oldTranslations = $this->model->getTranslations();
            foreach ($oldTranslations as $field => $translations) {
                dispatch(new TranslateModelFieldJob($this->model, $field, $this->languages));
            }

            foreach ($this->relations as $relation) {
                $models = $this->model->$relation;
                foreach ($models as $model) {
                    dispatch(new TranslateModelJob($model, $this->languages));
                }
            }

        } else {
            $this->fail();
        }

    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::TRANSLATE], $this->model);
    }

    public function uniqueId(): string
    {
        return $this->generateUniqueKey([basename($this->model::class), $this->model->id]);
    }
}
