<?php

namespace App\Jobs;

use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasHighTimeout;
use App\Jobs\Traits\HasTags;
use App\Jobs\Traits\IsUnique;
use App\Models\Collection;
use App\Models\Source;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ImportCollectionSourceJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use HasHighTimeout, HasTags, IsUnique;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Collection $collection,
        public array $attrs,
        public string $uniqueField,
        public string $uniqueId,
        public array $contributors = [],
        public string $contributorRole = 'author'
    ) {
        $this->onQueue(env('QUEUE_DEFAULT', 'default'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::debug(class_basename(static::class));
        if (! Source::withTrashed()->where('team_id', $this->collection->team_id)->where($this->uniqueField, $this->uniqueId)->exists()) {
            $this->collection->importSource(
                $this->attrs,
                $this->contributors,
                $this->contributorRole,
                $this->collection->enforce_source_categories,
                $this->collection->auto_categorize_sources,
                $this->collection->auto_transcribe_sources
            );
        }
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::IMPORT], $this->collection);
    }

    public function uniqueId(): string
    {
        return $this->generateUniqueKey([$this->collection->id, $this->uniqueId]);
    }
}
