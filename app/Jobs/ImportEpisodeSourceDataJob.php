<?php

namespace App\Jobs;

use App\Enums\JobCategoryTag;
use App\Enums\SourceType;
use App\Jobs\Traits\HasHighTimeout;
use App\Jobs\Traits\HasTags;
use App\Jobs\Traits\IsUnique;
use App\Models\Source;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ImportEpisodeSourceDataJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use HasHighTimeout, HasTags, IsUnique;

    /**
     * Create a new job instance.
     */
    public function __construct(public Source $source, public bool $override = false, public bool $useProxy = false)
    {
        $this->onQueue(env('QUEUE_DEFAULT', 'default'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::debug(class_basename(static::class));
        if ($this->source->type == SourceType::EPISODE) {
            $this->source->importPodcastEpisodeFromUrl($this->override, ($this->useProxy || ($this->attempts() > 1)));
        }
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::IMPORT], $this->source);
    }

    public function uniqueId(): string
    {
        return $this->generateUniqueKey($this->source->id);
    }
}
