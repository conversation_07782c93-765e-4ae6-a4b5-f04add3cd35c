<?php

namespace App\Jobs;

use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasTags;
use App\Jobs\Traits\IsUnique;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncFrontendListingUrlJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use HasTags, IsUnique;

    /**
     * Create a new job instance.
     */
    public function __construct(public Model $model)
    {
        $this->onQueue(env('QUEUE_HIGH', 'default'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::debug(class_basename(static::class));
        $this->model->syncFrontendListingUrl();
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::SYNC, JobCategoryTag::PUBLISH], $this->model);
    }

    public function uniqueId(): string
    {
        return $this->generateUniqueKey([basename($this->model::class), $this->model->id]);
    }
}
