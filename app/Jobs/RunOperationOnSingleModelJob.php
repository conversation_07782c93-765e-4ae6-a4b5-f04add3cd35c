<?php

namespace App\Jobs;

use App\Jobs\Traits\HasTags;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Laravel\SerializableClosure\SerializableClosure;

class RunOperationOnSingleModelJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use HasTags;

    /**
     * Create a new job instance.
     */
    public function __construct(public Model $model, public SerializableClosure $fnOperation)
    {
        $this->onQueue(env('QUEUE_HIGH', 'default'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::debug(class_basename(static::class));
        $closure = $this->fnOperation->getClosure();
        $closure($this->model);
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([], $this->model);
    }
}
