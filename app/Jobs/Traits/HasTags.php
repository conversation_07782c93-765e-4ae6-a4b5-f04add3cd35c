<?php

namespace App\Jobs\Traits;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

trait HasTags
{
    protected function getModelTag(Model $model): string
    {
        $modelName = strtolower(class_basename($model));

        return "model:{$modelName}";
    }

    protected function getIdTag(Model $model): string
    {
        return "id:{$model->id}";
    }

    protected function getNameTag(Model $model): string
    {
        $name = Str::slug($model->name);

        return "name:{$name}";
    }

    protected function getTypeTag(Model $model): string
    {
        return "type:{$model->type->value}";
    }

    protected function getCategoryTag($category): string
    {
        return "category:{$category->value}";
    }

    protected function getJobClassTag(): string
    {
        $className = class_basename($this);

        return "job:{$className}";
    }

    protected function getHostTag(): string
    {
        $host = gethostname();

        return "host:{$host}";
    }

    protected function generateTags(array $categories = [], $model = null): array
    {

        $tags = [
            $this->getJobClassTag(),
            $this->getHostTag(),
        ];

        foreach ($categories as $category) {
            $tags[] = $this->getCategoryTag($category);
        }

        if (! is_null($model)) {
            $tags[] = $this->getModelTag($model);
            $tags[] = $this->getIdTag($model);
            if (! empty($model->name)) {
                $tags[] = $this->getNameTag($model);
            }
            if (! empty($model->type)) {
                $tags[] = $this->getTypeTag($model);
            }
        }

        return $tags;

    }
}
