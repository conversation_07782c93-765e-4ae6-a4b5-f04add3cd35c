<?php

namespace App\Jobs;

use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasTags;
use App\Jobs\Traits\IsUnique;
use App\Models\Source;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ImportSourceTranscriptJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use HasTags, IsUnique;

    const IMPORT_CHECK_BACKOFF = [
        1,
        3,
        5,
        10,
        30,
        60,
        180,
    ];

    /**
     * Create a new job instance.
     */
    public function __construct(public Source $source, public int $currentBackoff = 1)
    {
        $this->onQueue(env('QUEUE_LOW', 'default'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::debug(class_basename(static::class));
        $currentBackoffIndex = array_search($this->currentBackoff, self::IMPORT_CHECK_BACKOFF);
        if ($currentBackoffIndex !== false) {
            $nextBackoffIndex = $currentBackoffIndex + 1;
            if (isset(self::IMPORT_CHECK_BACKOFF[$nextBackoffIndex])) {
                $this->source->importMediaTranscript(self::IMPORT_CHECK_BACKOFF[$nextBackoffIndex]);
            }
        }
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::IMPORT, JobCategoryTag::TRANSCRIBE], $this->source);
    }

    public function uniqueId(): string
    {
        return $this->generateUniqueKey($this->source->id);
    }
}
