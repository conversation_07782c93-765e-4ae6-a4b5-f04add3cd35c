<?php

namespace App\Jobs;

use App\Jobs\Traits\HasTags;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Lara<PERSON>\SerializableClosure\SerializableClosure;

class RunOperationOnModelsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use HasTags;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public string $modelClass,
        public array $ids,
        public SerializableClosure $fnOperation
    ) {
        $this->onQueue(env('QUEUE_HIGH', 'default'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::debug(class_basename(static::class));
        $this->modelClass::whereIn('id', $this->ids)->each(function ($model) {
            dispatch(new RunOperationOnSingleModelJob($model, $this->fnOperation));
        });
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags();
    }
}
