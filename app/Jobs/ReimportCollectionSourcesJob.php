<?php

namespace App\Jobs;

use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasHighTimeout;
use App\Jobs\Traits\HasTags;
use App\Jobs\Traits\IsUnique;
use App\Models\Collection;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ReimportCollectionSourcesJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use HasHighTimeout, HasTags, IsUnique;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Collection $collection
    ) {
        $this->onQueue(env('QUEUE_DEFAULT', 'default'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::debug(class_basename(static::class));
        $this->collection->sources()->orderBy('last_imported_at', 'asc')->get()->each(function ($source) {
            dispatch(new ImportSourceDataJob($source, true));
        });
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::IMPORT], $this->collection);
    }

    public function uniqueId(): string
    {
        return $this->generateUniqueKey($this->collection->id);
    }
}
