<?php

namespace App\Jobs;

use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasTags;
use App\Jobs\Traits\IsUnique;
use App\Models\Model;
use Google\Cloud\Core\Exception\GoogleException;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Spatie\Translatable\HasTranslations;

class TranslateModelFieldJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use HasTags, IsUnique;

    /**
     * Create a new job instance.
     */
    public function __construct(public Model $model, public string $field, public array $languages = [])
    {
        $this->onQueue(env('QUEUE_DEFAULT', 'default'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        Log::debug(class_basename(static::class));
        if (uses_trait($this->model, HasTranslations::class)) {

            $defaultLanguage = env('TRANSLATION_DEFAULT_LANGUAGE');
            $translations = $this->model->getTranslations($this->field);
            $allLanguages = ! empty($this->languages) ? $this->languages : config('agent.languages');

            if (isset($translations[$defaultLanguage])) {

                foreach ($allLanguages as $code => $name) {

                    if ($code != $defaultLanguage) {
                        try {
                            $translation = translate_field($code, $translations[$defaultLanguage], $translations[$defaultLanguage]);
                            $this->model->setTranslation($this->field, $code, $translation);
                        } catch (GoogleException $e) {
                            $className = class_basename($this->model::class);
                            Log::error("TRANSLATION FAILED: {$className}[{$this->model->id}] {$this->field}: {$translations[$defaultLanguage]} => {$code}");
                            Log::error($e->getMessage());
                        }
                    }

                }

                $this->model->save();

            }

        } else {
            $this->fail();
        }

    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::TRANSLATE], $this->model);
    }

    public function uniqueId(): string
    {
        return $this->generateUniqueKey([basename($this->model::class), $this->model->id, $this->field]);
    }
}
