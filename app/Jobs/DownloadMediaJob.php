<?php

namespace App\Jobs;

use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasHighTimeout;
use App\Jobs\Traits\HasTags;
use App\Models\Model;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class DownloadMediaJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use HasHighTimeout, HasTags;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Model $model,
        public string $field,
        public string $directory,
        public string $url,
        public ?string $extension = null
    ) {
        $this->onQueue(env('QUEUE_DEFAULT', 'default'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::debug(class_basename(static::class));
        $fields = [];
        $fields[$this->field] = save_file_from_url($this->url, $this->directory, $this->extension);
        $this->model->update($fields);
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::IMPORT, JobCategoryTag::MEDIA], $this->model);
    }
}
