<?php

namespace App\Jobs\Middleware;

use Closure;
use Illuminate\Contracts\Redis\LimiterTimeoutException;
use Illuminate\Support\Facades\Redis;

class ThrottleScraperJobs
{
    /**
     * Process the queued job.
     *
     * @param  \Closure(object): void  $next
     *
     * @throws LimiterTimeoutException
     */
    public function handle(object $job, Closure $next): void
    {
        Redis::throttle('scrape')
            ->block(0)
            ->allow(1)
            ->every(15)
            ->then(function () use ($job, $next) {
                $next($job);
            }, function () use ($job) {
                $job->release(15);
            })
        ;
    }
}
