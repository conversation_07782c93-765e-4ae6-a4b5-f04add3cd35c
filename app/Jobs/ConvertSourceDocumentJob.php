<?php

namespace App\Jobs;

use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasTags;
use App\Jobs\Traits\IsUnique;
use App\Models\Source;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ConvertSourceDocumentJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use HasTags, IsUnique;

    /**
     * Create a new job instance.
     */
    public function __construct(public Source $source, public bool $newUrl = false)
    {
        $this->onQueue(env('QUEUE_DEFAULT', 'default'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::debug(class_basename(static::class));
        $this->source->refresh();
        Log::debug($this->source->media_path);
        if ($this->source->canConvertDocument()) {
            $this->source->convertFromDocument($this->newUrl);
        }
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::CONVERT, JobCategoryTag::MEDIA], $this->source);
    }

    public function uniqueId(): string
    {
        return $this->generateUniqueKey($this->source->id);
    }
}
