<?php

namespace App\Http\Controllers;

use App\Filament\Api\ApiService;
use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Laravel\Sanctum\PersonalAccessToken;

class ApiAuth extends Controller
{
    public const TOKEN_NAME = 'Personal API Token';

    public const TOKEN_EXPIRY = 180; // in minutes

    public function getToken(Request $request): array|string
    {
        if ($request->user()) {
            $token = $request->user()->getApiToken(static::TOKEN_NAME);
            if ($token && ! empty($request->user()->api_token)) {
                static::refreshToken($token);
                $tokenStr = Crypt::decryptString($request->user()->api_token);
            } else {
                $tokenStr = $request->user()->createToken(static::TOKEN_NAME, ['*'], now()->addMinutes(static::TOKEN_EXPIRY))->plainTextToken;
                $request->user()->updateQuietly(['api_token' => Crypt::encryptString($tokenStr)]);
            }

            return ['token' => $tokenStr];
        } else {
            return '';
        }
    }

    public function getUser(Request $request): array
    {
        $user = User::with('currentTeam')->find($request->user()->id)->toArray();

        return ApiService::transformResource($user);
        /*return ApiService::transformResource(
            User::join('memberships', 'users.id', '=', 'model_id')
            ->with([
                'currentTeam',
                'currentTeamMembership',
                'currentTeamMembership.role',
                'currentTeamMembership.role.permissions'
            ])
            ->where('users.id', $request->user()->id)
            ->where('memberships.model_id', $request->user()->id)
            ->first()->toArray()
        );*/
    }

    public function logout(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect(Filament::getPanel('id')->getLoginUrl().'?logout');
    }

    public static function refreshToken(PersonalAccessToken $token): bool
    {
        return $token->update(['expires_at' => now()->addMinutes(static::TOKEN_EXPIRY)]);
    }
}
