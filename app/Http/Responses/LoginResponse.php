<?php

namespace App\Http\Responses;

use Filament\Http\Responses\Auth\LoginResponse as BaseLoginResponse;
use Illuminate\Http\RedirectResponse;
use Livewire\Features\SupportRedirects\Redirector;

class LoginResponse extends BaseLoginResponse
{
    public function toResponse($request): RedirectResponse|Redirector
    {
        $components = $request->get('components');
        $json = json_decode($components[0]['snapshot'], true);
        $redirectUrl = $json['data']['data'][0]['redirect_url'] ?? null;
        if (! empty($redirectUrl)) {
            return redirect(urldecode($redirectUrl));
        }

        return parent::toResponse($request);
    }
}
