<?php

namespace App\Http\Middleware;

use App\Http\Controllers\ApiAuth;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ExtendAccessToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->user()) {
            if ($token = $request->user()->currentAccessToken()) {
                if ($token->name == ApiAuth::TOKEN_NAME) {
                    ApiAuth::refreshToken($token);
                }
            }
        }

        return $next($request);
    }
}
