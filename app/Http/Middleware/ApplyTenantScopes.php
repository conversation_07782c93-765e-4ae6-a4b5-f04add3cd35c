<?php

namespace App\Http\Middleware;

use App\Services\Gatekeeper;
use Closure;
use Filament\Resources\Resource;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ApplyTenantScopes
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Gatekeeper::hasAdminAccess()) {
            Resource::scopeToTenant(false);
        }

        return $next($request);
    }
}
