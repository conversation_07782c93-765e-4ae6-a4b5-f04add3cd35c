<?php

namespace App\Http\Middleware;

use Filament\Facades\Filament;
use Filament\Http\Middleware\Authenticate as BaseAuthenticate;
use Filament\Models\Contracts\FilamentUser;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class Authenticate extends BaseAuthenticate
{
    /**
     * @throws AuthenticationException
     */
    protected function authenticate($request, array $guards): void
    {

        $panelId = Arr::first(explode('.', request()->getHttpHost()));
        if ($panelId == env('APP_SUBDOMAIN_API')) {
            $panelId = env('APP_API_VERSION');
        } else if ($panelId == env('APP_SUBDOMAIN_APP')) {
            $panelId = 'app';
        }
        $currentPanel = Filament::getPanel($panelId);

        $guard = $currentPanel->auth();
        if (!$guard->check()) {
//            Log::debug('unauthenticated');
            $this->unauthenticated($request, $guards);
            return;
        }

        $this->auth->shouldUse($currentPanel->getAuthGuard());

        $user = $guard->user();
        abort_if(
            $user instanceof FilamentUser ?
                (! $user->canAccessPanel($currentPanel)) :
                (config('app.env') !== 'local'),
            403,
        );

    }

    /**
     * Get the path the user should be redirected to when they are not authenticated.
     */
    protected function redirectTo($request): ?string
    {
        return Filament::getPanel('id')->getLoginUrl().'?redirect';
    }

    /**
     * @return void
     *
     * @throws AuthenticationException
     */
    protected function unauthenticated($request, array $guards)
    {
        if (in_array('sanctum', $guards)) {
            abort(401, json_encode(['message' => 'Unauthenticated']));
        } else {
            throw new AuthenticationException(
                'Unauthenticated.', $guards, $this->redirectTo($request)
            );
        }
    }
}
