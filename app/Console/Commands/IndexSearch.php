<?php

namespace App\Console\Commands;

use App\Jobs\ManageListingSearchDocumentJob;
use App\Models\Collection;
use App\Models\Contributor;
use App\Models\Organization;
use App\Models\Source;
use Illuminate\Console\Command;

class IndexSearch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'index:search {model} {ids}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Index search document(s).';

    const MODELS = [
        'collection' => Collection::class,
        'contributor' => Contributor::class,
        'organization' => Organization::class,
        'source' => Source::class,
    ];

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $this->newLine();
        $modelClass = static::MODELS[$this->argument('model')];

        $this->info(strtoupper(class_basename($modelClass)));
        $this->info(str_repeat('-', 16));

        $modelClass::approved()
            ->whereIn('id', explode(',', $this->argument('ids')))
            ->orderBy('name', 'asc')->each(function ($model) {
                if ($model->shouldIndexSearch()) {
                    $this->comment("{$model->name} [{$model->id}]");
                    dispatch(new ManageListingSearchDocumentJob($model));
                }
            })
        ;
        $this->newLine();

    }
}
