<?php

namespace App\Console\Commands;

use App\Console\Commands\Traits\HasDateOffset;
use App\Models\AgentPrompt;
use App\Models\FrontendListingImpression;
use App\Models\FrontendListingReferral;
use App\Models\FrontendListingView;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SyncAgentPrompts extends Command
{
    use HasDateOffset;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:agent-prompts {days=3} {offsetDays?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize agent prompts.';

    const UPSERT_CHUNK_SIZE = 1000;

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $this->setDateRange();

        $this->newLine();
        $this->comment("Getting usage from {$this->startDate->toDateTimeString()} to {$this->endDate->toDateTimeString()} ...");

        $data = DB::connection(env('FRONTEND_DB_CONNECTION'))
            ->table('prompts AS p')
            ->join('configs AS c', 'p.config_id', '=', 'c.id')
            ->join('sessions AS s', 'p.session_id', '=', 's.id')
            ->join('agents AS a', 'p.agent_id', '=', 'a.id')
            ->select(
                'p.id',
                'p.conversation_id',
                DB::raw('s.id AS session_id'),
                's.user_id',
                'p.language',
                DB::raw('s.ip_country AS country'),
                DB::raw('s.ip_region AS region'),
                DB::raw('s.ip_city AS city'),
                DB::raw('s.ip_timezone AS timezone'),
//                DB::raw("DATE_TRUNC('microsecond', p.prompted_at) AS prompted_at"),
                'p.prompted_at',
                'p.response_started_at',
                'p.response_completed_at',
                DB::raw('LENGTH(p.prompt) AS prompt_chars'),
                DB::raw('LENGTH(p.response) AS response_chars'),
                'p.prompt_tokens',
                'p.response_tokens',
                'p.chat_tokens',
                'p.agent_id',
                DB::raw('c.model_id AS agent_model_id'),
                'p.agent_token_id',
                DB::raw('(CASE WHEN p.translated_prompt IS NOT NULL THEN 1 ELSE 0 END) AS is_translated'),
                'p.client',
                'a.is_nonbillable',
                DB::raw("DATE_TRUNC('second', now()::timestamp) AS synced_at")
            )
            ->where('prompted_at', '>=', $this->startDate->toDateTimeString())
            ->where('prompted_at', '<=', $this->endDate->toDateTimeString())
            ->get()
            ->toArray();
        $items = collect($data)->map(fn ($x) => (array) $x)->toArray();

        $numUpserted = 0;
        foreach (array_chunk($items, static::UPSERT_CHUNK_SIZE) as $itemsChunk) {
            $numUpserted += AgentPrompt::upsert(
                $itemsChunk,
                ['id'],
                ['synced_at']
            );
        }

        $this->newLine();
        $this->info("{$numUpserted} prompts upserted.");

        $this->newLine();

    }

}
