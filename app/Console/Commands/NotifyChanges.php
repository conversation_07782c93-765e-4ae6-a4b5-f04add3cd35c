<?php

namespace App\Console\Commands;

use Altek\Accountant\Models\Ledger;
use App\Enums\NotificationFrequency;
use App\Enums\NotificationType;
use App\Models\User;
use App\Services\Auditor;
use Illuminate\Console\Command;

class NotifyChanges extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notify:changes {frequency=daily} {--u|users=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Notify users that a change happened.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $frequency = $this->argument('frequency');

        $qry = User::notifyFrequency(NotificationType::CHANGES, $frequency);
        if ($this->hasOption('users') && ! empty($this->option('users'))) {
            $qry->whereIn('users.id', explode(',', $this->option('users')));
        }
        $frequencyUserIds = $qry->get()->pluck('user_id')->toArray();

        $ledgers = Ledger::where('created_at', '>=', NotificationFrequency::getThreshold($frequency))->get();
        $userLedgerMap = [];
        foreach ($ledgers as $ledger) {
            $userIds = array_intersect(
                $frequencyUserIds,
                Auditor::getUsersToNotify($ledger)->pluck('id')->toArray()
            );
            foreach ($userIds as $userId) {
                $userLedgerMap[$userId][] = $ledger;
            }
        }

        $frequencyTitle = ucwords($frequency);
        foreach ($userLedgerMap as $userId => $ledgers) {
            $user = User::find($userId);
            $user->sendMultipleModelsChangedEmail($ledgers, " {$frequencyTitle} Changes Digest");
        }

    }
}
