<?php

namespace App\Console\Commands;

use App\Console\Commands\Traits\HasDateOffset;
use App\Models\FrontendListingImpression;
use App\Models\FrontendListingReferral;
use App\Models\FrontendListingView;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SyncFrontendStats extends Command
{
    use HasDateOffset;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:frontend-stats {days=3} {offsetDays?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize frontend stats.';

    const UPSERT_CHUNK_SIZE = 1000;

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $this->setDateRange();

        $this->newLine();
        $this->comment("Getting stats from {$this->startDate->toDateTimeString()} to {$this->endDate->toDateTimeString()} ...");

        $this->upsertItemStats('item_referrals', FrontendListingReferral::class, 'referred_at');
        $this->upsertItemStats('item_views', FrontendListingView::class, 'viewed_at');
        $this->upsertItemStats('item_impressions', FrontendListingImpression::class, 'viewed_at');
        $this->newLine();

    }

    protected function upsertItemStats(string $fromTable, string $modelName, string $timestampField): void
    {

        $data = DB::connection(env('FRONTEND_DB_CONNECTION'))
            ->table($fromTable)
            ->select(
                'id',
                'frontend',
                'item_model',
                'item_id',
                'prompt_id',
                'user_id',
                DB::raw("DATE_TRUNC('second', {$timestampField}) AS {$timestampField}"),
                DB::raw("DATE_TRUNC('second', now()::timestamp) AS synced_at")
            )
            ->where($timestampField, '>=', $this->startDate->toDateTimeString())
            ->where($timestampField, '<=', $this->endDate->toDateTimeString())
            ->get()
            ->toArray();
        $items = collect($data)->map(fn ($x) => (array) $x)->toArray();

        $numUpserted = 0;
        foreach (array_chunk($items, static::UPSERT_CHUNK_SIZE) as $itemsChunk) {
            $numUpserted += $modelName::upsert(
                $itemsChunk,
                ['id'],
                ['synced_at']
            );
        }

        $this->newLine();
        $name = str_replace('_', ' ', $fromTable);
        $this->info("{$numUpserted} {$name} upserted.");

    }
}
