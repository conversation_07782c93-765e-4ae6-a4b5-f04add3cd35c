<?php

namespace App\Console\Commands;

use App\Jobs\ManageListingSearchDocumentJob;
use App\Models\Model;
use App\Services\Labeller;
use App\Services\VectaraClient;
use Closure;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Staudenmeir\LaravelCte\Query\Builder as QueryBuilder;

abstract class FixModels extends Command
{

    protected $signature = null;

    protected $description = null;

    protected const int MAX_DELAY = 900;

    protected string $model = Model::class;

    protected array $problems = [];

    protected VectaraClient $searchClient;
    protected VectaraClient $ragClient;

    public function handle(): void
    {

        $this->newLine();

        // Set clients
        $this->searchClient = get_search_client();
        $this->ragClient = get_rag_client();

        // Add model-specific problems
        $this->addProblems();

        if (isset($this->problems[$this->argument('problem')])) {

            $problem = $this->problems[$this->argument('problem')];

            $qry = $this->filterModels($this->model::query(), $problem['filter']);

            // Limit
            $limit = intval($this->option('limit'));
            $throttle = intval($this->option('throttle'));
            if ($throttle) {
                $maxLimit = floor(static::MAX_DELAY / $throttle);
                if ($limit > $maxLimit) {
                    $limit = $maxLimit;
                    if ($this->option('verbose')) {
                        $this->comment("Max delay of " . static::MAX_DELAY . " seconds with a throttle of {$throttle} seconds; limiting to {$maxLimit} items.");
                        $this->newLine();
                    }
                }
            }
            $qry->limit($limit);

            // Order
            $qry->orderBy($problem['sort'], 'asc');

            if ($this->option('verbose')) {
                $this->comment($qry->toRawSql());
                $this->newLine();
            }

            $totalCount = number_format($qry->count());
            $models = $qry->get();
            $modelCount = number_format(count($models));
            $modelLabel = Labeller::model($this->model);
            $action = strtoupper($problem['action']);
            $this->info("{$action} {$modelCount} / {$totalCount} {$modelLabel}s.");
            $this->newLine();

            if ($qry->count() > 0) {

                $actionPast = $action . (str_ends_with($action, 'E') ? '' : 'E') . 'D';
                $i = 0;
                foreach ($qry->get() as $model)
                {

                    echo "{$model->name} ... ";

                    if ($this->option('dry')) {
                        $this->comment("WOULD {$action}");
                    } else {

                        if ($this->option('sync')) {
                            $success = $problem['sync']($model);
                        } else {
                            $success = $problem['async']($model)->delay(now()->addSeconds($throttle * $i));
                        }

                        if ($success) {
                            if ($this->option('sync')) {
                                $this->info($actionPast);
                            } else {
                                $this->info("{$action} QUEUED");
                            }
                        } else {
                            $this->error('ERROR');
                        }

                    }

                    ++$i;

                }

                $this->newLine();

            }

        } else {
            $this->newLine();
            $this->error('Invalid problem.');
            $this->newLine();
            exit;
        }

    }

    protected function addProblems(): void
    {
        $this->problems['search'] = [
            'action' => 'search index',
            'filter' => fn ($qry) => $qry->where('social_active', true)
                ->whereNull('social_indexed_at')
            ,
            'sort' => 'social_indexed_at',
            'sync' => fn ($model) => $model->reindexSearchDocument($this->searchClient),
            'async' => fn ($model) => dispatch(new ManageListingSearchDocumentJob($model)),
        ];
    }

    protected function filterModels(EloquentBuilder|QueryBuilder $qry, Closure $filterClosure): EloquentBuilder|QueryBuilder
    {
        if (!empty($this->option('ids'))) {
            $qry->whereIn('id', explode(',', $this->option('ids')));
        }
        return $filterClosure($qry);
    }

}
