<?php

namespace App\Console\Commands;

use App\Models\Category;
use App\Models\Source;
use Illuminate\Console\Command;

class CategorizeSources extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'categorize:sources';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Attempt to auto-categorize one or more sources.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $this->newLine();

        $sources = Source::has('categories', '=', 0)->orderBy('name')->get();
        $categories = Category::getAssignable(); // Call once and pass to avoid duplicate queries
        foreach ($sources as $source) {

            $author = ! $source->contributions->isEmpty() ? $source->contributions[0]->contributor->short_name : null;
            $this->line($source->name);
            $this->line('----------------');
            $this->comment("Author: {$author}");
            $this->comment("Description:\n{$source->description}");
            $source->autoCategorize($categories);
            $this->info('DISPATCHED');
            $this->newLine();

        }

    }
}
