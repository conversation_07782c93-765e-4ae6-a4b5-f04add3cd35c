<?php

namespace App\Console\Commands;

use App\Enums\CollectionType;
use App\Jobs\ImportNewWebsiteSourcesJob;

class ImportWebsiteSources extends ImportSourcesCommand
{
    const COLLECTION_TYPE = CollectionType::WEBSITE;

    const JOB_TYPE = 'website crawler';

    const JOB_CLASS = ImportNewWebsiteSourcesJob::class;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:websites {collectionId?} {--full}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatch jobs to import new items from active websites.';
}
