<?php

namespace App\Console\Commands;

use App\Enums\CollectionType;
use App\Jobs\ImportNewChannelSourcesJob;

class ImportChannelSources extends ImportSourcesCommand
{
    const COLLECTION_TYPE = CollectionType::CHANNEL;

    const JOB_TYPE = 'Channel / Playlist';

    const JOB_CLASS = ImportNewChannelSourcesJob::class;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:channels {collectionId?} {--full}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatch jobs to import new sources from active channels or playlists.';
}
