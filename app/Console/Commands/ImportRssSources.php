<?php

namespace App\Console\Commands;

use App\Enums\CollectionType;
use App\Jobs\ImportNewRssSourcesJob;

class ImportRssSources extends ImportSourcesCommand
{
    const COLLECTION_TYPE = CollectionType::RSS;

    const JOB_TYPE = 'RSS feed';

    const JOB_CLASS = ImportNewRssSourcesJob::class;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:rss_feeds {collectionId?} {--full}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatch jobs to import new items from active feeds.';
}
