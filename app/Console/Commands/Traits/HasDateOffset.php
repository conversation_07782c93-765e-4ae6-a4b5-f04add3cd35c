<?php

namespace App\Console\Commands\Traits;

use Carbon\Carbon;

trait HasDateOffset
{
    protected Carbon $startDate;

    protected Carbon $endDate;

    protected function setDateRange(): void
    {

        $numDays = intval($this->argument('days'));
        $numOffsetDays = intval($this->argument('offsetDays'));

        $this->endDate = Carbon::now();
        if ($numOffsetDays) {
            $this->endDate = Carbon::now()->subDays($numOffsetDays);
        }
        $this->startDate = Carbon::now()->subDays($numDays + $numOffsetDays);

    }
}
