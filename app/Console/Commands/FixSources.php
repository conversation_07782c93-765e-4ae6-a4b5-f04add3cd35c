<?php

namespace App\Console\Commands;

use App\Enums\SourceType;
use App\Jobs\ManageListingSearchDocumentJob;
use App\Jobs\ManageSourceRagDocumentJob;
use App\Models\Source;
use Closure;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Staudenmeir\LaravelCte\Query\Builder as QueryBuilder;

class FixSources extends FixModels
{

    protected $signature = 'fix:sources {problem} {--i|ids=} {--c|collections} {--l|limit=1000} {--d|dry} {--s|sync} {--t|throttle=0}';

    protected $description = 'Fix source issues.';

    protected string $model = Source::class;

    protected const array IMPORTED_TYPES = [
        SourceType::URL->value,
        SourceType::YOUTUBE->value,
        SourceType::EPISODE->value,
    ];

    protected function filterModels(EloquentBuilder|QueryBuilder $qry, Closure $filterClosure): EloquentBuilder|QueryBuilder
    {
        if (!empty($this->option('collections'))) {
            $qry->whereIn('importer_collection_id', explode(',', $this->option('collections')));
        }
        return parent::filterModels($qry, $filterClosure);
    }

    protected function addProblems(): void
    {

        $placeholderPrefix = Source::getPrefixedName('');

        $this->problems = [
            'image' => [
                'action' => 'import',
                'filter' => fn ($qry) => $qry->where('type', SourceType::URL->value)
                    ->whereNotNull('image_path')
                    ->whereRaw("RIGHT(TRIM(image_path), 1) = '.'")
                ,
                'sort' => 'last_imported_at',
                'sync' => fn ($model) => $model->import(true),
                'async' => fn ($model) => dispatch(function () use ($model) { $model->import(true); }),
            ],
            'placeholder' => [
                'action' => 'import',
                'filter' => fn ($qry) => $qry->whereIn('type', static::IMPORTED_TYPES)
                    ->where('name', 'LIKE', "{$placeholderPrefix}%")
                    ->whereNull('last_imported_at')
                ,
                'sort' => 'last_imported_at',
                'sync' => fn ($model) => $model->import(true),
                'async' => fn ($model) => dispatch(function () use ($model) { $model->import(true); }),
            ],
            'rag' => [
                'action' => 'rag index',
                'filter' => fn ($qry) => $qry->where('agent_active', true)
                    ->whereNotNull('text_path')
                    ->whereNull('agent_indexed_at')
                    ->whereNotNull('last_imported_at')
                ,
                'sort' => 'agent_indexed_at',
                'sync' => fn ($model) => $model->reindexRagDocument($this->ragClient),
                'async' => fn ($model) => dispatch(new ManageSourceRagDocumentJob($model)),
            ],
            'archived' => [
                'action' => 'unindex',
                'filter' => fn ($qry) => $qry->onlyTrashed()
                    ->where('agent_active', true)
                    ->whereNotNull('agent_indexed_at')
                ,
                'sort' => 'agent_indexed_at',
                'sync' => fn ($model) => $model->deleteRagDocument($this->ragClient, true) && $model->deleteSearchDocument($this->searchClient, true),
                'async' => fn ($model) => dispatch(new ManageSourceRagDocumentJob($model, true)) && dispatch(new ManageListingSearchDocumentJob($model, true)),
            ],
            'duplicate' => [
                'action' => 'delete',
                'filter' => fn ($qry) => $qry->where('type', SourceType::URL->value)
                    ->whereNotNull('url')
                    ->whereRaw("
                        id NOT IN (
                            SELECT
                                *
                            FROM (
                                SELECT
                                    MIN(n.id)
                                FROM
                                    sources n
                                WHERE
                                    n.url IS NOT NULL
                                GROUP BY
                                    n.url,
                                    n.team_id
                            ) x
                        )
                    ")
                ,
                'sort' => 'last_imported_at',
                'sync' => fn ($model) => $model->forceDelete(),
                'async' => fn ($model) => dispatch(function() use ($model) { $model->forceDelete(); }),
            ],
            'transcript' => [
                'action' => 'transcribe',
                'filter' => fn ($qry) => $qry->whereIn('type', [SourceType::YOUTUBE->value, SourceType::EPISODE->value, SourceType::MEDIA->value])
                    ->where('agent_active', true)
                    ->whereNull('text_path')
                    ->whereNotNull('importer_collection_id')
                    ->whereExists(function (EloquentBuilder|QueryBuilder $qry) {
                        $qry->select(DB::raw(1))
                            ->from('collections')
                            ->whereColumn('id', 'sources.importer_collection_id')
                            ->where('auto_transcribe_sources', true)
                        ;
                    })
                ,
                'sort' => 'agent_indexed_at',
                'sync' => fn ($model) => $model->transcribe(),
                'async' => fn ($model) => dispatch(function () use ($model) { $model->transcribe(); }),
            ],
            'text' => [
                'action' => 'import',
                'filter' => fn ($qry) => $qry->whereNotIn('type', [SourceType::URL->value])
                    ->where('agent_active', true)
                    ->whereNull('text_path')
                    ->whereNotNull('importer_collection_id')
                    ->whereExists(function (EloquentBuilder|QueryBuilder $qry) {
                        $qry->select(DB::raw(1))
                            ->from('collections')
                            ->whereColumn('id', 'sources.importer_collection_id')
                            ->where('auto_transcribe_sources', true)
                        ;
                    }),
                'sort' => 'agent_indexed_at',
                'sync' => fn ($model) => $model->import(true),
                'async' => fn ($model) => dispatch(function () use ($model) { $model->import(true); }),
            ],
            'terms' => [
                'action' => 'accept',
                'filter' => fn ($qry) => $qry->where('agent_active', true)
                    ->where('agent_terms_accepted', false)
                ,
                'sort' => 'agent_indexed_at',
                'sync' => fn ($model) => $model->update(['agent_terms_accepted'=>true]),
                'async' => fn ($model) => dispatch(function() use ($model) { $model->update(['agent_terms_accepted'=>true]); }),
            ]
        ];

        parent::addProblems();

    }
}
