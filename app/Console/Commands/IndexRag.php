<?php

namespace App\Console\Commands;

use App\Jobs\ManageSourceRagDocumentJob;
use App\Models\Source;
use Illuminate\Console\Command;

class IndexRag extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'index:rag {ids}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Index RAG source document(s).';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $this->newLine();
        Source::whereIn('id', explode(',', $this->argument('ids')))
            ->orderBy('agent_indexed_at', 'asc')->each(function ($source) {
                if ($source->shouldIndexRag()) {
                    $this->comment("{$source->name} [{$source->id}]");
                    dispatch(new ManageSourceRagDocumentJob($source));
                }
            })
        ;
        $this->newLine();

    }
}
