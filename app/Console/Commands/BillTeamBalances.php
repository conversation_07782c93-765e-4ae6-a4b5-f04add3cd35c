<?php

namespace App\Console\Commands;

use App\Console\Commands\Traits\HasDateOffset;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Symfony\Component\Console\Helper\TableCell;
use Symfony\Component\Console\Helper\TableCellStyle;
use Symfony\Component\Console\Helper\TableSeparator;

class BillTeamBalances extends Command
{
    use HasDateOffset;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bill:team-balances {--team=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Show team balances.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $this->newLine();

        $balancesQry = DB::table('spike_credit_transactions AS c')
            ->join('teams AS t', 'c.billable_id', '=', 't.id')
            ->select(
                't.id AS team_id',
                't.name AS team_name',
                DB::raw("IF(t.is_nonbillable = 1, 'N', 'Y') AS is_billable"),
                DB::raw("SUM(c.credits) AS balance")
            )
        ;

        if ($teamId = intval($this->option('team'))) {
            $balancesQry->where('t.id', $teamId);
        }

        $balances = $balancesQry->groupBy('t.id')
            ->orderBy('is_billable', 'desc')
            ->orderBy('balance')
            ->orderBy('t.name')
            ->get()
        ;

        $rows = [];
        foreach ($balances as $balance) {
            $rows[] = [
                new TableCell(
                    $balance->team_id,
                    ['style' => new TableCellStyle(['align'=>'right', 'fg'=>'gray'])]
                ),
                $balance->team_name,
                new TableCell(
                    $balance->is_billable,
                    [
                        'style' => new TableCellStyle([
                            'align' => 'center',
                            'fg' => ($balance->is_billable == 'N') ? 'red' : 'default'
                        ])
                    ]
                ),
                new TableCell(
                    number_format($balance->balance),
                    [
                        'style' => new TableCellStyle([
                            'align' => 'right',
                            'fg' => (
                                ($balance->is_billable == 'N') ? 'yellow' :
                                    ($balance->balance < 0 ? 'red' : 'green')
                                )
                            ]
                        )
                    ]
                ),
            ];
        }

        $this->table(
            ['ID', 'Name', 'Billable', 'Credit Balance'],
            $rows
        );

        $this->newLine();

    }

}
