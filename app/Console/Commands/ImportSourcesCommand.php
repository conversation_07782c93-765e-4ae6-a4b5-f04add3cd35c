<?php

namespace App\Console\Commands;

use App\Models\Collection;
use Illuminate\Console\Command;

abstract class ImportSourcesCommand extends Command
{
    const COLLECTION_TYPE = null;

    const JOB_TYPE = null;

    const JOB_CLASS = null;

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $collectionType = static::COLLECTION_TYPE;
        $jobType = static::JOB_TYPE;
        $jobClass = static::JOB_CLASS;

        $this->newLine();
        $collections = Collection::where('type', $collectionType)->shouldAutoImportSources();
        if (! empty($this->argument('collectionId'))) {
            $collections->where('id', $this->argument('collectionId'));
        }
        $numCollections = $collections->count();
        $this->line("Dispatching {$numCollections} {$jobType} jobs ...");
        $this->newLine();
        foreach ($collections->get() as $collection) {
            dispatch(new $jobClass($collection, $this->option('full')));
            $this->comment("Dispatched: {$collection->name}");
        }
        $this->newLine();
        $this->info("{$numCollections} {$jobType} jobs were dispatched.");
        $this->newLine();

    }
}
