<?php

namespace App\Console\Commands;

use App\Enums\CollectionType;
use App\Jobs\ImportNewPodcastSourcesJob;

class ImportPodcastSources extends ImportSourcesCommand
{
    const COLLECTION_TYPE = CollectionType::PODCAST;

    const JOB_TYPE = 'podcast';

    const JOB_CLASS = ImportNewPodcastSourcesJob::class;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:podcast_episodes {collectionId?} {--full}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatch jobs to import new items from active podcasts.';
}
