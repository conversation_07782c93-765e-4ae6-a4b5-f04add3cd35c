<?php

namespace App\Console\Commands;

use App\Models\Collection;
use App\Models\Contributor;
use App\Models\FrontendListingView;
use App\Models\Organization;
use App\Models\Source;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SyncSocialViews extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:social-views';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize Social listing views.';

    const MODELS = [
        'collection' => Collection::class,
        'contributor' => Contributor::class,
        'organization' => Organization::class,
        'source' => Source::class,
    ];

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $this->newLine();
        foreach (static::MODELS as $itemModel => $modelClass) {

            $this->info(strtoupper($itemModel));
            $this->info(str_repeat('-', 16));
            $views = FrontendListingView::select('item_model', 'item_id', DB::raw('COUNT(item_id) AS num_views'))
                ->where('frontend', 'social')
                ->where('item_model', $itemModel)
                ->groupBy('item_model', 'item_id')
                ->having('num_views', '>', 0)
                ->orderBy('num_views', 'desc')
                ->get();

            foreach ($views as $view) {
                $model = $modelClass::withTrashed()->find($view->item_id);
                if ($model) {
                    $this->comment("{$model->name}: {$view->num_views}");
                    $model->updateQuietly(['num_views' => $view->num_views]);
                }
            }
            $this->newLine();

        }

    }
}
