<?php

namespace App\Console\Commands;

use App\Enums\SourceMediaStatus;
use App\Jobs\ImportSourceTranscriptJob;
use App\Models\Source;
use Illuminate\Console\Command;

class ImportSourceTranscripts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:transcripts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatch jobs to import transcripts from processing transcript tasks.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->newLine();
        $sources = Source::where('media_status', SourceMediaStatus::PROCESSING);
        $numSources = $sources->count();
        $this->line("Dispatching {$numSources} checks for processing transcripts ...");
        $this->newLine();
        foreach ($sources->get() as $source) {
            dispatch(new ImportSourceTranscriptJob($source));
            $this->comment("Dispatched: {$source->name}");
        }
        $this->newLine();
        $this->info("{$numSources} processing transcript check jobs were dispatched.");
        $this->newLine();
    }
}
