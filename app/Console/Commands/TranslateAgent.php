<?php

namespace App\Console\Commands;

use Exception;
use Google\Cloud\Core\Exception\GoogleException;
use Google\Cloud\Core\Exception\ServiceException;
use Google\Cloud\Translate\V2\TranslateClient;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class TranslateAgent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'translate:agent {languages?} {--retranslate=} {--fresh}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create Agent language translations.';

    protected ?TranslateClient $client = null;

    protected ?array $languages = null;

    protected ?array $retranslations = null;

    protected ?string $outDir = null;

    const OUT_PATH = 'app/agent_translations';

    const LANGUAGES_FILE = '/languages.json';

    const TRANSLATIONS_FILE = '/{code}/translation.json';

    const DO_NOT_TRANSLATE = [
        'apologist.ai',
    ];

    const EXCLUDE_LANGUAGES = [
        'iw',
        'jv',
        'zh-CN',
    ];

    const PATCHED_LANGUAGES = [
        'pt' => 'Português',
        'pt-BR' => 'Português (Brasil)',
    ];

    /**
     * Execute the console command.
     *
     * @throws GoogleException
     */
    public function handle(): void
    {

        $this->outDir = storage_path(static::OUT_PATH);
        $this->mkDirIfNotExists($this->outDir);

        $this->client = new TranslateClient(['key' => env('TRANSLATION_API_KEY')]);

        $languagesFile = $this->outDir.static::LANGUAGES_FILE;
        $this->setLanguages($languagesFile);
        if (empty($this->argument('languages'))) {
            $this->updateLanguagesFile($languagesFile);
        }
        $this->setRetranslations();
        $this->generateTranslationFiles();

    }

    /**
     * @throws ServiceException
     */
    protected function setLanguages(string $languagesFile): void
    {

        $this->newLine();
        echo 'Building language options ... ';

        $languagesArg = array_filter(explode(',', $this->argument('languages')));
        if (! empty($languagesArg)) {
            $codes = $languagesArg;
        } else {
            $codes = $this->client->languages();
        }

        if (File::exists($languagesFile) && ! $this->option('fresh')) {
            $this->languages = json_decode(file_get_contents($languagesFile), true);
        } else {
            $this->languages = [];
        }

        // Loop through supported languages and add any that are missing
        foreach ($codes as $code) {

            // Attempt to add the language as a language option if we don't already have it
            if (! isset($this->languages[$code]) || is_null($this->languages[$code])) {

                try {
                    $locals = $this->client->localizedLanguages(['target' => $code]);
                    foreach ($locals as $local) {
                        if ($local['code'] == $code) {
                            $this->languages[$code] = $local['name'];
                            break;
                        }
                    }
                } catch (Exception $e) {
                    //                    $this->languages[$code] = null;
                }

            }

        }

        $this->languages = Arr::except($this->languages, static::EXCLUDE_LANGUAGES);
        foreach (static::PATCHED_LANGUAGES as $code => $name) {
            if (empty($languagesArg) || in_array($code, $languagesArg)) {
                $this->languages[$code] = $name;
            }
        }
        ksort($this->languages);

        if (! empty($this->argument('languages'))) {
            $this->languages = Arr::only($this->languages, explode(',', $this->argument('languages')));
        }

        $this->info('OK');

        if ($this->option('verbose')) {
            $this->newLine();
//            $this->comment(print_r(json_encode($this->languages, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE), true));
        }

    }

    protected function updateLanguagesFile($languagesFile): void
    {

        $this->newLine();
        echo 'Saving languages file ... ';
        $this->saveJsonFile($languagesFile, $this->languages);
        $this->info('OK');
        $this->newLine();

    }

    protected function generateTranslationFiles(): void
    {

        // Load up the English translations
        $englishTranslationsFile = $this->getTranslationsFile('en');
        if (! File::exists($englishTranslationsFile)) {
            $this->error("{$englishTranslationsFile} does not exist!");
            exit;
        }
        $englishTranslations = json_decode(file_get_contents($englishTranslationsFile), true);

        // Generate the translation files
        foreach (array_keys($this->languages) as $code) {
            $this->generateTranslationFile($code, $englishTranslations);
        }

    }

    protected function setRetranslations(): void
    {
        if (! empty($this->option('retranslate'))) {
            $this->retranslations = explode(',', $this->option('retranslate'));
        }
    }

    protected function generateTranslationFile(string $code, array $englishTranslations): void
    {

        $this->comment("{$this->languages[$code]} ({$code})");
        $this->comment(str_repeat('-', 16));

        $translationsFile = $this->getTranslationsFile($code);
        if (File::exists($translationsFile)) {
            $existingTranslations = json_decode(file_get_contents($translationsFile), true);
        } else {
            $existingTranslations = [];
        }

        // Remove any retranslations from existing translations
        if (! empty($this->retranslations)) {
            foreach ($this->retranslations as $retranslate) {
                data_forget($existingTranslations, $retranslate);
            }
        }

        if ($code == 'en') {
            $translations = $englishTranslations;
        } else {
            echo 'Translating ... ';
            if ($this->option('verbose')) {
                $this->newLine();
            }
            try {
                $translations = $this->translate($englishTranslations, 'en', $code, $existingTranslations);
                $this->info('OK');
            } catch (Exception $e) {
                $this->error('ERROR, USING ENGLISH');
                $translations = $englishTranslations;
            }
        }

        echo 'Saving translation file ... ';
        try {
            $this->mkDirIfNotExists($translationsFile);
            $this->saveJsonFile($translationsFile, $translations);
            $this->info('OK');
        } catch (Exception $e) {
            $this->error('ERROR');
        }

        $this->newLine();

    }

    protected function getTranslationsFile(string $code): string
    {
        return replace_tokens($this->outDir.static::TRANSLATIONS_FILE, ['code' => $code]);
    }

    /**
     * @throws ServiceException
     */
    protected function translate(string|array $text, string $from, string $to, string|array|null $existingTranslation): string|array
    {
        if (is_array($text)) {
            $result = [];
            foreach ($text as $key => $val) {
                $result[$key] = $this->translate(
                    $val,
                    $from,
                    $to,
                    $existingTranslation[$key] ?? (is_array($val) ? [] : null)
                );
            }

            return $result;
        } else {
            if (! $this->option('fresh') && $existingTranslation) {
                $translation = $existingTranslation;
            } elseif (in_array(Str::lower($text), static::DO_NOT_TRANSLATE)) {
                $translation = $text;
            } else {
                $translationRes = $this->client->translate($text, ['source' => $from, 'target' => $to]);
                $translation = $translationRes['text'];
            }
            if ($this->option('verbose')) {
                $this->line("<fg=gray>{$text} => {$translation}</>");
            }

            return $translation;
        }
    }

    protected function mkDirIfNotExists(string $path): bool
    {
        $path = dirname($path);

        return File::isDirectory($path) or File::makeDirectory($path, 0777, true, true);
    }

    protected function saveJsonFile(string $file, array $json): false|int
    {
        return file_put_contents($file, json_encode($json, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
}
