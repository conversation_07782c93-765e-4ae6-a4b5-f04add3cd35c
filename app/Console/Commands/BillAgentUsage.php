<?php

namespace App\Console\Commands;

use App\Console\Commands\Traits\HasDateOffset;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Symfony\Component\Console\Helper\TableCell;
use Symfony\Component\Console\Helper\TableCellStyle;
use Symfony\Component\Console\Helper\TableSeparator;

class BillAgentUsage extends Command
{
    use HasDateOffset;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bill:agent-usage {days=3} {offsetDays?} {--team=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Backfill agent usage.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $this->setDateRange();

        $this->newLine();

        // Get credits to backfill grouped by team / date
        $rttCredits = env_int('REALTIME_TRANSLATION_CREDITS');
        $usageQry = DB::table('agent_prompts AS p')
            ->join('agents AS a', 'p.agent_id', '=', 'a.id')
            ->join('agent_models AS m', 'a.model_id', '=', 'm.id')
            ->join('teams AS t', 'a.team_id', '=', 't.id')
            ->select(
                't.name AS team_name',
                'a.team_id',
                DB::raw('MAX(a.auto_translate) AS auto_translate'),
                DB::raw("DATE_FORMAT(p.prompted_at, '%Y-%m-%d') AS date"),
                DB::raw("SUM(m.num_credits + IF(p.is_translated, {$rttCredits}, 0)) AS num_credits"),
                DB::raw("COUNT(p.id) AS num_prompts")
            )
            ->where('p.prompted_at', '>=', $this->startDate->toDateTimeString())
            ->where('p.prompted_at', '<=', $this->endDate->toDateTimeString())
            ->where('p.is_nonbillable', false)
//            ->where('p.is_billed', false)
        ;

        if ($teamId = intval($this->option('team'))) {
            $usageQry->where('a.team_id', $teamId);
        }

        $usages = $usageQry->groupBy(
            'a.team_id',
            DB::raw("DATE_FORMAT(p.prompted_at, '%Y-%m-%d')")
        )
            ->orderBy('t.name')
            ->orderBy('date')
            ->get()
        ;
        echo "Backfilling {$usages->count()} usage days grouped by team ... ";
        if ($this->option('verbose')) {
            $this->newLine(2);
        }

        // Backfill usage
        $rows = [];
        $currentTeamId = null;
        foreach ($usages as $usage)
        {

            if ($usage->team_id != $currentTeamId) {
                if (!is_null($currentTeamId)) {
                    $rows[] = new TableSeparator();
                }
                $rows[] = [new TableCell(
                    "{$usage->team_name} (ID: {$usage->team_id}) " . ($usage->auto_translate ? '[RTT]' : ''),
                    ['colspan' => 4]
                )];
                $rows[] = new TableSeparator();
                $currentTeamId = $usage->team_id;
            }

            $row = [
                new TableCell(
                    $usage->date,
                    ['style' => new TableCellStyle(['fg'=>'gray'])]
                ),
                new TableCell(
                    number_format($usage->num_prompts),
                    ['style' => new TableCellStyle(['align'=>'right', 'fg'=>'yellow'])]
                ),
                new TableCell(
                    number_format($usage->num_credits),
                    ['style' => new TableCellStyle(['align'=>'right', 'fg'=>'yellow'])]
                ),
            ];

            // Find if there's already a daily usage record for this team
            $existing = DB::table('spike_credit_transactions')
                ->where('billable_type', 'App\\Models\\Team')
                ->where('billable_id', $usage->team_id)
                ->where('type', 'usage')
                ->where('credits', '<', 0)
                ->where(DB::raw("DATE_FORMAT(created_at, '%Y-%m-%d')"), $usage->date)
                ->first();

            $credits = ($usage->num_credits * -1);
            if ($existing) {

                try {
                    DB::table('spike_credit_transactions')
                        ->where('id', $existing->id)
                        ->update(['credits' => $credits])
                    ;
                    $row[] = "<fg=green>UPDATED</>";
                } catch (\Exception $e) {
                    $row[] = "<fg=red>UPDATE FAILED: {$e->getMessage()}</>";
                }

            } else {

                try {
                    $timestamp = Carbon::parse($usage->date)->addHours(12)->toDateTimeString();
                    DB::table('spike_credit_transactions')->insert([
                        'billable_type' => 'App\\Models\\Team',
                        'billable_id' => $usage->team_id,
                        'type' => 'usage',
                        'credit_type' => 'credits',
                        'credits' => $credits,
                        'created_at' => $timestamp,
                        'updated_at' => $timestamp,
                    ]);
                    $row[] = "<fg=green>INSERTED</>";
                } catch (\Exception $e) {
                    $row[] = "<fg=red>INSERT FAILED: {$e->getMessage()}</>";
                }

            }

            $rows[] = $row;

        }

        if ($this->option('verbose')) {
            $this->table(
                ['Date', 'Prompts', 'Credits', 'Status'],
                $rows
            );
        } else {
            $this->info('OK');
        }

        $this->newLine();

    }

}
