<?php

namespace App\Console\Commands;

use App\Console\Commands\Traits\HasDateOffset;
use Illuminate\Console\Command;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class SyncAgentImpressions extends Command
{
    use HasDateOffset;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:agent-impressions {days=3} {offsetDays?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize Agent prompt sources to impressions.';

    const UPSERT_CHUNK_SIZE = 1000;

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $this->setDateRange();

        $this->newLine();
        $this->comment("Syncing source impressions from Agent prompts from {$this->startDate->toDateTimeString()} to {$this->endDate->toDateTimeString()} ...");

        $impressionsQry = DB::connection(env('FRONTEND_DB_CONNECTION'))
            ->table('prompts AS p')
            ->join('prompt_sources AS ps', 'p.id', '=', 'ps.prompt_id')
            ->join('sessions AS s', 'p.session_id', '=', 's.id')
            ->select(
                DB::raw("'agent' AS frontend"),
                DB::raw("'source' AS item_model"),
                'ps.source_id AS item_id',
                'ps.prompt_id',
                DB::raw("(CASE WHEN p.client = 'api' THEN null ELSE CAST(p.user_id AS INTEGER) END) AS user_id"),
                'p.prompted_at AS viewed_at'
            )
            ->where('p.prompted_at', '>=', $this->startDate->toDateTimeString())
            ->where('p.prompted_at', '<=', $this->endDate->toDateTimeString())
            ->whereNotExists(function (Builder $qry) {
                $qry->select(DB::raw(1))
                    ->from('item_impressions')
                    ->where('frontend', 'agent')
                    ->where('item_model', 'source')
                    ->whereColumn('item_id', 'ps.source_id')
                    ->whereColumn('prompt_id', 'ps.prompt_id')
                    ->whereRaw("user_id IS NOT DISTINCT FROM (CASE WHEN p.client = 'api' THEN null ELSE CAST(s.user_id AS INTEGER) END)")
                    ->whereColumn('viewed_at', 'p.prompted_at');
            })
            ->groupBy([
                'ps.source_id',
                'ps.prompt_id',
                'p.user_id',
                'p.client',
                'p.prompted_at',
            ]);
        $data = $impressionsQry->get()->toArray();
        $hits = collect($data)->map(fn ($x) => (array) $x)->toArray();

        $numUpdates = 0;
        foreach (array_chunk($hits, static::UPSERT_CHUNK_SIZE) as $hitsChunk) {
            $numUpdates += DB::connection(env('FRONTEND_DB_CONNECTION'))
                ->table('item_impressions')
                ->upsert(
                    $hitsChunk,
                    ['frontend', 'item_model', 'item_id', 'user_id', 'prompt_id', 'viewed_at']
                );
        }

        $this->newLine();
        $this->info("{$numUpdates} search hits upserted as impressions.");
        $this->newLine();

    }
}
