<?php
namespace App\Console\Commands;

use App\Models\Agent;
use App\Models\AgentModel;
use App\Models\AgentModelProvider;
use App\Models\AgentQuestion;
use App\Models\AgentToken;
use App\Models\Category;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class SyncAgents extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:agents {models?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize Agents and Categories from Ignite to Agent.';

    const UPSERT_CHUNK_SIZE = 1000;

    protected const MODELS = [
        AgentModelProvider::class => [],
        AgentModel::class => [],
//        Agent::class => ['corpus_api_key'],
//        AgentToken::class   =>  ['token'],
//        AgentQuestion::class => [],
        Category::class => [],
    ];

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $this->newLine();

        if (!empty($this->argument('models'))) {
            $passedModels = explode(',', $this->argument('models'));
            $modelsToSync = [];
            foreach (static::MODELS as $model => $excludeFields)
            {
                if (in_array(class_basename($model), $passedModels)) {
                    $modelsToSync[$model] = $excludeFields;
                }
            }
        } else {
            $modelsToSync = static::MODELS;
        }

        Schema::connection(env('FRONTEND_DB_CONNECTION'))->disableForeignKeyConstraints();
        foreach ($modelsToSync as $model => $excludeFields) {

            $this->info(class_basename($model));
            $this->info(str_repeat('-', 16));

            $fields = (new $model)->getFillable();
            $table = get_model_table($model);
            $records = DB::table($table)
                ->select(Arr::except(array_merge(['id', DB::raw('NOW() AS synced_at')], $fields), $excludeFields))
                ->whereNull('deleted_at')
                ->get()
                ->map(fn ($item) => (array) $item)
                ->toArray()
            ;

            $numUpdates = 0;
            foreach (array_chunk($records, static::UPSERT_CHUNK_SIZE) as $recordsChunk) {
                $numUpdates += DB::connection(env('FRONTEND_DB_CONNECTION'))->table($table)->upsert(
                    $recordsChunk,
                    ['id'],
                    Arr::except(array_merge(['synced_at'], $fields), $excludeFields)
                );
            }
            $this->comment("{$numUpdates} upserted");

            $deleteIds = DB::table($table)->whereNotNull('deleted_at')->pluck('id');
            $numDeletes = DB::connection(env('FRONTEND_DB_CONNECTION'))->table($table)->whereIn('id', $deleteIds)->delete();
            $this->comment("{$numDeletes} deleted");

            $this->newLine();

        }
        Schema::connection(env('FRONTEND_DB_CONNECTION'))->enableForeignKeyConstraints();

        // Go back and update agent categories, collections, and sources
        echo 'Syncing Agent aggregates ... ';
        $agents = Agent::all();
        foreach ($agents as $agent) {
            DB::connection(env('FRONTEND_DB_CONNECTION'))
                ->table('agents')
                ->where('id', $agent->id)
                ->update([
                    'categories' => $agent->categories()->pluck('categories.id'),
                    'collections' => $agent->collections()->pluck('collections.id'),
                    'contributors' => $agent->contributors()->pluck('contributors.id'),
                    'sources' => $agent->sources()->pluck('sources.id'),
                ]);
        }
        $this->info('OK');
        $this->newLine();

    }
}
