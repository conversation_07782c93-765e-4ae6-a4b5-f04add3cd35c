<?php

namespace App\Console\Commands;

use App\Jobs\ManageSourceRagDocumentJob;
use App\Models\Source;
use App\Models\Team;
use App\Services\VectaraClient;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class SyncRagIndex extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:rag-index {--t|teams=} {--p|pagesize=100} {--l|limit=100} {--s|sync} {--d|dry}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync RAG documents.';

    protected const DEFAULT_PAGE_SIZE = 100;

    protected VectaraClient $client;

    protected int $pageSize;

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $this->newLine();

        $this->client = get_rag_client();
        $this->pageSize = !empty($this->option('pagesize')) ?  intval($this->option('pagesize')) : self::DEFAULT_PAGE_SIZE;

        // Get all the RAG documents in the corpus
        $documents = $this->getRagDocuments();
        $numDocuments = count($documents);
        $numDocumentsFormatted = number_format($numDocuments);
        $this->info("Found {$numDocumentsFormatted} RAG documents");
//        if ($this->option('verbose')) {
//            $docStrs = array_map(fn ($document) => $document['id'], $documents);
//            $this->comment(implode("\n", $docStrs));
//        }
        $documentIds = array_map(fn ($document) => intval($document['id']), $documents);
        $this->newLine();

        // Get all Sources that *should* be in the corpus
        $sourceIds = Source::where('agent_active', true)->whereNotNull('text_path')->pluck('id')->toArray();

        $documentsToAdd = array_values(array_diff($sourceIds, $documentIds));
        if (!empty($this->option('limit'))) {
            $documentsToAdd = array_slice($documentsToAdd, 0, intval($this->option('limit')));
        }
        $numDocumentsToAdd = count($documentsToAdd);
        if ($numDocumentsToAdd > 0) {

            $numDocumentsToAddFormatted = number_format($numDocumentsToAdd);
            $this->info("Index {$numDocumentsToAddFormatted} missing RAG documents ...");
            foreach ($documentsToAdd as $sourceId)
            {

                $source = Source::find($sourceId);
                echo "{$source['id']}: {$source->name} ... ";

                // If for some reason the text file doesn't actually exist, self-heal text_path to null and bail on this Source
                if (!Storage::disk('public')->exists($source->text_path)) {
                    $source->updateQuietly(['text_path' => null]);
                    $this->comment("INVALID TEXT PATH, REMOVED");
                    continue;
                }

                $action = ($this->option('sync') ? '' : 'QUEUE ') . 'INDEX';
                if (!$this->option('dry')) {
                    $res = $this->option('sync') ? $source->indexRagDocument() : dispatch(new ManageSourceRagDocumentJob($source));
                    if ($res) {
                        $this->info($action);
                    } else {
                        $this->error($action);
                    }
                } else {
                    $this->comment("WOULD {$action}");
                }

            }

        } else {
            $this->info('No RAG documents to index');
        }
        $this->newLine();

        $documentsToRemove = array_values(array_diff($documentIds, $sourceIds));
        if (!empty($this->option('limit'))) {
            $documentsToRemove = array_slice($documentsToRemove, 0, intval($this->option('limit')));
        }
        $numDocumentsToRemove = count($documentsToRemove);
        if ($numDocumentsToRemove > 0) {

            $numDocumentsToRemoveFormatted = number_format($numDocumentsToRemove);
            $this->info("Deleting {$numDocumentsToRemoveFormatted} orphaned RAG documents ...");
            foreach ($documentsToRemove as $sourceId)
            {

                $str = $sourceId;
                if ($source = Source::withTrashed()->find($sourceId)) {
                    $str .= ": {$source->name}";
                }
                $str .= ' ... ';
                echo $str;

                $action = ($this->option('sync') ? '' : 'QUEUE ') . 'DELETION';
                if (!$this->option('dry')) {
                    $res = $this->option('sync') ?
                        $this->client->deleteDocument(strval($sourceId)) :
                        dispatch(function () use ($sourceId) {
                            $client = get_rag_client();
                            $client->deleteDocument(strval($sourceId));
                        })
                    ;
                    if ($res) {
                        $this->info($action);
                    } else {
                        $this->error($action);
                    }
                } else {
                    $this->comment("WOULD {$action}");
                }

            }

        } else {
            $this->info('No RAG documents to delete');
        }
        $this->newLine();

    }

    protected function getRagDocuments(): array
    {

        $qry = Team::query();
        if (!empty($this->option('teams'))) {
            $qry->whereIn('id', explode(',', $this->option('teams')));
        }
        $teamIds = $qry->pluck('id')->toArray();

        $documents = [];
        foreach ($teamIds as $teamId)
        {

            $hasMore = true;
            $pageKey = null;
            while ($hasMore)
            {

                $res = $this->client->listDocuments($this->pageSize, "doc.team = {$teamId}", $pageKey);
                $json = json_decode($res->getBody()->getContents(), true);

                if ($res->getStatusCode() !== 200) {
                    $this->error('ERROR LISTING DOCUMENTS');
                    echo json_encode($json, JSON_PRETTY_PRINT);
                    exit;
                } else {
                    $documents = array_merge($documents, $json['documents']);
                    $pageKey = (
                        isset($json['metadata']) &&
                        isset($json['metadata']['page_key']) &&
                        !empty($json['metadata']['page_key'])
                    ) ?
                        $json['metadata']['page_key'] :
                        null
                    ;
                    $hasMore = !is_null($pageKey);
                }

            }

        }

        return $documents;

    }

}
