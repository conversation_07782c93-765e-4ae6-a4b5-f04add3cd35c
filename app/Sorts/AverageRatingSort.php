<?php

namespace App\Sorts;

use Illuminate\Support\Facades\DB;
use Spatie\QueryBuilder\Sorts\Sort;

class AverageRatingSort implements Sort
{
    public function __invoke($query, bool $descending, string $property)
    {
        $query->leftJoin('ratings', get_model_table($property).'.id', '=', 'ratings.rateable_id')
            ->where('ratings.rateable_type', $property)
            ->orderBy(DB::raw('AVG(ratings.rating)'), $descending ? 'desc' : 'asc');
    }
}
