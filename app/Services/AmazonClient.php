<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;

class AmazonClient
{
    const BASE_URL = 'https://api.rainforestapi.com/';

    const LANGUAGE = 'English';

    const DOMAIN = 'amazon.com';

    const TIMEOUT = 300;

    const DEFAULT_SORT = 'featured'; // featured, most_recent, average_review

    const MAX_PAGES = 4;

    const MOCK = false;

    protected Client $client;

    protected string $apiKey;

    protected string $accountId;

    protected const AMAZON_URLS = [
        'www.amazon.'
    ];

    public function __construct(string $apiKey, string $accountId)
    {
        $this->apiKey = $apiKey;
        $this->accountId = $accountId;
        $this->client = new Client([
            'base_uri' => self::BASE_URL,
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
//                'timeout' => self::TIMEOUT.'S',
            ],
            'timeout' => self::TIMEOUT,
        ]);
    }

    /**
     * @throws GuzzleException
     */
    public function getBooksByKeyword(
        string $keywords,
        string $sort = 'most_recent',
        bool $strictMatch = false,
        ?string $categoryId = null,
        ?string $topicToMatch = null,
        int $retries = 0
    ): array {

        if (static::MOCK) {

            $file = storage_path('app/search.json');
//            Log::debug($file);
            $json = json_decode(file_get_contents($file), true);
//            Log::debug(json_encode($json, JSON_PRETTY_PRINT));

        } else {

            $params = [
                'search_term' => strtolower($keywords),
                'sort_by' => $sort,
                'exclude_sponsored' => 'true',
                'max_page' => static::MAX_PAGES,
            ];
            if ($categoryId) {
                $params['category_id'] = $categoryId;
            }
            $json = $this->getResults('search', $params);

        }

        $books = [];

        if (empty($json['search_results'])) {

            if ($retries > 3) {
                Log::debug('NO RESULTS AFTER 3 TRIES');

                return [];
            }

            return $this->getBooksByKeyword($keywords, $sort, $strictMatch, $categoryId, $topicToMatch, $strictMatch, ++$retries);

        } else {

            foreach ($json['search_results'] as $item) {

                $hasAuthor = true;
                if ($strictMatch && isset($item['authors']) && ! empty($item['authors'])) {
                    $hasAuthor = false;
                    foreach ($item['authors'] as $author) {
                        if (str_contains(strtolower($author['name']), strtolower($keywords))) {
                            $hasAuthor = true;
                            break;
                        }
                    }
                }

                if ($hasAuthor) {
                    $books[] = $this->mapBookAttrs($item);
                }

            }

            return $books;

        }

    }

    /**
     * @throws GuzzleException
     */
    public function getBook(string $id, ?string $topicToMatch = null): ?array
    {

        Log::debug("NO CACHE HIT: {$id}");

        if (static::MOCK) {

            $file = storage_path("app/book{$id}.json");
//            Log::debug($file);
            if (file_exists($file)) {
                $json = json_decode(file_get_contents($file), true);
            } else {
                return null;
            }

        } else {

            $json = $this->getResults('product', ['asin' => $id]);

        }

        $product = $json['product'];

        //        $hasTopic = (!isset($product['categories_flat']) || str_contains(strtolower($product['categories_flat']), strtolower($topicToMatch)));
        //        $hasLanguage = str_contains(strtolower($product['language']), strtolower(static::LANGUAGE));

        //        if ($hasTopic && $hasLanguage) {
        return $this->mapBookAttrs($product);
        //        } else {
        //            return null;
        //        }

    }

    protected function mapBookAttrs(array $product): array
    {

        $colonPos = strpos($product['title'], ':');
        if ($colonPos !== false) {
            $product['subtitle'] = trim(substr($product['title'], $colonPos + 1));
            $product['title'] = substr($product['title'], 0, $colonPos);
        }

        return [
            'external_id' => $product['asin'] ?? null,
            'name' => $product['title'] ?? null,
            'subtitle' => $product['subtitle'] ?? null,
            'referral_url' => $product['link'] ?? null,
            'authors' => $product['authors'] ?? [],
            'rating' => $product['rating'] ?? null,
            'num_ratings' => $product['ratings_total'] ?? null,
            'description' => $product['book_description'] ?? null,
            'image_url' => $product['image'] ?? $product['main_image']['link'] ?? null,
            'published_on' => (isset($product['publication_date']) ? Formatter::date($product['publication_date']) : null),
            'publisher' => $product['publisher'] ?? null,
        ];

    }

    /**
     * @throws GuzzleException
     */
    protected function getResults(string $type, array $params): array
    {

        $params = array_merge(
            $params,
            [
                'type' => $type,
                'api_key' => $this->apiKey,
                'associate_id' => $this->accountId,
                'amazon_domain' => static::DOMAIN,
                'output' => 'json',
            ]
        );

        $queryString = http_build_query($params);
        $url = sprintf('%s?%s', 'request', $queryString);
        //        Log::debug($url);
        $response = $this->client->get($url);
        $json = json_decode($response->getBody(), true);

        //        Log::debug(json_encode($json, JSON_PRETTY_PRINT));
        return $json;

    }

    public static function getAsinFromUrl(string $url): ?string
    {
        preg_match('~/dp/([a-zA-Z0-9]+)/ref=tag~', $url, $matches);
        return $matches[1];
    }

    public static function isBookUrl(string $url): bool
    {
        return (
            url_matches($url, static::AMAZON_URLS) &&
            str_contains($url, '/dp/')
        );
    }

}
