<?php

namespace App\Services;

use App\Models\Source;
use App\Models\Traits\BelongsToTeam;
use App\Models\User;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Gatekeeper
{
    public static function isSuperadmin($userId = null): bool
    {
        $user = static::getUser($userId);
        if (! is_null($user)) {
            return $user->hasRole(Gatekeeper::getSuperadminRoleId(), 'web');
        }

        return false;
    }

    public static function isAdmin($userId = null): bool
    {
        $user = static::getUser($userId);
        if (! is_null($user)) {
            return $user->hasRole(Gatekeeper::getSuperadminRoleId(), 'web') || $user->hasRole(Gatekeeper::getAdminRoleId(), 'web');
        }

        return false;
    }

    public static function hasAdminAccess($userId = null): bool
    {
        $user = static::getUser($userId);
        if (! is_null($user)) {
            return $user->roles()->where('has_admin_access', true)->exists();
        }
        return false;
    }

    public static function canModerateContent($userId = null): bool
    {
        $user = static::getUser($userId);
        if (! is_null($user)) {
            return static::userCan('moderate', Source::class);
        }
        return false;
    }

    /**
     * @return Authenticatable|bool
     */
    protected static function getUser($userId = null): ?Authenticatable
    {
        if (is_null($userId) && auth()->check()) {
            return auth()->user();
        } elseif (! is_null($userId)) {
            return User::find($userId)->first();
        }

        return null;
    }

    public static function getSuperadminRoleId(): int
    {
        return env_int('SUPERADMIN_ROLE_ID');
    }

    public static function getAdminRoleId(): int
    {
        return env_int('ADMIN_ROLE_ID');
    }

    public static function getModeratorRoleId(): int
    {
        return env_int('MODERATOR_ROLE_ID');
    }

    public static function getManagerRoleId(): int
    {
        return env_int('MANAGER_ROLE_ID');
    }

    public static function getMemberRoleId(): int
    {
        return env_int('MEMBER_ROLE_ID');
    }

    public static function userCan(string $ability, string $modelClass, ?Model $record = null): bool
    {
        return auth()->user()->can($ability.'_'.Str::snake(class_basename($modelClass)), $record);
    }

    public static function userCanAdminister(string $modelClass): bool
    {
        return static::userCan('administer', $modelClass);
    }

    public static function userOwns(?Model $model): bool
    {
        return ! is_null($model) && ($model->user_id == auth()->user()->id);
    }

    public static function isTeamOwnable($model): bool
    {
        return uses_trait($model, BelongsToTeam::class);
    }
}
