<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Str;

class Formatter
{
    public static function shortLang(string $language): string
    {
        return Str::limit(strtolower($language), 2, '');
    }

    public static function yesNo(bool|int|null $bool): string
    {
        return $bool ? 'Yes' : 'No';
    }

    public static function datetime(?string $datetime): ?string
    {
        if (is_null($datetime)) {
            return null;
        } elseif (is_numeric($datetime)) {
            return Carbon::createFromTimestamp($datetime)->toDateTimeString();
        }

        return Carbon::parse($datetime)->toDateTimeString();
    }

    public static function date(?string $date): ?string
    {
        try {
            if (is_null($date)) {
                return null;
            } elseif (is_numeric($date)) {
                return Carbon::createFromTimestamp($date)->format('Y-m-d');
            }
            return Carbon::parse($date)->format('Y-m-d');
        } catch (\Exception $e) {
            return null;
        }
    }

    public static function year(?string $year): ?string
    {
        if (is_null($year)) {
            return null;
        }

        return intval(Carbon::parse($year)->format('Y'));
    }
}
