<?php

namespace App\Services;

use Altek\Accountant\Models\Ledger;
use App\Enums\NotificationFrequency;
use App\Enums\NotificationType;
use App\Models\Category;
use App\Models\Collection;
use App\Models\Contributor;
use App\Models\Membership;
use App\Models\Organization;
use App\Models\Permission;
use App\Models\Role;
use App\Models\Source;
use App\Models\SourceContribution;
use App\Models\Team;
use App\Models\TeamInvitation;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class Auditor
{
    public const ACTIONS = [
        'created' => 'Created',
        'updated' => 'Updated',
        'deleted' => 'Archived',
        'restored' => 'Restored',
        'forceDeleted' => 'Deleted',
        'toggled' => 'Toggled',
        'synced' => 'Updated',
        'existingPivotUpdated' => 'Updated',
        'attached' => 'Updated',
        'detached' => 'Updated',
    ];

    public const ACTION_OVERRIDES = [
        Contributor::class => [
            'created' => 'Added',
        ],
        Membership::class => [
            'created' => 'Added',
            'deleted' => 'Suspended',
            'restored' => 'Reactivated',
        ],
        Source::class => [
            'created' => 'Added',
        ],
        TeamInvitation::class => [
            'deleted' => 'Canceled',
        ],
        Team::class => [
            'deleted' => 'Suspended',
            'restored' => 'Reactivated',
        ],
    ];

    public const MODEL_OVERRIDES = [
        Membership::class => 'Team Member',
    ];

    public const LABEL_SUFFIX_OVERRIDES = [
        'user_id' => 'owner',
        'model_id' => 'user',
        'url' => 'URL',
        'path' => '',
    ];

    /**
     * @var array|string[]
     */
    protected static array $superadminMonitoredModels = [
        Category::class,
        Collection::class,
        Contributor::class,
        Membership::class,
        Organization::class,
        Permission::class,
        Role::class,
        Source::class,
        SourceContribution::class,
        Team::class,
        TeamInvitation::class,
        User::class,
    ];

    /**
     * @var array|string[]
     */
    protected static array $teamOwnerMonitoredModels = [
        Membership::class,
        TeamInvitation::class,
    ];

    /**
     * @var array|string[]
     */
    protected static array $adminMonitoredModels = [
        Collection::class,
        Contributor::class,
        Organization::class,
        Source::class,
        SourceContribution::class,
    ];

    /**
     * @var array|string[]
     */
    protected static array $ownerMonitoredModels = [
        Collection::class,
        Contributor::class,
        Source::class,
        SourceContribution::class,
        Team::class,
    ];

    /**
     * @var array|string[]
     */
    protected static array $substituteParentModels = [
        SourceContribution::class => 'source',
    ];

    public static function getModifiedFields(Ledger $ledger): array
    {

        $pivot = $ledger->getPivotData();
        $model = $ledger->recordable;

        $modified = [];
        if ($model) {

            $fields = Arr::except(
                $ledger->getData(),
                array_merge(
                    ['id', 'created_at', 'updated_at', 'deleted_at'],
                    $model->getHidden(),
                    $model->getIgnored()
                )
            );

            $previousQry = $model->ledgers()->where('event', $ledger->event);
            if (! empty($pivot)) {
                $previousQry->where('pivot->relation', $pivot['relation']);
            }
            $previousQry = $previousQry->where('id', '!=', $ledger->id)
                ->where('created_at', '<=', $ledger->created_at)
                ->orderBy('created_at', 'desc');
            $previousLedger = $previousQry->first();

            $casts = $model->getCasts();
            foreach ($fields as $field => $newValue) {

                $oldValue = ($previousLedger && isset($previousLedger->properties[$field])) ? $previousLedger->properties[$field] : null;
                $possibleRelation = static::removeIdSuffix($field);
                if (($possibleRelation != $field) && $model->$possibleRelation) {
                    $relatedModel = $model->$possibleRelation->getModel();
                    if ($oldValue) {
                        $oldRelated = $relatedModel::withTrashed()->find($oldValue);
                        $oldValue = $oldRelated->name ?? null;
                    }
                    $newRelated = $relatedModel::withTrashed()->find($newValue);
                    $newValue = $newRelated->name ?? null;
                }

                if ($newValue != $oldValue) {

                    if (is_enum($oldValue)) {
                        $oldValue = Labeller::mutate($oldValue->value);
                    }
                    if (is_enum($newValue)) {
                        $newValue = Labeller::mutate($newValue->value);
                    }

                    if (isset($casts[$field]) && ($casts[$field] == 'boolean')) {
                        $oldValue = Formatter::yesNo($oldValue);
                        $newValue = Formatter::yesNo($newValue);
                    } elseif (static::fieldMatches($field, 'path')) {
                        $oldValue = static::getFileHtml($oldValue);
                        $newValue = static::getFileHtml($newValue);
                    } elseif (static::fieldMatches($field, 'url')) {
                        $oldValue = get_link_html($oldValue, $oldValue);
                        $newValue = get_link_html($newValue, $newValue);
                    } elseif (static::fieldMatches($field, 'at')) {
                        $oldValue = Formatter::datetime($oldValue);
                        $newValue = Formatter::datetime($newValue);
                    } elseif (static::fieldMatches($field, 'on')) {
                        $oldValue = Formatter::date($oldValue);
                        $newValue = Formatter::date($newValue);
                    }

                    $modified[$field] = [
                        'label' => static::getEntryLabel($field),
                        'from' => $oldValue,
                        'to' => $newValue,
                        'relation' => false,
                    ];

                }

            }

            if (! empty($pivot)) {

                $relation = $pivot['relation'];
                if (empty($previousLedger->pivot['properties'])) {
                    $previousLedger = $previousQry
                        ->where('pivot->relation', $relation)
                        ->where('pivot->properties', '!=', '[]')
                        ->first();
                }

                $oldRelated = $previousLedger ? static::getRelated($model, $relation, $previousLedger->pivot['properties']) : [];
                $newRelated = static::getRelated($model, $relation, $pivot['properties']);

                if ($newRelated != $oldRelated) {
                    $modified[$relation] = [
                        'label' => static::getEntryLabel($relation),
                        'from' => $oldRelated,
                        'to' => $newRelated,
                        'relation' => true,
                    ];
                }

            }

        }

        return $modified;

    }

    public static function getEntryLabel(string $field): string
    {
        foreach (static::LABEL_SUFFIX_OVERRIDES as $find => $replace) {
            if (str_ends_with($field, $find)) {
                $field = str_replace($find, $replace, $field);
                break;
            }
        }

        return Labeller::mutate(Str::title(static::removeIdSuffix($field)));
    }

    public static function removeIdSuffix(string $str): string
    {
        if (str_ends_with(strtolower(str_replace('_', ' ', $str)), ' id')) {
            $str = substr($str, 0, -3);
        }

        return $str;
    }

    public static function getRelated(Model $model, string $relation, array $records): array
    {

        $relatedModel = $model->$relation()->getQuery()->getModel();
        $relatedClass = get_class($relatedModel);
        $relatedForeignKey = $relatedModel->getForeignKey();

        $related = [];
        foreach ($records as $record) {
            $related[] = $record[$relatedForeignKey];
        }

        return $relatedClass::withTrashed()->whereIn('id', $related)->orderBy('name')->pluck('name')->toArray();

    }

    public static function getActionLabel(string $action, ?Model $model = null): string
    {
        $label = static::ACTIONS[$action];
        if ($model) {
            $modelClass = get_class($model);
            if (
                isset(static::ACTION_OVERRIDES[$modelClass]) &&
                isset(static::ACTION_OVERRIDES[$modelClass][$action])
            ) {
                $label = static::ACTION_OVERRIDES[$modelClass][$action];
            }
        }

        return $label;
    }

    public static function getModelLabel(Model $model): string
    {
        if (isset(static::MODEL_OVERRIDES[$model::class])) {
            return static::MODEL_OVERRIDES[$model::class];
        }

        return Labeller::model($model);
    }

    public static function getFileHtml($path): ?HtmlString
    {
        $url = get_path_url($path);
        if ($url) {
            if (is_image_mime(get_remote_mime_type($url))) {
                return static::getImageHtml($url);
            } else {
                return get_link_html($url);
            }
        }

        return null;
    }

    public static function fieldMatches(string $field, string $match): bool
    {
        return ($field == $match) || str_ends_with($field, "_{$match}");
    }

    protected static function getImageHtml(string $url, int $height = 64): HtmlString
    {
        return new HtmlString("<a href='{$url}' target='_blank'><img src='{$url}' style='height: {$height}px'></a>");
    }

    public static function getUsersToNotify(Ledger $ledger): EloquentCollection
    {

        $model = $ledger->recordable;

        // Only proceed if there is a model
        $usersToNotify = new EloquentCollection;
        if ($model) {

            $fields = Auditor::getModifiedFields($ledger);
            $modelClass = get_class($model);

            // Only send the notification if there are any fields that matter
            if (! empty($fields)) {

                if (isset(static::$substituteParentModels[$modelClass])) {
                    $model = $model->{static::$substituteParentModels[$modelClass]};
                }

                // Add superadmins
                if (in_array($modelClass, static::$superadminMonitoredModels)) {
                    $usersToNotify->concat(
                        User::superadmins()->notifyNow(NotificationType::CHANGES)->get()
                    );
                }

                if (Gatekeeper::isTeamOwnable($modelClass)) {

                    // Add team owner
                    if (
                        in_array($modelClass, static::$teamOwnerMonitoredModels) &&
                        ($model->team?->owner->getNotificationFrequency(NotificationType::CHANGES->value) == NotificationFrequency::IMMEDIATELY->value)
                    ) {
                        $usersToNotify->push($model->team->owner);
                    }

                    // Add team admins
                    if (
                        in_array($modelClass, static::$adminMonitoredModels) &&
                        $model->team
                    ) {
                        $usersToNotify->concat(
                            $model->team?->admins()->notifyNow(NotificationType::CHANGES)->get()
                        );
                    }

                }

                // Add model owners
                if (in_array($modelClass, static::$ownerMonitoredModels)) {
                    $usersToNotify->push($model->owner);
                }

            }

        }

        return $usersToNotify;

    }

}
