<?php
namespace App\Services;

use Guzzle<PERSON>ttp\Client;
use Guz<PERSON><PERSON>ttp\HandlerStack;
use Guz<PERSON><PERSON>ttp\Middleware;
use GuzzleHttp\Psr7\HttpFactory;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Uri;
use Illuminate\Support\Facades\Log;
use MrMySQL\YoutubeTranscript\Exception\YouTubeRequestFailedException;
use MrMySQL\YoutubeTranscript\TranscriptList;
use MrMySQL\YoutubeTranscript\TranscriptListFetcher;
use Psr\Http\Message\RequestInterface;

class YoutubeTranscriptionClient
{

    protected TranscriptListFetcher $client;

    public function __construct()
    {
        $modifyRequestUriMiddleware = Middleware::mapRequest(function (RequestInterface $request) {
            $proxyUrl = get_proxied_url($request->getUri());
//            Log::debug('Proxy URL: ' . $proxyUrl);
            return $request->withUri(new Uri($proxyUrl));
        });
        $stack = HandlerStack::create();
        $stack->push($modifyRequestUriMiddleware);
        $client = new Client([
            'handler' => $stack,
//            'timeout' => env_int('TIMEOUT_MEDIUM', 300),
        ]);
//        increase_timeout('HIGH');
        $this->client = new TranscriptListFetcher($client, new HttpFactory(), new HttpFactory());
    }

    /**
     * @throws \Throwable
     */
    public static function getText(string $videoId, string|array $language, bool $includeTimestamps = false): string
    {

        $client = new static();
        $segments = $client->getByLanguage($videoId, $language);

        /*
        if ($includeTimestamps) {
            $ts = 0;
            $sentences = [];
            $str = '';
            foreach ($segments as $segment) {
                var_dump($segment);
                $str .= "[{$segment['start']}] {$segment['text']} ";
                $lineEnd = substr($segment['text'], -2);
                if (
                    str_contains($lineEnd, '.') ||
                    str_contains($lineEnd, '?') ||
                    str_contains($lineEnd, '!')
                ) {
                    $sentences[] = $str;
                    $str = '';
                }
            }
            $sentences[] = $str;

            var_dump($sentences);
        } else {

        }
        */

        $transcript = html_entity_decode(implode(" ", array_map(fn($segment) => $segment['text'], $segments)));
        $llm = new LlmClient();
        $cleanTranscript = $llm->cleanupTranscript($transcript);

        return $cleanTranscript;

    }

    /**
     * @throws \Throwable
     */
    public function getAll(string $videoId): TranscriptList
    {
        return $this->client->fetch($videoId);
    }

    /**
     * @throws \Throwable
     */
    public function getLanguages(string $videoId): array
    {
        $transcripts = $this->getAll($videoId);
        return $transcripts->getAvailableLanguageCodes();
    }

    /**
     * @throws \Throwable
     */
    public function getByLanguage(string $videoId, string|array $language): array
    {

        if (!is_array($language)) {
            $language = [$language];
        }

        $segments = [];
        $transcripts = $this->getAll($videoId);
//        Log::debug(json_encode($transcripts));
        try {
            $transcript = $transcripts->findTranscript($language);
            $segments = $transcript->fetch();
        } catch (YouTubeRequestFailedException $e) {
            Log::debug("Failed to fetch the transcript: {$e->getMessage()}");
        }

        if (empty($texts)) {
            try {
                $transcript = $transcripts->findGeneratedTranscript($language);
                $segments = $transcript->fetch();
            } catch (YouTubeRequestFailedException $e) {
                Log::debug("Failed to fetch the transcript: {$e->getMessage()}");
            }
        }

        return $segments;

    }

}
