<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;

class VectaraClient
{
    const BASE_URL = 'https://api.vectara.io/v2/corpora/{corpusId}/';

    const ENDPOINTS = [
        'get' => 'documents/{documentId}',
        'create' => 'documents',
        'delete' => 'documents/{documentId}',
        'query' => 'query',
        'list' => 'documents',
    ];

    const int TIMEOUT = 300;

    const int RATE_LIMIT_STATUS = 429;

    const int DOCUMENT_EXISTS_STATUS = 409;

    const int RATE_LIMIT_WAIT = 5;

    protected Client $client;

    protected int $customerId;

    protected string $corpusId;

    public function __construct(int $customerId, string $apiKey, int|string|null $corpusId = null)
    {
        $this->customerId = $customerId;
        $this->corpusId = $corpusId;
        $this->client = new Client([
            'base_uri' => replace_tokens(self::BASE_URL, ['corpusId' => $this->corpusId]),
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'customer-id' => $this->getCustomerId(),
                'x-api-key' => $apiKey,
                'timeout' => self::TIMEOUT.'S',
            ],
            'timeout' => self::TIMEOUT,
        ]);
    }

    /**
     * @throws GuzzleException
     */
    public function ragQuery(string $prompt, string $filters = ''): ?ResponseInterface
    {
        $config = [
            'sentences_before' => env_int('RAG_CONTEXT_SENTENCES'),
            'sentences_after' => env_int('RAG_CONTEXT_SENTENCES'),
        ];

        return $this->query($prompt, $filters, env_int('RAG_NUM_EMBEDDINGS'), $config);
    }

    /**
     * @return ResponseInterface|void
     *
     * @throws GuzzleException
     */
    public function query(string $prompt, string $filters = '', int $numResults = 10, array $config = []): ?ResponseInterface
    {

        $data = [
            'query' => $prompt,
            'search' => [
                'metadata_filter' => $filters,
                'context_configuration' => $config,
                'limit' => $numResults,
            ],
        ];

        try {
            $res = $this->client->post(self::ENDPOINTS['query'], ['json' => $data]);
            if ($this->isRateLimited($res)) {
                sleep(self::RATE_LIMIT_WAIT);
                $res = $this->query($prompt, $filters, $numResults, $config);
            }
            return $res;
        } catch (ClientException $e) {
            Log::debug(Psr7\Message::toString($e->getRequest()));
            Log::debug(Psr7\Message::toString($e->getResponse()));
            return null;
        }

    }

    /**
     * @throws GuzzleException
     */
    public function getDocument(string $id): ResponseInterface
    {

        $debug = "Getting Vectara document: {$id} ... ";
        try {
            $res = $this->client->get(replace_tokens(self::ENDPOINTS['get'], ['documentId' => $id]));
            if ($this->isRateLimited($res)) {
                sleep(self::RATE_LIMIT_WAIT);
                $res = $this->getDocument($id);
            }
            $debug .= 'OK';
        } catch (ClientException $e) {
            $res = $e->getResponse();
            $debug .= 'FAILED';
        }
        //        Log::debug($debug);

        return $res;

    }

    /**
     * @throws GuzzleException
     */
    public function listDocuments(int $limit = 10, ?string $filters = null, ?string $pageKey = null): ResponseInterface
    {

        try {
            $query = ['limit' => $limit];
            if (!empty($filters)) {
                $query['metadata_filter'] = $filters;
            }
            if (!is_null($pageKey)) {
                $query['page_key'] = $pageKey;
            }
            $res = $this->client->get(self::ENDPOINTS['list'], ['query' => $query]);
            if ($this->isRateLimited($res)) {
                sleep(self::RATE_LIMIT_WAIT);
                $res = $this->listDocuments($limit, $pageKey);
            }
        } catch (ClientException $e) {
            $res = $e->getResponse();
            Log::debug(Psr7\Message::toString($e->getRequest()));
        }

        return $res;

    }

    /**
     * @throws GuzzleException
     */
    public function indexDocument(string $id, ?array $meta = [], ?string $content = null, bool $skipDeletion = false): ResponseInterface
    {

        // First determine if the document exists, and delete it if so
        if (!$skipDeletion) {
            $this->deleteDocument($id);
        }

        $data = [
            'id' => strval($id),
            'type' => 'structured',
            'metadata' => $meta,
        ];
        if (! is_null($content)) {
            $data['sections'] = [[
                'text' => $content,
            ]];
        }

        $debug = "Indexing Vectara document {$id} to {$this->corpusId} ... ";
        try {
            $res = $this->client->post(self::ENDPOINTS['create'], ['json' => $data]);
            if ($this->isRateLimited($res) || $this->isAlreadyIndexed($res)) {
                sleep(self::RATE_LIMIT_WAIT);
                $res = $this->indexDocument($id, $meta, $content, !$this->isAlreadyIndexed($res));
            }
            $debug .= 'OK';
        } catch (ClientException $e) {
            Log::debug(Psr7\Message::toString($e->getRequest()));
            Log::debug(Psr7\Message::toString($e->getResponse()));
            $debug .= 'FAILED';
            $res = $e->getResponse();
        }
        Log::debug($debug);

        return $res;

    }

    /**
     * @throws GuzzleException
     */
    public function deleteDocument(string $id): ResponseInterface
    {

        // First determine if the document exists, and delete it if so
        $docRes = $this->getDocument($id);

        $debug = "Deleting Vectara document {$id} ... ";
        if ($docRes->getStatusCode() == 200) {

            try {
                $res = $this->client->delete(replace_tokens(self::ENDPOINTS['delete'], ['documentId' => $id]));
                if ($this->isRateLimited($res)) {
                    sleep(self::RATE_LIMIT_WAIT);
                    $res = $this->deleteDocument($id);
                }
                $debug .= 'OK, DELETED';
            } catch (ClientException $e) {
                $res = $e->getResponse();
                $debug .= 'FAILED';
            }

        } else {
            $debug .= "OK, DOESN'T EXIST";
            $res = $docRes;
        }
        Log::debug($debug);

        return $res;

    }

    protected function getCustomerId(): int
    {
        return $this->customerId;
    }

    protected function isRateLimited($res): bool
    {
        return ($res->getStatusCode() === self::RATE_LIMIT_STATUS);
    }

    protected function isAlreadyIndexed($res): bool
    {
        return ($res->getStatusCode() === self::DOCUMENT_EXISTS_STATUS);
    }

}
