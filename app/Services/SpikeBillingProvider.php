<?php

namespace App\Services;

use App\Http\Middleware\RedirectIfNotSubscribed;
use Exception;
use Filament\Billing\Providers\Contracts\Provider;
use Illuminate\Http\RedirectResponse;
use Opcodes\Spike\Facades\Spike;

class SpikeBillingProvider implements Provider
{

    public function getRouteAction(): string | \Closure | array
    {
        return function (): RedirectResponse {
            return redirect('billing');
        };
    }

    public function getSubscribedMiddleware(): string
    {
        return RedirectIfNotSubscribed::class;
    }

}
