<?php

namespace App\Services;

use Alaouy\Youtube\Facades\Youtube;
use App\Enums\CollectionType;
use App\Enums\SourceType;
use Carbon\Carbon;
use DateInterval;
use Exception;
use Illuminate\Support\Facades\Log;
use stdClass;

class YoutubeClient
{

    protected const MIN_DURATION = 180; // in seconds

    protected const THUMBNAIL_SIZES = [
        'maxres',
        'high',
        'medium',
        'standard',
        'default',
    ];

    protected const DEFAULT_HOST = 'youtube.com';

    protected const SHARE_HOST = 'youtu.be';

    protected const DEFAULT_SORT = 'date';

    protected const MAX_RESULTS = 50;

    protected string $apiKey;

    protected bool $overrideDurationRestrictions;

    public function __construct(?string $apiKey = null, bool $overrideDurationRestrictions = false)
    {
        $this->apiKey = $apiKey;
        $this->overrideDurationRestrictions = $overrideDurationRestrictions;
    }

    public function getVideos(string $channelOrPlaylistId, int $maxResults, ?int $beforeYear = null, ?string $order = null): array
    {
        if (static::isChannelId($channelOrPlaylistId)) {
            return $this->getVideosByChannelId($channelOrPlaylistId, $maxResults, $beforeYear, $order);
        } else {
            return $this->getVideosByPlaylistId($channelOrPlaylistId, $maxResults, $beforeYear);
        }
    }

    public function getVideosByChannelId(string $channelId, int $maxResults = 50, ?int $beforeYear = null, ?string $order = null): array
    {
        $params = [
            'channelId' => $channelId,
            'maxResults' => $maxResults,
            'order' => $order,
        ];
        if (!is_null($beforeYear)) {
            $params['publishedBefore'] = Carbon::createFromDate($beforeYear, 1, 1)->toRfc3339String();
        }
        return $this->getAllVideos($params);
    }

    public function getVideosByPlaylistId(string $playlistId, int $maxResults = 50): array
    {
        return Youtube::getPlaylistItemsByPlaylistId($playlistId, '', $maxResults);
    }

    /**
     * @throws Exception
     */
    protected function getAllVideos(array $params): array
    {

        $params = array_merge(
            $params,
            [
                'part' => ['id'],
                'relevanceLanguage' => 'en',
                'type' => 'video',
                'order' => $params['order'] ?? static::DEFAULT_SORT,
                'maxResults' => $params['maxResults'] ?? static::MAX_RESULTS,
            ]
        );

        $videos = [];
        $maxPages = ($params['maxResults'] >= static::MAX_RESULTS) ? 0 : 1;
        $nextPage = true;
        $currentPage = 1;
        while ($nextPage) {

            $search = Youtube::searchAdvanced($params, true);
            Log::debug($search);
            if (isset($search['results']) && ! empty($search['results'])) {

                // First, get the IDs of all videos that match the criteria
                $ids = array_map(fn ($item) => $item->id->videoId, $search['results']);

                // Next, get the full details for that list of IDs
                $results = Youtube::getVideoInfo($ids, ['id', 'snippet', 'contentDetails']);

                // Filter out any results that don't meet the minumum duration threshold
                if (!$this->overrideDurationRestrictions) {
                    $results = array_filter($results, fn ($result) => (
                        ! isset($result->contentDetails->duration) ||
                        (static::durationToSeconds($result->contentDetails->duration) >= static::MIN_DURATION)
                    ));
                }

                // Add only the valid results to the array of videos
                $videos = array_merge($videos, $results);

            }

            if (isset($search['info']['nextPageToken'])) {
                $params['pageToken'] = $search['info']['nextPageToken'];
            }
            $nextPage = (isset($search['info']['nextPageToken']) && ! empty($search['info']['nextPageToken']));

            if ($maxPages && ($currentPage >= $maxPages)) {
                break;
            }

            $currentPage++;

        }

        return $videos;

    }

    public static function isChannelId(string $channelOrPlaylistId): bool
    {
        return str_starts_with($channelOrPlaylistId, 'UC') || str_starts_with($channelOrPlaylistId, 'HC');
    }

    public static function getBestThumbnailUrl(object $thumbnails): string
    {
        foreach (static::THUMBNAIL_SIZES as $size) {
            if (isset($thumbnails->$size)) {
                return $thumbnails->$size->url;
            }
        }

        return 'default';
    }

    /**
     * @throws Exception
     */
    public static function durationToSeconds(string $duration): int
    {
        $interval = new DateInterval($duration);

        return ($interval->d * 24 * 60 * 60) +
            ($interval->h * 60 * 60) +
            ($interval->i * 60) +
            $interval->s;
    }

    public static function getVideoUrl(string $id): string
    {
        return replace_tokens(env('YOUTUBE_VIDEO_URL'), ['videoId' => $id]);
    }

    public static function getVideoLanguage(object $snippet): string
    {
        return isset($snippet->defaultAudioLanguage) ? substr($snippet->defaultAudioLanguage, 0, 2) : 'en';
    }

    public static function isValidUrl(string $url): bool
    {
        return url_matches($url, [static::DEFAULT_HOST, static::SHARE_HOST]);
    }

    public static function isChannelUrl(string $url): bool
    {
        return (
            str_contains($url, static::DEFAULT_HOST . '/@') ||
            str_contains($url, static::DEFAULT_HOST . '/channel/') ||
            str_contains($url, static::DEFAULT_HOST . '/c/') ||
            str_contains($url, static::DEFAULT_HOST . '/user/')
        );
    }

    public static function parseChannelFromUrl(string $url): string|null
    {
        if (static::isChannelUrl($url)) {
            return basename(parse_url($url, PHP_URL_PATH));
        }
        return null;
    }

    public static function getChannelByUrl(string $url): StdClass|null
    {
        if (static::isChannelUrl($url)) {
            $channelId = static::parseChannelFromUrl($url);
            if (str_contains($url, static::DEFAULT_HOST . '/channel/')) {
                return Youtube::getChannelById($channelId);
            } else if (str_contains($url, static::DEFAULT_HOST . '/@')) {
                return static::getChannelByHandle($channelId);
            } else {
                return Youtube::getChannelByName($channelId);
            }
        }
        return null;
    }

    public static function getChannelByHandle(string $handle): StdClass|null
    {
        $API_URL = Youtube::getApi('channels.list');
        $params = [
            'forHandle' => $handle,
            'part' => implode(',', ['id', 'snippet', 'contentDetails', 'statistics']),
        ];
        $apiData = Youtube::api_get($API_URL, $params);
        return Youtube::decodeSingle($apiData);
    }

    public static function isPlaylistUrl(string $url): bool
    {
        return str_contains($url, static::DEFAULT_HOST . '/playlist?list=');
    }

    public static function parsePlaylistIdFromUrl(string $url): string|null
    {
        if (static::isPlaylistUrl($url)) {
            parse_str(parse_url($url, PHP_URL_QUERY), $params);
            return $params['list'] ?? null;
        }
        return null;
    }

    public static function isVideoUrl(string $url): bool
    {
        if (static::isValidUrl($url)) {
            return !is_null(YoutubeClient::parseVideoIdFromUrl($url));
        }
        return false;
    }

    public static function parseVideoIdFromUrl(string $url): string|null
    {
        if (str_contains($url, static::SHARE_HOST)) {
            return basename(parse_url($url, PHP_URL_PATH));
        } else {
            parse_str(parse_url($url, PHP_URL_QUERY), $params);
            if (isset($params['v'])) {
                return $params['v'];
            }
        }
        return null;
    }

    public static function getVideoById(string $videoId): StdClass|null
    {
        return Youtube::getVideoInfo($videoId);
    }

}
