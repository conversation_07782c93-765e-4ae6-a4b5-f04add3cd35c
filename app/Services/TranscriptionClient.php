<?php

namespace App\Services;

use App\Services\TranscriptionClients\AssemblyAiClient;
use App\Services\TranscriptionClients\HappyScribeClient;
use App\Services\TranscriptionClients\SpeechFlowClient;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Log;

class TranscriptionClient
{
    const PROVIDERS = [
        'AssemblyAI' => [
            'client' => AssemblyAiClient::class,
            'key' => 'ASSEMBLYAI_API_KEY',
            'secret' => 'ASSEMBLYAI_API_KEY',
        ],
        'SpeechFlow' => [
            'client' => SpeechFlowClient::class,
            'key' => 'SPEECHFLOW_API_KEY',
            'secret' => 'SPEECHFLOW_API_SECRET',
        ],
        'HappyScribe' => [
            'client' => HappyScribeClient::class,
            'key' => 'HAPPYSCRIBE_ORGANIZATION_ID',
            'secret' => 'HAPPYSCRIBE_API_KEY',
        ],
    ];

    protected mixed $client;

    public function __construct(string $provider, string $language = 'en')
    {
        $clientClass = static::PROVIDERS[$provider]['client'];
        $this->client = new $clientClass(
            env(static::PROVIDERS[$provider]['key']),
            env(static::PROVIDERS[$provider]['secret']),
            $language
        );
    }

    public function transcribe(string $url, string $name): Response
    {

        $debug = "Creating remote media transcription from {$url} ... ";
        try {
            $res = $this->client->transcribe($url, $name);
            $debug .= 'OK';
        } catch (GuzzleException $e) {
            $res = $e->getResponse();
            $debug .= 'FAILED';
        }
        Log::debug($debug);

        return $res;

    }

    public function query(string $id): Response
    {

        $debug = "Querying transcription from ID: {$id} ... ";
        try {
            $res = $this->client->query($id);
            $debug .= 'OK';
        } catch (GuzzleException $e) {
            $res = $e->getResponse();
            $debug .= 'FAILED';
        }
        Log::debug($debug);

        return $res;

    }

    public function getField(string $field): ?string
    {
        return $this->client->getField($field);
    }

    public function transcriptIsReady(array $json): bool
    {
        return $this->client->transcriptIsReady($json);
    }

    public function transcriptIsProcessing(array $json): bool
    {
        return $this->client->transcriptIsProcessing($json);
    }

    /**
     * @return mixed|null
     */
    public function getErrorMessage(string $error): mixed
    {
        $json = json_decode($error, true);
        if (isset($json[$this->client->getField('error')])) {
            return $json[$this->client->getField('error')];
        }

        return null;
    }
}
