<?php

namespace App\Services;

use DateTimeImmutable;
use <PERSON><PERSON><PERSON><PERSON>\Clock\FrozenClock;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Builder;
use <PERSON><PERSON><PERSON><PERSON>\JWT\JwtFacade;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Hmac\Sha256;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key\InMemory;
use <PERSON><PERSON><PERSON><PERSON>\JWT\UnencryptedToken;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Validation\Constraint;

class Jwt
{
    public static function encode(
        string $str,
        string $expiresAt = '+1000 years',
        ?string $issuedBy = null,
        ?string $permittedFor = null
    ): string {

        if (is_null($issuedBy)) {
            $issuedBy = env('JWT_ISSUER');
        }
        if (is_null($permittedFor)) {
            $permittedFor = env('JWT_PERMITTED');
        }

        $token = (new JwtFacade)->issue(
            new Sha256,
            static::getSecret(),
            static fn (
                Builder $builder,
                DateTimeImmutable $issuedAt
            ): Builder => $builder
                ->identifiedBy($str)
                ->issuedBy($issuedBy)
                ->permittedFor($permittedFor)
                ->expiresAt($issuedAt->modify($expiresAt))
        );

        return $token->toString();

    }

    public static function decode(string $jwt): UnencryptedToken
    {

        $token = (new JwtFacade)->parse(
            $jwt,
            new Constraint\SignedWith(new Sha256, static::getSecret()),
            new Constraint\StrictValidAt(
                new FrozenClock(new DateTimeImmutable)
            )
        );

        return $token;

    }

    public static function getSecret(): InMemory
    {
        return InMemory::base64Encoded(
            base64_encode(env('JWT_SECRET'))
        );
    }
}
