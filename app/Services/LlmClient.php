<?php

namespace App\Services;

use App\Models\Agent;
use App\Models\Source;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;
use OpenAI\Laravel\Facades\OpenAI;

class LlmClient
{
    const TIMEOUT = 0;

    const MAX_TOKENS = 4096;

    protected Agent $agent;

    protected string $model;

    protected string $stripSeq;

    protected string $stopSeq;

    protected string $apiKey;

    protected string $baseUrl;

    protected const REWRITE_PROMPT = '
        You are a professional writer.
        Here is a {type}:
        ----------------
        {str}
        ----------------
        Rewrite the above {type} to make it unique and interesting.
        Respond with ONLY the rewritten text.
    ';

    protected const STRIP_REWRITE_TEXTS = [
        '----------------',
        'Here is the rewritten {type}:',
        'Here is the rewritten summary:',
        'Here is a rewritten version:',
        'Here is a rewritten version of the {type}:',
        'Here is the rewritten text:',
        'Here is your rewritten {type}:',
        'Think outside the box and be creative.',
    ];

    protected const CATEGORIZE_PROMPT = '
        Given the {context_title} below, select the best {num} categories from this list of categories:
        {categories}
        ----------------
        {context_title}
        {context_body}
        ----------------
        Respond with ONLY a list of the best {num} categor(ies) that match the above {context_title}.
        Respond in JSON format.
    ';

    protected const TRANSCRIPT_PROMPT = '
        Clean up the following text. Add punctuation where appropriate. Remove filler words like \'um\' and \'uh\'.
        Respond with ONLY the cleaned up text.
        ----------------
        {text}
    ';

    const ENDPOINTS = [
        'completion' => '/completions',
        'chat' => '/chat/completions',
    ];

    protected \OpenAI\Resources\Chat $client;

    protected int $customerId;

    protected int $corpusId;

    public function __construct()
    {
        $this->client = OpenAI::chat();
    }

    public function prompt(string $prompt, ?array $format = null): ?array
    {

//        Log::debug("PROMPT: {$prompt}");

        try {

            $params = [
                'model' => 'gpt-4o-mini',
                'messages' => [['role'=>'user', 'content'=>$prompt]],
            ];
            if ($format) {
//                Log::debug("Format: " . json_encode($format));
                $params['format'] = json_encode($format);
            }

            $result = $this->client->create($params);
//            Log::debug("RESULT: " . json_encode($result));
            return $result->choices;

        } catch (Exception $e) {
            Log::error($e->getMessage());
            return null;
        }

    }

    public function rewrite(?string $str, ?string $type = 'description'): ?string
    {

        if (empty($str)) {
            return null;
        }

        $prompt = replace_tokens(trim(static::REWRITE_PROMPT), ['str' => remove_html($str), 'type' => $type]);
        $choices = $this->prompt($prompt);

        if (is_array($choices)) {
            return $choices[0]->message->content;
        } else {
            Log::error($choices);
            return null;
        }

    }

    public function suggestCategories(array $categories, string $contextTitle, string $contextBody, int $maxCategories): array
    {

        $selectedCategories = null;
        $prompt = replace_tokens(
            static::CATEGORIZE_PROMPT,
            [
                'num' => $maxCategories,
                'categories' => implode(', ', array_values($categories)),
                'context_title' => $contextTitle,
                'context_body' => $contextBody,
            ]
        );

        $responses = $this->prompt(
            $prompt,
            ['type'=>'json_object']
        );

        if (is_array($responses)) {
            $firstResponse = $responses[0]->message->content;
            $categoriesStartPos = strpos($firstResponse, '[');
            $categoriesEndPos = strrpos($firstResponse, ']');
            $json = substr($firstResponse, $categoriesStartPos, $categoriesEndPos - $categoriesStartPos + 1);
            $selectedCategories = json_decode($json);
        } else {
            Log::error($responses);
        }

        if (! is_null($selectedCategories)) {
            try {

                $selectedCategoryIds = [];
                foreach ($selectedCategories as $selectedCategory) {
                    $selectedCategoryIds[] = array_search($selectedCategory, $categories);
                }

                $uniqueCategoryIds = array_filter($selectedCategoryIds);
                return $uniqueCategoryIds;

            } catch (Exception $e) {
                return [];
            }
        } else {
            return [];
        }

    }

    public function cleanupTranscript(?string $str): string|null
    {

        if (empty($str)) {
            return null;
        }

        $prompt = replace_tokens(trim(static::TRANSCRIPT_PROMPT), ['text' => $str]);
        $choices = $this->prompt($prompt);

        if (is_array($choices)) {
            $response = $choices[0]->message->content;
            return trim($response);
        } else {
            Log::error($choices);
        }

        return null;

    }

}
