<?php

namespace App\Services\TranscriptionClients;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;

class HappyScribeClient extends TranscriptionClient
{
    const BASE_URL = 'https://www.happyscribe.com/api/v1/';

    const ENDPOINTS = [
        'createTranscription' => 'transcriptions',
        'getTranscription' => 'transcriptions/{transcriptionId}',
        'createExport' => 'exports',
        'getExport' => 'exports/{exportId}',
    ];

    const FIELDS = [
        'id' => 'id',
        'txt' => null,
        'url' => 'download_link',
        'error' => 'error',
    ];

    const STATUS_CODES = [
        'failed' => 'failed',
        'complete' => 'automatic_done',
        'ready' => 'ready',
        'expired' => 'expired',
    ];

    protected int $orgId;

    public function __construct(string $orgId, string $apiKey, string $language = 'en')
    {

        $this->orgId = intval($orgId);
        $headers = [
            'Authorization' => "Bearer {$apiKey}",
            'Content-Type' => 'application/json',
        ];

        $this->options = [
            'base_uri' => self::BASE_URL,
            'headers' => $headers,
            'timeout' => self::TIMEOUT,
        ];

        $this->client = new Client($this->options);

    }

    /**
     * @return Response
     *
     * @throws GuzzleException
     */
    public function transcribe(string $url, string $name): ResponseInterface
    {

        $options = $this->options;
        $options['json'] = [
            'transcription' => [
                'name' => $name,
                'language' => 'en-US',
                'tmp_url' => $url,
                'organization_id' => $this->orgId,
                'is_subtitle' => false,
            ],
        ];

        Log::debug('Transcription Request: '.json_encode($options, JSON_UNESCAPED_SLASHES));

        return $this->client->post(
            self::ENDPOINTS['createTranscription'],
            $options
        );

    }

    /**
     * @throws GuzzleException
     */
    public function query(string $transcriptionId): ResponseInterface
    {

        $uri = replace_tokens(static::ENDPOINTS['getTranscription'], ['transcriptionId' => $transcriptionId]);
        $res = $this->client->get($uri);
        $contents = $res->getBody()->getContents();
        Log::debug($contents);
        $json = json_decode($contents, true);

        // If the transcript isn't complete or has an error, bail now
        if (
            ($res->getStatusCode() != 200) ||
            ! $this->transcriptIsReady($json)
        ) {
            return $res;
        }

        $options = $this->options;
        $options['json'] = [
            'export' => [
                'format' => 'txt',
                'transcription_ids' => [
                    $transcriptionId,
                ],
            ],
        ];
        Log::debug(json_encode($options, JSON_UNESCAPED_SLASHES));
        $res = $this->client->post(
            self::ENDPOINTS['createExport'],
            $options
        );
        $contents = $res->getBody()->getContents();
        Log::debug($contents);

        // If the creation wasn't successful, bail now
        if ($res->getStatusCode() != 200) {
            return $res;
        }

        $json = json_decode($contents, true);
        $exportId = $json['id'];

        return $this->pollExport($exportId);

    }

    /**
     * @return ResponseInterface|void
     *
     * @throws GuzzleException
     */
    protected function pollExport(string $exportId)
    {
        Log::debug("Polling export ID: {$exportId} ...");
        $uri = replace_tokens(static::ENDPOINTS['getExport'], ['exportId' => $exportId]);
        $res = $this->client->get($uri);
        $contents = $res->getBody()->getContents();
        $json = json_decode($contents, true);
        Log::debug($contents);
        if (in_array($json['state'], [static::STATUS_CODES['expired'], static::STATUS_CODES['failed'], static::STATUS_CODES['ready']])) {
            return $res;
        } else {
            sleep(30);
            $this->pollExport($exportId);
        }
    }

    public function transcriptIsReady(array $json): bool
    {
        return in_array($json['state'], [static::STATUS_CODES['complete'], static::STATUS_CODES['ready']]);
    }

    public function transcriptIsProcessing(array $json): bool
    {
        return ! in_array($json['state'], [static::STATUS_CODES['complete'], static::STATUS_CODES['failed']]);
    }
}
