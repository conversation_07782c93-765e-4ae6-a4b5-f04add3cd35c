<?php

namespace App\Services\TranscriptionClients;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;

class SpeechFlowClient extends TranscriptionClient
{
    const BASE_URL = 'https://api.speechflow.io/asr/file/v1/';

    const ENDPOINTS = [
        'create' => 'create',
        'query' => 'query?taskId={taskId}&resultType={resultType}',
    ];

    const FIELDS = [
        'id' => 'taskId',
        'txt' => 'result',
        'error' => 'msg',
    ];

    const RESULT_TYPE = 4;

    const STATUS_CODES = [
        'pending' => 10000,
        'processing' => 11001,
        'complete' => 11000,
    ];

    public function __construct(string $apiKey, string $apiSecret, string $language = 'en')
    {

        $headers = [
            'keyId' => $apiKey,
            'keySecret' => $apiSecret,
            'language' => $language,
        ];

        $this->options = [
            'base_uri' => self::BASE_URL,
            'headers' => $headers,
            'timeout' => self::TIMEOUT,
        ];

        $this->client = new Client($this->options);

    }

    /**
     * @throws GuzzleException
     */
    public function transcribe(string $url, string $name): ResponseInterface
    {

        $options = $this->options;
        $options['headers']['Content-Type'] = 'application/x-www-form-urlencoded';
        $options['form_params'] = [
            'lang' => $this->options['headers']['language'],
            'remotePath' => $url,
        ];

        Log::debug('Transcription Request: '.json_encode($options, JSON_UNESCAPED_SLASHES));

        return $this->client->post(
            static::ENDPOINTS['create'],
            $options
        );

    }

    /**
     * @throws GuzzleException
     */
    public function query(string $taskId): ResponseInterface
    {
        $uri = replace_tokens(static::ENDPOINTS['query'], ['taskId' => $taskId, 'resultType' => static::RESULT_TYPE]);

        return $this->client->get($uri);
    }

    public function transcriptIsReady(array $json): bool
    {
        return $json['code'] == static::STATUS_CODES['complete'];
    }

    public function transcriptIsProcessing(array $json): bool
    {
        return $json['code'] == static::STATUS_CODES['processing'];
    }
}
