<?php

namespace App\Services\TranscriptionClients;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;

class AssemblyAiClient extends TranscriptionClient
{
    const BASE_URL = 'https://api.assemblyai.com/v2/';

    const ENDPOINTS = [
        'create' => 'transcript',
        'query' => 'transcript/{transcriptId}',
    ];

    const FIELDS = [
        'id' => 'id',
        'txt' => 'text',
        'error' => 'error',
    ];

    const STATUS_CODES = [
        'failed' => 'error',
        'complete' => 'completed',
        'processing' => 'processing',
    ];

    public function __construct(string $apiKey, string $apiSecret, string $language = 'en')
    {

        $headers = [
            'authorization' => $apiKey,
            'Content-Type' => 'application/json',
        ];

        $this->options = [
            'base_uri' => self::BASE_URL,
            'headers' => $headers,
            'timeout' => self::TIMEOUT,
        ];

        $this->client = new Client($this->options);

    }

    /**
     * @return Response
     *
     * @throws GuzzleException
     */
    public function transcribe(string $url, string $name): ResponseInterface
    {

        $options = $this->options;
        $options['json'] = [
            //            'language'  =>  'en-US',
            'audio_url' => $url,
            'speaker_labels' => true,
        ];

        Log::debug('Transcription Request: '.json_encode($options, JSON_UNESCAPED_SLASHES));

        return $this->client->post(
            self::ENDPOINTS['create'],
            $options
        );

    }

    /**
     * @throws GuzzleException
     */
    public function query(string $transcriptId): ResponseInterface
    {
        $uri = replace_tokens(static::ENDPOINTS['query'], ['transcriptId' => $transcriptId]);

        return $this->client->get($uri);
    }

    public function transcriptIsReady(array $json): bool
    {
        return $json['status'] === static::STATUS_CODES['complete'];
    }

    public function transcriptIsProcessing(array $json): bool
    {
        return (
            ($json['status'] !== static::STATUS_CODES['complete']) &&
            (
                $json['status'] !== static::STATUS_CODES['failed'] ||
                str_contains($json['error'], 'balance')
            )
        );
    }
}
