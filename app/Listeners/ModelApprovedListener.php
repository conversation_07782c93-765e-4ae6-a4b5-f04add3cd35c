<?php

namespace App\Listeners;

use App\Enums\JobCategoryTag;
use App\Enums\NotificationType;
use App\Jobs\Traits\HasTags;
use Illuminate\Database\Eloquent\Model;
use RingleSoft\LaravelProcessApproval\Enums\ApprovalStatusEnum;
use RingleSoft\LaravelProcessApproval\Events\ProcessApprovedEvent;

class ModelApprovedListener
{
    use HasTags;

    public ?Model $model = null;

    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(ProcessApprovedEvent $event): void
    {
        $model = $event->approval->approvable;
        $this->model = $model;
        $model->update(['is_approved' => true]);
        $model->approvalStatus()->update(['status' => ApprovalStatusEnum::APPROVED->value]); // TODO: This should not be necessary!!!
        $usersToNotify = $model->team?->admins()->notifyNow(NotificationType::APPROVALS)->get();
        if ($usersToNotify) {
            foreach ($usersToNotify as $user) {
                $user->sendModelApprovalNotification($model);
            }
        }
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::WORKFLOW], $this->model);
    }
}
