<?php

namespace App\Listeners;

use Altek\Accountant\Events\Recording;
use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasTags;

class LedgerRecordingListener
{
    use HasTags;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Recording $event): ?bool
    {

        // Add data to relationship sync events
        $recordNewLedger = true;
        if ($event->event == 'synced') {
            $latestLedger = $event->model->ledgers()
                ->where('event', 'synced')
                ->where('pivot->relation', $event->pivotRelation)
                ->latest()
                ->first();
            if ($latestLedger) {
                $oldRelations = $latestLedger->getPivotData()['properties'];
                $newRelations = $event->pivotProperties;
                $recordNewLedger = ($oldRelations != $newRelations);
            }
        }

        return $recordNewLedger;

    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::NOTIFY]);
    }
}
