<?php

namespace App\Listeners;

use App\Enums\JobCategoryTag;
use App\Events\UserCreatedEvent;
use App\Jobs\Traits\HasTags;
use App\Models\Membership;
use App\Models\User;
use App\Services\Gatekeeper;
use Throwable;

class UserSubscribeListListener
{
    use HasTags;

    public ?User $user = null;

    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(UserCreatedEvent $event): void
    {
        $this->user = $event->user;
        $this->user->addToMailingList(env('SENDGRID_USER_LIST_ID'));
        $this->user->addToMailingList(env('SENDGRID_ORG_LIST_ID'));
    }

    /**
     * Handle a job failure.
     */
    public function failed(UserCreatedEvent $event, Throwable $exception): void {}

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::USER], $this->user);
    }
}
