<?php

namespace App\Listeners;

use App\Enums\JobCategoryTag;
use App\Enums\NotificationType;
use App\Jobs\Traits\HasTags;
use App\Models\User;
use RingleSoft\LaravelProcessApproval\Events\ProcessSubmittedEvent;

class ModelSubmittedListener
{
    use HasTags;

    public $model = null;

    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(ProcessSubmittedEvent $event): void
    {
        $model = $event->approvable;
        $this->model = $model;
        $usersToNotify = $model->team?->admins()->notifyNow(NotificationType::APPROVALS)->get()
            ->merge(
                User::superadmins()->notifyNow(NotificationType::APPROVALS)->get()
            )->unique('id');
        foreach ($usersToNotify as $user) {
            $user->sendModelSubmissionNotification($model);
        }
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::WOR<PERSON>FLOW], $this->model);
    }
}
