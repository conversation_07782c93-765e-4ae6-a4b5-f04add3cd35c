<?php

namespace App\Listeners;

use App\Console\Commands\ImportChannelSources;
use App\Console\Commands\ImportPodcastSources;
use App\Console\Commands\ImportRssSources;
use App\Console\Commands\ImportWebsiteSources;
use App\Enums\CollectionType;
use App\Enums\JobCategoryTag;
use App\Events\CollectionSavedEvent;
use App\Jobs\Traits\HasTags;
use App\Models\Collection;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class CollectionSavedListener implements ShouldQueue
{
    use HasTags, InteractsWithQueue;

    protected const TRIGGER_FIELDS = [
        CollectionType::RSS->value => 'url',
        CollectionType::WEBSITE->value => 'url',
        CollectionType::CHANNEL->value => 'external_id',
        CollectionType::PODCAST->value => 'url',
    ];

    public ?Collection $collection = null;

    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(CollectionSavedEvent $event): void
    {

        $this->collection = $event->collection;
        $collectionType = $this->collection->type->value;
        $triggerField = static::TRIGGER_FIELDS[$collectionType] ?? null;

        Log::debug('Collection Changes: '.json_encode($this->collection->getChanges(), JSON_UNESCAPED_SLASHES));

        if (
            $this->collection->auto_import_sources &&
            $triggerField &&
            $this->collection->hasChanged([$triggerField, 'auto_import_sources']) &&
            ! empty($this->collection->$triggerField)
        ) {
            $this->collection->importSources(true);
        }

    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::IMPORT], $this->collection);
    }
}
