<?php

namespace App\Listeners;

use App\Enums\JobCategoryTag;
use App\Events\TeamCreatedEvent;
use App\Jobs\Traits\HasTags;
use App\Models\Membership;
use App\Models\Team;
use App\Services\Gatekeeper;
use Throwable;

class TeamSetupListener
{
    use HasTags;

    public ?Team $team = null;

    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(TeamCreatedEvent $event): void
    {

        $this->team = $event->team;

        // Add the creator as an admin member
        $membership = Membership::firstOrCreate([
            'model_id' => $event->team->user_id,
            'team_id' => $event->team->id,
            'role_id' => Gatekeeper::getManagerRoleId(),
        ]);

        // Set the current tenant
        auth()->user()->switchTenant($event->team->id);

    }

    /**
     * Handle a job failure.
     */
    public function failed(TeamCreatedEvent $event, Throwable $exception): void {}

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::TEAM], $this->team);
    }
}
