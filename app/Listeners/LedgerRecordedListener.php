<?php

namespace App\Listeners;

use Altek\Accountant\Events\Recorded;
use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasTags;
use App\Services\Auditor;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class LedgerRecordedListener implements ShouldQueue
{
    use HasTags, InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Recorded $event): void
    {

        $usersToNotify = Auditor::getUsersToNotify($event->ledger);

        // Notify unique users
        if (! $usersToNotify->isEmpty()) {
            foreach ($usersToNotify->unique('id') as $user) {
                if ($user) {
                    $user->sendModelChangeNotification($event);
                }
            }
        }

    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::NOTIFY]);
    }
}
