<?php

namespace App\Listeners;

use App\Enums\JobCategoryTag;
use App\Enums\SourceType;
use App\Events\SourceSavedEvent;
use App\Jobs\ConvertSourceDocumentJob;
use App\Jobs\DownloadSourceYoutubeTranscriptJob;
use App\Jobs\ImportEpisodeSourceDataJob;
use App\Jobs\ImportUrlSourceDataJob;
use App\Jobs\ImportYoutubeSourceDataJob;
use App\Jobs\ManageSourceRagDocumentJob;

use App\Jobs\Traits\HasTags;
use App\Jobs\TranscribeSourceMediaJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

class SourceSavedListener implements ShouldQueue
{
    use HasTags, InteractsWithQueue;

    public ?Model $model = null;

    const SOURCE_IMPORTERS = [
        SourceType::URL->value => ImportUrlSourceDataJob::class,
        SourceType::YOUTUBE->value => ImportYoutubeSourceDataJob::class,
        SourceType::EPISODE->value => ImportEpisodeSourceDataJob::class,
    ];

    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(SourceSavedEvent $event): void
    {

        $this->model = $event->source;
        Log::debug('Source Changes: '.json_encode($event->source->getChanges(), JSON_UNESCAPED_SLASHES));

        $jobs = [];

        if (
            isset(static::SOURCE_IMPORTERS[$event->source->type->value]) &&
            $event->source->hasChanged([
                'url',
                'url_content_selector',
                'url_author_selector',
                'url_published_selector',
                'url_image_selector',
                'url_render_js',
                'external_id',
                'is_approved',
            ]) &&
            ! $event->source->isImported()
        ) {
            $jobClass = static::SOURCE_IMPORTERS[$event->source->type->value];
            $jobs[] = new $jobClass($event->source);
        }

        if ($event->source->hasChanged(['media_path', 'media_url'])) {

            Log::debug('MEDIA CHANGED');

            if (
                $event->source->importerCollection?->auto_transcribe_sources &&
                in_array($event->source->type, [SourceType::MEDIA, SourceType::EPISODE, SourceType::YOUTUBE]) &&
                $event->source->canTranscribe()
            ) {
                if ($event->source->type == SourceType::YOUTUBE) {
                    Log::debug('GET YOUTUBE TRANSCRIPT');
                    $jobs[] = new DownloadSourceYoutubeTranscriptJob($event->source);
                } else {
                    Log::debug('TRANSCRIBE MEDIA');
                    $jobs[] = new TranscribeSourceMediaJob($event->source);
                }
            }

            if (
                in_array($event->source->type, [SourceType::BOOK, SourceType::ARTICLE]) &&
                $event->source->canConvertDocument()
            ) {
                Log::debug('CONVERT DOCUMENT');
                $jobs[] = new ConvertSourceDocumentJob(
                    $event->source,
                    ($event->source->hasChanged('media_url') && !is_null($event->source->media_url))
                );
            }

        }

        if (count($event->source->getChanges()) > 0) {
            $jobs[] = new ManageSourceRagDocumentJob($event->source);
        }

        if (! empty($jobs)) {
            Bus::chain($jobs)->dispatch();
        }

    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::AUDIT], $this->model);
    }
}
