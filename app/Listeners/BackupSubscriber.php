<?php

namespace App\Listeners;

use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasTags;
use Illuminate\Events\Dispatcher;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Spatie\Backup\Events\BackupWasSuccessful;
use Spatie\Backup\Events\CleanupWasSuccessful;
use Symfony\Component\Console\Output\ConsoleOutput;

class BackupSubscriber
{
    use HasTags;

    public function subscribe(Dispatcher $events): void
    {

        $events->listen(
            BackupWasSuccessful::class,
            [BackupSubscriber::class, 'handleBackup']
        );

        $events->listen(
            CleanupWasSuccessful::class,
            [BackupSubscriber::class, 'handleCleanup']
        );

    }

    public function handleBackup(BackupWasSuccessful $event): void
    {

        increase_timeout('MAX');

        $backupDestination = $event->backupDestination;
        $backupPath = Str::replaceLast('.zip', '', $backupDestination->newestBackup()->path());

        $output = new ConsoleOutput;

        $sources = config('backup.backup.source.disks') ?? [];

        foreach ($sources as $disk) {
            $output->writeln('<info>Determining files to backup for disk '.$disk.'...</info>');
            $destinationPath = $backupPath.'/'.$disk.'/';

            $files = Storage::disk($disk)->allFiles();
            $output->writeln('<info>Copying disk '.$disk.' to disk named backup: '.count($files).' files</info>');

            foreach ($files as $file) {
                Storage::disk($backupDestination->diskName())->put(
                    $destinationPath.$file,
                    Storage::disk($disk)->get($file)
                );
            }
        }

    }

    public function handleCleanup(CleanupWasSuccessful $event): void
    {

        increase_timeout('MAX');

        $backupDestination = $event->backupDestination;

        $output = new ConsoleOutput;
        $output->writeln('<info>Cleaning disks backups of '.$backupDestination->backupName().' on disk '.$backupDestination->diskName().'...</info>');

        $directories = Storage::disk($backupDestination->diskName())->directories($backupDestination->backupName());

        if (count($directories) > 0) {
            $backups = $backupDestination
                ->fresh()
                ->backups()
                ->map(function ($backup) {
                    return Str::replaceLast('.zip', '', $backup->path());
                })
                ->toArray();

            $directoriesToDelete = array_filter($directories, function ($directory) use ($backups) {
                return ! in_array($directory, $backups);
            });

            if (count($directoriesToDelete) > 0) {
                foreach ($directoriesToDelete as $directory) {
                    Storage::disk($backupDestination->diskName())->deleteDirectory($directory);
                }
            }
        }

    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::BACKUP]);
    }
}
