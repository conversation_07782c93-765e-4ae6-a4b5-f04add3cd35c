<?php

namespace App\Listeners;

use App\Enums\JobCategoryTag;
use App\Enums\NotificationType;
use App\Jobs\Traits\HasTags;
use Illuminate\Database\Eloquent\Model;
use RingleSoft\LaravelProcessApproval\Events\ProcessRejectedEvent;

class ModelRejectedListener
{
    use HasTags;

    public ?Model $model = null;

    /**
     * Create the event listener.
     */
    public function __construct() {}

    /**
     * Handle the event.
     */
    public function handle(ProcessRejectedEvent $event): void
    {
        $model = $event->approval->approvable;
        $this->model = $model;
        $model->update(['is_approved' => false]);
        $comment = $event->approval->comment;
        $usersToNotify = $model->team->admins()->notifyNow(NotificationType::APPROVALS)->get();
        foreach ($usersToNotify as $user) {
            $user->sendModelRejectionNotification($model, $comment);
        }
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::WORKFLOW], $this->model);
    }
}
