<?php

use App\Models\Team;
use App\Services\Gatekeeper;
use App\Services\Jwt;
use App\Services\VectaraClient;
use Carbon\Carbon;
use Detection\MobileDetect;
use Filament\Facades\Filament;
use Google\Cloud\Core\Exception\GoogleException;
use Google\Cloud\Core\Exception\ServiceException;
use Google\Cloud\Translate\V2\TranslateClient;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Mail\SentMessage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use OsiemSiedem\Autolink\Elements\UrlElement;
use OsiemSiedem\Autolink\Facades\Autolink;

if (! function_exists('app_prefix')) {
    function app_prefix(): string
    {
        return Str::slug(env('APP_NAME'), '_');
    }
}

if (! function_exists('app_domain')) {
    function app_domain(?string $subdomain = 'app', ?int $port = null): string
    {
        $base = env('APP_BASE_DOMAIN');

        return "{$subdomain}.{$base}".($port ? ":{$port}" : '');
    }
}

if (! function_exists('is_mobile')) {
    function is_mobile(): bool
    {
        return (new MobileDetect)->isMobile();
    }
}

if (! function_exists('env_int')) {
    function env_int($var, $default = 0): int
    {
        return intval(env($var, $default));
    }
}

if (! function_exists('env_float')) {
    function env_float($var): float
    {
        return floatval(env($var));
    }
}

if (! function_exists('env_array')) {
    function env_array($var): array
    {
        return explode(',', env($var));
    }
}

if (! function_exists('is_enum')) {
    function is_enum($var): bool
    {
        return $var instanceof \UnitEnum;
    }
}

if (! function_exists('is_image_mime')) {
    function is_image_mime(string $mime): bool
    {
        return str_starts_with($mime, 'image');
    }
}

if (! function_exists('is_video_mime')) {
    function is_video_mime(string $mime): bool
    {
        return str_starts_with($mime, 'video');
    }
}

if (! function_exists('is_audio_mime')) {
    function is_audio_mime(string $mime): bool
    {
        return str_starts_with($mime, 'audio');
    }
}

if (! function_exists('is_html_mime')) {
    function is_html_mime(string $mime): bool
    {
        return str_ends_with($mime, 'html');
    }
}

if (! function_exists('is_media_mime')) {
    function is_media_mime(string $mime): bool
    {
        return (is_video_mime($mime) || is_audio_mime($mime));
    }
}

if (! function_exists('is_doc_mime')) {
    function is_doc_mime(string $mime): bool
    {
        return (
            str_ends_with($mime, 'pdf') ||
            str_ends_with($mime, 'msword') ||
            str_ends_with($mime, 'officedocument.wordprocessingml.document') ||
            str_ends_with($mime, 'epub+zip') ||
            str_ends_with($mime, 'vnd.oasis.opendocument.text')
        );
    }
}

if (! function_exists('get_path_url')) {
    function get_path_url(?string $path): ?string
    {
        return empty($path) ? null : Storage::disk('public')->url($path);
    }
}

if (! function_exists('get_support_link')) {
    function get_support_link(string $text): HtmlString
    {
        return get_link_html(env('SUPPORT_URL'), $text);
    }
}

if (! function_exists('get_link_html')) {
    function get_link_html(string|array|null $url, string|array|null $text = null): ?HtmlString
    {
//        Log::debug($url);
//        Log::debug($text);
        if (empty($url)) {
            return null;
        }
        if (is_array($url)) {
            $links = [];
            foreach ($url as $key => $val) {
                $links[] = get_link_html($key, $val);
            }

            return new HtmlString(implode(', ', $links));
        } else {
            $text = $text ?? $url;

            return new HtmlString("<a href='{$url}' target='_blank' style='text-decoration: underline'>{$text}</a>");
        }
    }
}

if (! function_exists('replace_tokens')) {
    function replace_tokens(string $str, array $tokens = []): string
    {
        foreach ($tokens as $search => $replace) {
            $str = str_replace('{'.$search.'}', $replace ?? '', $str);
        }

        return $str;
    }
}

if (! function_exists('strip_paragraphs')) {
    function strip_paragraphs(?string $str): ?string
    {
        if (is_null($str)) {
            return null;
        }

        return str_replace(['<p>', '</p>'], '', $str);
    }
}

if (! function_exists('strip_links')) {
    function strip_links(?string $str): ?string
    {
        return preg_replace('#<a.*?>(.*?)</a>#is', '\1', $str);
    }
}

if (! function_exists('get_current_team_id')) {
    function get_current_team_id(): ?int
    {
        if (auth()->check() && auth()->user()->current_team_id) {
            return auth()->user()->current_team_id;
        } elseif (Filament::getTenant()) {
            return Filament::getTenant()->id;
        }
        return env('ADMIN_TEAM_ID');
    }
}

if (! function_exists('get_current_team')) {
    function get_current_team(): ?Model
    {
        if (auth()->check() && auth()->user()->currentTeam) {
            return auth()->user()->currentTeam;
        } elseif (Filament::getTenant()) {
            return Filament::getTenant();
        }
        return Team::find(env('ADMIN_TEAM_ID'));
    }
}

if (! function_exists('save_file_from_url')) {
    function save_file_from_url(string $url, string $directory, ?string $extension = null): bool|string
    {

        Log::debug("Getting File: {$url} ... ");
        //        $response = Http::withOptions(['debug' => true])->timeout(900)->get($url);
        try {
            $response = Http::timeout(900)->get($url);
            if ($response->ok()) {
                if (is_null($extension)) {
                    $extension = get_file_extension(parse_url($url, PHP_URL_PATH));
                }
                Log::debug("Successfully Downloaded: {$url} ({$extension})");

                return save_file(
                    $response->body(),
                    $extension,
                    $directory
                );
            }
        } catch (\Throwable $e) {
            Log::debug("ERROR Downloading: {$url}; {$e->getMessage()}");
        }

        return false;

    }
}

if (! function_exists('save_file')) {
    function save_file(string $contents, string $extension, ?string $directory = null): bool|string
    {
        $filename = Str::ulid().'.'.$extension;
        $path = "{$directory}/{$filename}";
        Storage::disk('public')->put($path, $contents);
        Log::debug("SAVED: {$path}");

        return $path;
    }
}

if (! function_exists('get_file_extension')) {
    function get_file_extension(string $path): string
    {
        return strtolower(pathinfo($path, PATHINFO_EXTENSION));
    }
}

if (! function_exists('parse_language_code')) {
    function parse_language_code(string $str): string
    {
        return strtolower(substr($str, 0, 2));
    }
}

if (! function_exists('convert_paragraphs')) {
    function convert_paragraphs(string $str): string
    {
        return '<p>'.implode('</p><p>', array_filter(explode("\n", $str))).'</p>';
    }
}

if (! function_exists('convert_links')) {
    function convert_links(string $str): string
    {
//        Log::debug($str);
        return Autolink::convert($str, function ($element) {
            if ($element instanceof UrlElement) {
                $element->getAttributes()->setAttribute('target', '_blank');
            }
            return $element;
        });
    }
}

if (! function_exists('convert_html')) {
    function convert_html(string $str): string
    {
        return convert_paragraphs(convert_links($str));
    }
}

if (! function_exists('get_model_table')) {
    function get_model_table(string $className): string
    {
        return (new $className)->getTable();
    }
}

if (! function_exists('add_url_params')) {
    function add_url_params(string $url, array $params): string
    {
        $parts = parse_url($url, PHP_URL_QUERY);
        if ($parts) {
            parse_str($parts, $queryParams);
        } else {
            $queryParams = [];
        }

        return $url.'?'.http_build_query(array_merge($queryParams, $params));
    }
}

if (! function_exists('add_utf8_bom')) {
    function add_utf8_bom(string $str): string
    {
        return "\xEF\xBB\xBF".$str;
    }
}

if (! function_exists('wrap_csv_string')) {
    function wrap_csv_string(string $str): string
    {
        return '"'.str_replace('"', '""', $str).'"';
    }
}

if (! function_exists('get_rag_client')) {
    function get_rag_client(?Team $team = null): VectaraClient
    {
        if ($team && $team->has_custom_corpus) {
            $corpusKey = env('TEAM_CORPUS_PREFIX').$team->id;
            $apiKey = $team->corpus_api_key;
        } else {
            $corpusKey = env('RAG_CORPUS_KEY');
            $apiKey = env('RAG_API_KEY');
        }

        return new VectaraClient(env_int('RAG_CUSTOMER_ID'), $apiKey, $corpusKey);
    }
}

if (! function_exists('get_search_client')) {
    function get_search_client(): VectaraClient
    {
        return new VectaraClient(env_int('RAG_CUSTOMER_ID'), env('SEARCH_API_KEY'), env('SEARCH_CORPUS_KEY'));
    }
}

if (! function_exists('increase_timeout')) {
    function increase_timeout(string $level = 'MEDIUM'): void
    {
        $level = strtoupper($level);
        $memoryEnv = "MEMORY_{$level}";
        $timeoutEnv = "TIMEOUT_{$level}";
        ini_set('memory_limit', env($memoryEnv, '512M'));
        set_time_limit(env_int($timeoutEnv, 300));
    }
}

if (! function_exists('translate_text')) {
    /**
     * @throws GoogleException
     * @throws ServiceException
     */
    function translate_text(string $text, string $to, ?string $from = null): string
    {
        $from = expand_language_code($from);
        $to = expand_language_code($to);

        // DeepL
        //        $translator = new \DeepL\Translator(env('TRANSLATION_API_KEY'));
        //        $result = $translator->translateText($text, $from, $to);
        //        return trim($result->text);

        // Google Translate
        $translate = new TranslateClient([
            'key' => env('TRANSLATION_API_KEY'),
            'target' => $to,
        ]);
        $result = $translate->translate($text);

        return $result['text'];

    }
}

if (! function_exists('translate_field')) {
    /**
     * @throws GoogleException
     * @throws ServiceException
     */
    function translate_field($language, $currentState, $savedState): string
    {
        $str = $currentState ?? $savedState;
        if (is_array($str)) {
            $str = tiptap_converter()->asHTML($str);
        }

        return translate_text($str, $language);
    }
}

if (! function_exists('expand_language_code')) {
    function expand_language_code(?string $code): ?string
    {
        if (is_null($code)) {
            return null;
        }
        $expandMap = [
            'en' => 'en-US',
            'pt' => 'pt-PT',
        ];

        return $expandMap[$code] ?? $code;
    }
}

if (! function_exists('truncate_excerpt')) {
    function truncate_excerpt(string $str, ?int $max = null): ?string
    {
        if (is_null($max)) {
            $max = env('DEFAULT_EXCERPT_LENGTH');
        }
        $str = remove_html($str);

        return Str::words($str, str_word_count(Str::limit($str, $max)), ' ...');
    }
}

if (! function_exists('remove_html')) {
    function remove_html(?string $str): ?string
    {
        return strip_tags(str_replace('>', '> ', $str));
    }
}

if (! function_exists('bool_to_string')) {
    function bool_to_string(bool $bool): string
    {
        return var_export($bool, true);
    }
}

if (! function_exists('get_now_timestamp')) {
    function get_now_timestamp(): string
    {
        return Carbon::now()->format('Y-m-d H:i:s');
    }
}

if (! function_exists('uses_trait')) {
    function uses_trait(string|object $class, string $trait): bool
    {
        return in_array($trait, class_uses_recursive($class));
    }
}

if (! function_exists('restrict_editing')) {
    function restrict_editing(?Model $record): bool
    {
        return
            ! is_null($record) &&
            (
                $record->trashed() ||
                ! Gatekeeper::userCan('update', $record::class) ||
                $record->is_locked ||
                (
                    ! is_null($record->approvalStatus) &&
                    in_array(
                        strtolower($record->approvalStatus->status),
                        [
                            'submitted',
                            'pending',
                            //                            'approved',
                            //                            'discarded',
                        ]
                    )
                )
            );
    }
}

if (! function_exists('encrypt_frontend')) {
    function encrypt_frontend(string $str): string
    {
        return Jwt::encode($str);
    }
}

if (! function_exists('frontend_db_array_cast')) {
    function frontend_db_array_cast(array $arr): string
    {
        return '{'.implode(',', $arr).'}';
    }
}

if (! function_exists('btn_id')) {
    function btn_id(string $str): string
    {
        return 'btn-'.Str::slug($str);
    }
}

if (! function_exists('get_proxied_url')) {
    function get_proxied_url(?string $url, bool $renderJs = false, ?int $timeout = 60): ?string
    {

        if (is_null($url)) {
            return null;
        }

        $timeoutMs = $timeout * 1000;
        return replace_tokens(
            env('WEB_SCRAPER_API_URL'),
            [
                'key' => env('WEB_SCRAPER_API_KEY'),
                'render_js' => $renderJs ? 'true' : 'false',
                'render_js_timeout_ms' => $timeoutMs,
                'total_timeout_ms' => $timeoutMs,
                'trial_timeout_ms' => $timeoutMs,
                'url' => urlencode(trim($url)),
            ]
        );

    }
}

if (! function_exists('parse_all_urls')) {
    function parse_all_urls(?string $str): array
    {
        preg_match_all('#\bhttps?://[^,\s()<>]+(?:\([\w\d]+\)|([^,[:punct:]\s]|/))#', $str, $match);
        return $match[0];
    }
}

if (!function_exists('get_remote_mime_type')) {
    function get_remote_mime_type(string $url): ?string
    {
        try {
            $headers = array_change_key_case(get_headers($url, true, get_ssl_context()), CASE_LOWER);
            return parse_mime_type($headers['content-type'] ?? null);
        } catch (\Throwable $e) {
            return null;
        }
    }
}

if (!function_exists('parse_mime_type')) {
    function parse_mime_type(string|array|null $mime): ?string
    {
        if (!is_null($mime)) {
            if (is_array($mime)) {
                $mime = $mime[count($mime) - 1];
            }
            if (str_contains($mime, ';')) {
                $mime = trim(explode(';', $mime)[0]);
            }
        }
        return $mime;
    }
}

if (!function_exists('get_mime_category')) {
    function get_mime_category(string|array|null $mime): ?string
    {
        $mime = parse_mime_type($mime);
        $mimeParts = explode('/', $mime);
        return $mimeParts[0];
    }
}

if (!function_exists('get_ssl_context')) {
    function get_ssl_context()
    {
        return stream_context_create( [
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
            ],
        ]);
    }
}

if (!function_exists('url_matches')) {
    function url_matches($url, array $patterns, bool $regex = false): bool
    {
        foreach ($patterns as $pattern)
        {
            if (
                ($regex && preg_match($pattern, $url)) ||
                str_contains($url, $pattern)
            ) {
                return true;
            }
        }
        return false;
    }
}

if (!function_exists('is_website_root')) {
    function is_website_root(string $url): bool
    {
        if (str_ends_with($url, '/')) {
            $url = substr($url, 0, -1);
        }
        return ($url === get_website_root($url));
    }
}

if (!function_exists('get_website_root')) {
    function get_website_root(string $url): string
    {
        $parsedUrl = parse_url($url);
        return $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
    }
}

if (!function_exists('get_url_param')) {
    function get_url_param(string $url, string $param): string
    {
        $components = parse_url($url);
        parse_str($components['query'], $params);
        return urldecode($params[$param]);
    }
}

if (!function_exists('send_debug_email'))
{
    function send_debug_email($subject, $body): ?SentMessage
    {
        if (env('SEND_DEBUG_EMAIL')) {
            $env = strtoupper(env('APP_ENV'));
            $subjectPrefix = "[{$env}] DEBUG:";
            return Mail::raw($body, function ($message) use ($subjectPrefix, $subject) {
                $message->to(env('ADMIN_EMAIL'))
                    ->from(env('ADMIN_EMAIL'))
                    ->subject("{$subjectPrefix} {$subject}")
                ;
            });
        }
        return null;
    }
}
