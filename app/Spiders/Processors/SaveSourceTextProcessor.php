<?php

namespace App\Spiders\Processors;

use RoachPHP\ItemPipeline\ItemInterface;
use RoachPHP\ItemPipeline\Processors\ItemProcessorInterface;
use RoachPHP\Support\Configurable;

class SaveSourceTextProcessor implements ItemProcessorInterface
{
    use Configurable;

    public function processItem(ItemInterface $item): ItemInterface
    {
        $content = $item->get('content');
        $model = $item->get('model');
        if (! empty($content)) {
            $model->updateQuietly([
                'text_path' => $model->saveTextFile($content),
                'text_imported_at' => get_now_timestamp(),
            ]);
        }

        return $item;
    }
}
