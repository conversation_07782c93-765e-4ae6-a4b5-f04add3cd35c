<?php

namespace App\Spiders\Processors;

use App\Models\SourceContribution;
use App\Services\Formatter;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use RoachPHP\ItemPipeline\ItemInterface;
use RoachPHP\ItemPipeline\Processors\ItemProcessorInterface;
use RoachPHP\Support\Configurable;

class SaveImportedSourceFieldsProcessor implements ItemProcessorInterface
{
    use Configurable;

    const array AUTHOR_DELIMITERS = ['and', '&', 'with'];

    public function processItem(ItemInterface $item): ItemInterface
    {

        $model = $item->get('model');
        $data = [];

        // Set the referral URL if empty
        if (empty($model->referral_url) && ! empty($model->url)) {
            $data['referral_url'] = $model->url;
        }

        // Set the title if available
        $title = $item->get('title');
        if (! empty($title)) {
            $data['name'] = Str::limit($title, 250);
            $data['slug'] = Str::slug($title);
        }

        // Set the description if available
        $description = $item->get('description');
        if (! empty($description)) {
            $data['description'] = Str::limit($description, 50000);
        }

        // Save the image if available
        $imageUrl = $item->get('image_url');
        if (! empty($imageUrl)) {
            $model->saveImageFromUrl($imageUrl);
        }

        // Save the media if available
        $mediaUrl = $item->get('media_url');
        if (! empty($mediaUrl)) {
            $data['media_url'] = $mediaUrl;
        }

        // Set the published date if available
        $publishedDatetime = $item->get('published_datetime');
        if (! empty($publishedDatetime)) {
            $data['published_on'] = Formatter::date($publishedDatetime);
        }

        // Set the external ID if available
        $externalId = $item->get('external_id');
        if (! empty($externalId)) {
            $data['external_id'] = $externalId;
        }

        // Set the author if available
        $authors = $item->get('author');
        if (! empty($authors)) {

            foreach (static::AUTHOR_DELIMITERS as $delimiter) {
                if (str_contains($authors, $delimiter)) {
                    $authors = explode($delimiter, " {$authors} ");
                    break;
                }
            }

            if (! is_array($authors)) {
                $authors = [$authors];
            }

            foreach ($authors as $author) {
                SourceContribution::firstOrCreateByContributorName($author, $model);
            }

        }

        // Set the language if available
        $language = $item->get('language');
        if (! empty($language)) {
            $data['language'] = $language;
        }

        // Update new values
        Log::debug('UPDATE SOURCE: '.json_encode($data, JSON_UNESCAPED_SLASHES));
        if (! empty($data)) {
            $data['last_imported_at'] = get_now_timestamp();
            $model->updateQuietly($data);
        }

        return $item;

    }
}
