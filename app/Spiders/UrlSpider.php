<?php

namespace App\Spiders;

use App\Models\Source;
use App\Spiders\Processors\SaveImportedSourceFieldsProcessor;
use App\Spiders\Processors\SaveSourceTextProcessor;
use Generator;
use Illuminate\Support\Facades\Log;
use Roach<PERSON><PERSON>\Downloader\Middleware\RequestDeduplicationMiddleware;
use RoachPHP\Http\Response;
use Symfony\Component\DomCrawler\UriResolver;

class UrlSpider extends Spider
{

    public array $downloaderMiddleware = [
        RequestDeduplicationMiddleware::class,
    ];

    public array $itemProcessors = [
        SaveImportedSourceFieldsProcessor::class,
        SaveSourceTextProcessor::class,
    ];

    const array GATEWAY_ERRORS = [
        'gateway timeout',
        'gateway time-out',
        'bad gateway'
    ];

    /**
     * @throws \Exception
     */
    public function parse(Response $response): Generator
    {

        $model = $this->context['model'];
        $override = $this->context['override'];
        $useProxy = $this->context['useProxy'];
        $isJson = json_decode($response->text(), true);
        $responseText = trim(strtolower($response->text()));

        $isGatewayError = false;
        foreach (static::GATEWAY_ERRORS as $error)
        {
            if (str_contains($responseText, $error)) {
                $isGatewayError = true;
                break;
            }
        }
        $isResponseError = (($response->getStatus() < 200) || ($response->getStatus() >= 400));

        // If the response looks like an error ...
        if ($isJson || $isGatewayError || $isResponseError) {

            // If this request wasn't already using a proxy, retry with a proxy
            if (!$useProxy) {
                Log::debug("SOURCE URL IMPORT HTTP ERROR, RETRYING w/ PROXY: {$model->id}");
                $model->importFromUrl(true, true);

            // Otherwise, throw an exception and bail
            } else {
                throw new \Exception("PROXIED SOURCE URL IMPORT FAILED: {$model->id}");
            }

        // Otherwise process it!
        } else {
            yield $this->item([
                'model' => $model,
                'content' => $this->parseContent($response, $model->url_content_selector, $model->url_content_strip),
                'title' => ($override || str_starts_with($model->name, Source::getPrefixedName(''))) ? $this->parseTitle($response, $model) : null,
                'description' => ($override || empty($model->description)) ? $this->parseDescription($response, $model->url_content_selector) : null,
                'image_url' => ($override || empty($model->getOriginal('image_path'))) ? $this->parseImage($response, $model->url_image_selector) : null,
                'author' => ($override || ($model->contributions()->count() == 0)) ? $this->parseAuthor($response, $model->url_author_selector) : null,
                'published_datetime' => ($override || empty($model->published_on)) ? $this->parsePublishDatetime($response, $model->url_published_selector) : null,
                'language' => $this->parseLanguage($response),
            ]);
        }

    }

    protected function parseContent(Response $response, ?string $selector, ?string $strip = null): ?string
    {
        return $this->getFirstAvailableContent(
            $response,
            [
                $selector,
                'article',
                'main',
                'body',
            ],
            $strip
        );
    }

    protected function parseTitle(Response $response, Source $source): ?string
    {

        $title = $this->getFirstAvailableContent(
            $response,
            [
                ['meta[property="og:title"]', 'content'],
                'title',
                'h1',
                'body',
            ]
        );

        if ($source->shouldUseImporterSettings() && ! empty($source->importerCollection->source_title_strip)) {
            $title = str_ireplace($source->importerCollection->source_title_strip, '', $title);
        }

        return html_entity_decode($title);

    }

    protected function parseDescription(Response $response, ?string $selector): ?string
    {
        $description = $this->getFirstAvailableContent(
            $response,
            [
                ['meta[name="og:description"]', 'content'],
                ['meta[name="description"]', 'content'],
            ]
        );
        if (is_null($description)) {
            $description = $this->parseContent($response, $selector);
        }

        return $description;
    }

    protected function parseImage(Response $response, ?string $selector): ?string
    {

        $imageUrl = $this->getFirstAvailableContent(
            $response,
            [
                !empty($selector) ? [$selector, 'src'] : null,
                ['meta[property="og:image"]', 'content'],
                ['meta[name="twitter:image"]', 'content'],
            ]
        );
        if (! empty($imageUrl) && ! str_starts_with($imageUrl, 'http')) {
            $imageUrl = UriResolver::resolve($imageUrl, $response->getBaseHref());
        }

        //  Do a final check to validate that this is really an image; set it to null if not
        if (!is_null($imageUrl)) {
            $imageMime = get_remote_mime_type($imageUrl);
            if (!is_null($imageMime) && !str_starts_with($imageMime, 'image')) {
                $imageUrl = null;
            }
        }

        return $imageUrl;

    }

    protected function parseAuthor(Response $response, ?string $selector): ?string
    {
        return $this->getFirstAvailableContent(
            $response,
            [
                $selector,
                ['meta[name="author"]', 'content'],
            ]
        );
    }

    protected function parsePublishDatetime(Response $response, ?string $selector): ?string
    {
        return $this->getFirstAvailableContent(
            $response,
            [
                $selector,
                ['meta[property="article:published_time"]', 'content'],
                ['meta[property="article:modified_time"]', 'content'],
                ['time[datetime]', 'datetime'],
            ]
        );
    }

    protected function getFirstAvailableContent(Response $response, array $selectors, ?string $strip = null): ?string
    {
        $content = null;
        foreach ($selectors as $selector) {
            if (! empty($selector)) {
                $attr = null;
                if (is_array($selector)) {
                    $attr = $selector[1];
                    $selector = $selector[0];
                }
                $content = $this->getSingleElementContents(
                    $response,
                    $selector,
                    $attr,
                    $strip
                );
                if (! empty($content)) {
                    break;
                }
            }
        }

        return $content;
    }
}
