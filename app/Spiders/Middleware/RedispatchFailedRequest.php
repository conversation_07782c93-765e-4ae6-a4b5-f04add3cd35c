<?php

namespace App\Spiders\Middleware;

use Illuminate\Support\Facades\Log;
use RoachPHP\Downloader\Middleware\ResponseMiddlewareInterface;
use RoachPHP\Http\Response;
use RoachPHP\Support\Configurable;

class RedispatchFailedRequest implements ResponseMiddlewareInterface
{
    use Configurable;

    public function handleResponse(Response $response): Response
    {
//        Log::debug($response->getStatus());
        return $response;
    }
}
