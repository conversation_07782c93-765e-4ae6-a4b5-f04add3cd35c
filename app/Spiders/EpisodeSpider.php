<?php

namespace App\Spiders;

use App\Spiders\Processors\SaveImportedSourceFieldsProcessor;
use Exception;
use Generator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use PhanAn\Poddle\Poddle;
use RoachP<PERSON>\Downloader\Middleware\RequestDeduplicationMiddleware;
use RoachPHP\Extensions\LoggerExtension;
use RoachPHP\Extensions\StatsCollectorExtension;
use RoachPHP\Http\Response;

class EpisodeSpider extends Spider
{
    const FEED_URL_REGEX = '/[\w.~:\/?#\[\]@!$&()*+,;=%-]*\/\/[^\/]+\/?.*\/.*\.xml[\w.~:\/?#\[\]@!$&()*+,;=%\-]*/';

    public array $downloaderMiddleware = [
        RequestDeduplicationMiddleware::class,
    ];

    public array $spiderMiddleware = [
        //
    ];

    public array $itemProcessors = [
        SaveImportedSourceFieldsProcessor::class,
    ];

    public array $extensions = [
        LoggerExtension::class,
        StatsCollectorExtension::class,
    ];

    public int $concurrency = 1;

    public int $requestDelay = 0;

    protected Model $model;

    public function parse(Response $response): Generator
    {

        $this->model = $this->context['model'];
        $data = [];
        $urlsTried = [];

        // First try to find XML feeds in links
        $links = $response->filter('a')->links();
        foreach ($links as $link) {
            $linkUrl = $link->getUri();
            if (preg_match(static::FEED_URL_REGEX, $linkUrl)) {
                Log::debug("Match: {$linkUrl}");
                $data = $this->getEpisodeData($linkUrl);
                if (! empty($data)) {
                    break;
                }
            }
            $urlsTried[] = $linkUrl;
        }

        // If that didn't work, try to find XML feeds via regex
        if (empty($data)) {
            preg_match_all(static::FEED_URL_REGEX, $response->getBody(), $matches);
            foreach ($matches as $match) {
                if (!empty($match)) {
                    $url = $match[0];
                    if (! in_array($url, $urlsTried)) {
                        Log::debug("Match: {$url}");
                        $data = $this->getEpisodeData($url);
                        if (! empty($data)) {
                            break;
                        }
                    }
                    $urlsTried[] = $url;
                }
            }
        }

        yield $this->item($data);

    }

    protected function getEpisodeData($feedUrl): array
    {

        Log::debug("Found Feed: {$feedUrl}");
        $data = [];

        try {

            $feed = Poddle::fromUrl($feedUrl);
            $channel = $feed->getChannel();
            $episodes = $feed->getEpisodes();

            foreach ($episodes as $episode) {
                if ($episode->metadata->link == $this->model->url) {

                    Log::debug('Matched Episode: '.json_encode($episode, JSON_UNESCAPED_SLASHES));
                    $guid = $episode->guid->value;
                    $description = ! empty($episode->metadata->description) ? convert_html($episode->metadata->description) : convert_html($channel->description);
                    $imageUrl = ! empty($episode->metadata->image) ? $episode->metadata->image : $channel->image;

                    $data = [
                        'model' => $this->model,
                        'description' => empty($this->model->description) ? $description : null,
                        'image_url' => empty($this->model->image_path) ? $imageUrl : null,
                        'media_url' => $episode->enclosure->url,
                        'author' => ($this->model->contributions()->count() == 0) ? $channel->metadata->author : null,
                        'published_datetime' => empty($this->model->published_on) ? $episode->metadata->pubDate->format('Y-m-d') : null,
                        'external_id' => $guid,
                    ];
//                    Log::debug(json_encode($data, JSON_UNESCAPED_SLASHES));

                    break;

                }
            }

        } catch (Exception $e) {
            Log::debug($e->getMessage());
        }

        return $data;

    }
}
