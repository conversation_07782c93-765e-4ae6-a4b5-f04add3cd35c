<?php

namespace App\Policies;

use App\Models\User;
use App\Policies\Traits\CanBeAdministered;
use App\Policies\Traits\CanBeLocked;

class UserPolicy extends Policy
{
    use CanBeAdministered;
    use CanBeLocked;

    /**
     * Determine whether the user can bulk restore.
     */
    public function impersonate(User $user): bool
    {
        return $user->can($this->getAbility('impersonate'));
    }

    /**
     * Determine whether the user can upload files.
     */
    public function uploadFiles(User $user): bool
    {
        return true;
    }
}
