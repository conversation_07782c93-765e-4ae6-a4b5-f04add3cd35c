<?php

namespace App\Policies;

use App\Models\User;
use App\Policies\Traits\CanBeAdministered;
use App\Policies\Traits\CanBeLocked;
use App\Policies\Traits\CanBeModerated;
use App\Policies\Traits\CanBeOrdered;
use App\Policies\Traits\CanBePromoted;
use App\Policies\Traits\CanBeReplicated;

class AgentPolicy extends Policy
{
    use CanBeAdministered;
    use CanBeLocked;
    use CanBeModerated;
    use CanBeOrdered;
    use CanBePromoted;
    use CanBeReplicated;

    /**
     * Determine whether the user can test.
     */
    public function test(User $user): bool
    {
        return $user->can($this->getAbility('test'));
    }
}
