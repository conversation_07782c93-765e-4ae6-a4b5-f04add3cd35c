<?php

namespace App\Policies\Traits;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

trait CanBeAdministered
{
    /**
     * Determine whether the user can administer.
     */
    public function administer(User $user, ?Model $model): bool
    {
        return $user->can($this->getAbility('administer')) &&
        (
            ! uses_trait($this, CanBeLocked::class) ||
            ! $model->is_locked ||
            $user->can($this->getAbility('lock'))
        );
    }
}
