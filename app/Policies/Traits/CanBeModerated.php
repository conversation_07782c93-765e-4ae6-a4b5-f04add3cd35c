<?php

namespace App\Policies\Traits;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

trait CanBeModerated
{
    /**
     * Determine whether the user can moderate.
     */
    public function moderate(User $user, ?Model $model): bool
    {
        return
            $user->can($this->getAbility('moderate')) &&
            (
                ! uses_trait($this, CanBeLocked::class) ||
                ! $model->is_locked ||
                $user->can($this->getAbility('lock'))
            );
    }
}
