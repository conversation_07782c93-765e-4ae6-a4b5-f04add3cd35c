<?php

namespace App\Policies\Traits;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

trait CanBePromoted
{
    /**
     * Determine whether the user can promote.
     */
    public function promote(User $user, ?Model $model): bool
    {
        return
            $user->can($this->getAbility('promote')) &&
            (
                ! uses_trait($this, CanBeLocked::class) ||
                ! $model->is_locked ||
                $user->can($this->getAbility('lock'))
            );
    }
}
