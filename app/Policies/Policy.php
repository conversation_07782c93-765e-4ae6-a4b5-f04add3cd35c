<?php

namespace App\Policies;

use App\Models\User;
use App\Policies\Traits\CanBeLocked;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Database\Eloquent\Model;

class Policy
{
    use HandlesAuthorization;

    protected ?string $resource = null;

    public function __construct()
    {
        if (is_null($this->resource)) {
            $this->resource = str_replace('policy', '', strtolower(class_basename(static::class)));
        }
    }

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can($this->getAbility('view_any'));
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ?Model $model): bool
    {
        return $user->can($this->getAbility('view'));
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can($this->getAbility('create'));
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ?Model $model): bool
    {
        return
            $user->can($this->getAbility('update'));
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ?Model $model): bool
    {
        return
            $user->can($this->getAbility('delete')) &&
            (
                ! uses_trait($this, CanBeLocked::class) ||
                ! $model->is_locked ||
                $user->can($this->getAbility('lock'))
            );
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can($this->getAbility('delete_any'));
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, ?Model $model): bool
    {
        return
            $user->can($this->getAbility('force_delete')) &&
            (
                ! uses_trait($this, CanBeLocked::class) ||
                ! $model->is_locked ||
                $user->can($this->getAbility('lock'))
            );
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can($this->getAbility('force_delete_any'));
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, ?Model $model): bool
    {
        return
            $user->can($this->getAbility('restore')) &&
            (
                ! uses_trait($this, CanBeLocked::class) ||
                ! $model->is_locked ||
                $user->can($this->getAbility('lock'))
            );
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can($this->getAbility('restore_any'));
    }

    protected function getAbility(string $ability): string
    {
        return implode('_', [$ability, $this->resource]);
    }
}
