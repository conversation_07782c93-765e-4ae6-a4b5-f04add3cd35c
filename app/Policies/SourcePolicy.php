<?php

namespace App\Policies;

use App\Models\User;
use App\Policies\Traits\CanBeAdministered;
use App\Policies\Traits\CanBeLocked;
use App\Policies\Traits\CanBeModerated;
use App\Policies\Traits\CanBePromoted;

class SourcePolicy extends Policy
{
    use CanBeAdministered;
    use CanBeLocked;
    use CanBeModerated;
    use CanBePromoted;

    /**
     * Determine whether the user can categorize.
     */
    public function categorize(User $user): bool
    {
        return $user->can($this->getAbility('categorize'));
    }

    /**
     * Determine whether the user can categorize unlimited.
     */
    public function categorizeUnlimited(User $user): bool
    {
        return $user->can($this->getAbility('categorize_unlimited'));
    }
}
