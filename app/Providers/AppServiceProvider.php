<?php

namespace App\Providers;

use Altek\Accountant\Models\Ledger;
use App\Http\Responses\LoginResponse;
use App\Http\Responses\LogoutResponse;
use App\Models\Team;
use App\Models\User;
use App\Routing\RemovableRoutesMixin;
use Filament\Facades\Filament;
use Filament\Http\Responses\Auth\Contracts\LoginResponse as LoginResponseContract;
use Filament\Http\Responses\Auth\Contracts\LogoutResponse as LogoutResponseContract;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;
use Laravel\Cashier\Cashier;
use Laravel\Pennant\Feature;
use Opcodes\Spike\Facades\Credits;
use Opcodes\Spike\Facades\Spike;
use RickDBCN\FilamentEmail\Models\Email;
use RingleSoft\LaravelProcessApproval\Models\ProcessApproval;
use RingleSoft\LaravelProcessApproval\Models\ProcessApprovalStatus;
use Spatie\Permission\Models\Role;

class AppServiceProvider extends ServiceProvider
{
    /**
     * The path to your application's "home" route.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/home';

    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(LogoutResponseContract::class, LogoutResponse::class);
        $this->app->bind(LoginResponseContract::class, LoginResponse::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {

        Feature::discover();
        Feature::resolveScopeUsing(fn ($driver) => get_current_team());

        Ledger::resolveRelationUsing(
            'owner',
            function ($model) {
                return $model->belongsTo(User::class, 'user_id')->withTrashed();
            }
        );

        Email::resolveRelationUsing(
            'owner',
            function ($model) {
                return $model->belongsTo(User::class, 'user_id')->withTrashed();
            }
        );
        Email::creating(function (Email $email) {
            $user = User::where('email', $email->to)->first();
            if ($user) {
                $email->user_id = $user->id;
            }
        });

        Role::resolveRelationUsing(
            'team',
            function ($model) {
                return $model->belongsTo(Team::class)->withTrashed();
            }
        );

        ProcessApproval::resolveRelationUsing(
            'approvable',
            function ($model) {
                return $model->morphTo(__FUNCTION__, 'approvable_type', 'approvable_id');
            }
        );

        // Fix issue with approval status not saving the creator_id under certain circumstances
        ProcessApprovalStatus::creating(function (ProcessApprovalStatus $approvalStatus) {
            if (empty($approvalStatus->creator_id)) {
                $approvalStatus->creator_id = auth()->check() ? auth()->user()->id : $approvalStatus->approvable->user_id;
            }
        });

        // Billing
        Cashier::useCustomerModel(Team::class);
        Cashier::calculateTaxes();
        Spike::billable(Team::class)->resolve(function ($request) {
            return $request->user()?->currentTeam;
        });
        Spike::billable(Team::class)->authorize(function (Team $billable, $request) {
            return $request->user()?->ownsTeam($billable);
        });
        Spike::resolveAvatarUsing(function (Team $billable) {
            return $billable->getFilamentAvatarUrl();
        });
        Credits::allowNegativeBalance(function($creditType, ?Team $billable) {
            return !$billable?->is_prepaid_only;
        });

        $this->bootRoute();

    }

    public function bootRoute(): void
    {

        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });

        Route::mixin(new RemovableRoutesMixin);

    }
}
