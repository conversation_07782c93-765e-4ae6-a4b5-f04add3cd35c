<?php

namespace App\Providers;

use App\Models\User;
use App\Services\Gatekeeper;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Laravel\Horizon\Horizon;
use Laravel\Horizon\HorizonApplicationServiceProvider;

class HorizonServiceProvider extends HorizonApplicationServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        parent::boot();

        // Horizon::routeSmsNotificationsTo('15556667777');
        Horizon::routeMailNotificationsTo(env('ADMIN_EMAIL'));
        // Horizon::routeSlackNotificationsTo('slack-webhook-url', '#channel');
    }

    protected function gate(): void
    {
        Gate::define('viewHorizon', function (User $user) {
            return Gatekeeper::isSuperadmin() && app()->isLocal();
        });
    }

    protected function authorization(): void
    {
        Auth::setDefaultDriver(config('filament.auth.guard', 'web'));
        parent::authorization();
    }
}
