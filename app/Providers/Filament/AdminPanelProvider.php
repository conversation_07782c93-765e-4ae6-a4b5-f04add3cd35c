<?php

namespace App\Providers\Filament;

use App\Filament\Pages\Admin\Dashboard;
use App\Filament\Pages\Admin\ListAuthenticationLogs;
use App\Filament\Resources\AgentIntegrationPlatformResource;
use App\Filament\Resources\AgentModelProviderResource;
use App\Filament\Resources\AgentModelResource;
use App\Filament\Resources\AgentResource;
use App\Filament\Resources\CategoryResource;
use App\Filament\Resources\ClassificationResource;
use App\Filament\Resources\CollectionResource;
use App\Filament\Resources\ContributorResource;
use App\Filament\Resources\DiscussionResource;
use App\Filament\Resources\OrganizationResource;
use App\Filament\Resources\SourceResource;
use App\Filament\Resources\TeamResource;
use App\Filament\Resources\UserResource;
use App\Models\Agent;
use App\Models\Organization;
use App\Services\Gatekeeper;
use BezhanSalleh\FilamentShield\FilamentShieldPlugin;
use EightyNine\Approvals\ApprovalPlugin;
use Exception;
use Filament\Panel;
use Filament\SpatieLaravelTranslatablePlugin;
use Filament\View\PanelsRenderHook;
use Leandrocfe\FilamentApexCharts\FilamentApexChartsPlugin;
use Maartenpaauw\Filament\Pennant\FilamentPennantPlugin;
use RalphJSmit\Filament\RecordFinder\FilamentRecordFinder;
use RickDBCN\FilamentEmail\FilamentEmail;
use Rupadana\ApiService\Resources\TokenResource;
use ShuvroRoy\FilamentSpatieLaravelBackup\FilamentSpatieLaravelBackupPlugin;
use ShuvroRoy\FilamentSpatieLaravelHealth\FilamentSpatieLaravelHealthPlugin;
use Spatie\Health\Checks\Checks\DebugModeCheck;
use Spatie\Health\Checks\Checks\EnvironmentCheck;
use Spatie\Health\Checks\Checks\OptimizedAppCheck;
use Spatie\Health\Facades\Health;
use Stephenjude\FilamentDebugger\DebuggerPlugin;
use Tapp\FilamentAuthenticationLog\FilamentAuthenticationLogPlugin;

class AdminPanelProvider extends PanelProvider
{
    public function boot(): void
    {
        parent::boot();
        Health::checks([
            OptimizedAppCheck::new(),
            DebugModeCheck::new(),
            EnvironmentCheck::new(),
        ]);
    }

    /**
     * @throws Exception
     */
    public function panel(Panel $panel): Panel
    {
        $panel = $this->apgUi($panel);
        $panel = $this->apgMiddleware($panel);

        return $panel
            ->id(env('APP_SUBDOMAIN_ADMIN', 'admin'))
            ->domain(app_domain(env('APP_SUBDOMAIN_ADMIN', 'admin')))
            ->path('')
            ->globalSearch(false)
            ->resources([
                'agent' => AgentResource::class,
                'category' => CategoryResource::class,
                'classification' => ClassificationResource::class,
                'agent_model_providers' => AgentModelProviderResource::class,
                'agent_models' => AgentModelResource::class,
                'agent_integration_platforms' => AgentIntegrationPlatformResource::class,
                'collection' => CollectionResource::class,
                'contributor' => ContributorResource::class,
                'source' => SourceResource::class,
                'organization' => OrganizationResource::class,
                'discussion' => DiscussionResource::class,
                'team' => TeamResource::class,
                'user' => UserResource::class,
                'token' => TokenResource::class,
            ])
            ->brandLogo(secure_asset('img/apologist-light.svg'))
            ->darkModeBrandLogo(secure_asset('img/apologist-dark.svg'))
            ->brandLogoHeight('2rem')
            ->discoverPages(in: app_path('Filament/Pages/Admin'), for: 'App\\Filament\\Pages\\Admin')
            ->pages([
                Dashboard::class,
                ListAuthenticationLogs::class,
            ])
//            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                //                PendingSourcesTable::class,
                //                PendingContributorsTable::class,
                //                PendingCollectionsTable::class,
                //                RecentHistoryTable::class,
            ])
            ->renderHook(
                PanelsRenderHook::PAGE_HEADER_ACTIONS_BEFORE,
                fn (array $scopes) => view('components.app.approval-status', ['scopes' => $scopes]),
                scopes: $this->getListingScopes(),
            )
            ->renderHook(
                PanelsRenderHook::PAGE_HEADER_WIDGETS_AFTER,
                fn (array $scopes) => view('components.app.notices.rejection-comment', ['scopes' => $scopes]),
                scopes: $this->getListingScopes(),
            )
            ->renderHook(
                PanelsRenderHook::PAGE_FOOTER_WIDGETS_BEFORE,
                fn (array $scopes) => view(
                    'components.listing-stats',
                    [
                        'scopes' => $scopes,
                        'dashboard' => false,
                        'model' => Organization::class,
                        'title' => 'Organization Listing Stats',
                    ]
                ),
                scopes: $this->getOrganizationListingScopes(),
            )
            ->renderHook(
                PanelsRenderHook::PAGE_FOOTER_WIDGETS_BEFORE,
                fn (array $scopes) => view(
                    'components.agent-usage',
                    [
                        'scopes' => $scopes,
                        'list' => in_array('App\Filament\Resources\AgentResource\Pages\ListRecords', $scopes),
                        'title' => 'Agent Usage',
                    ]
                ),
                scopes: $this->getAgentUsageScopes(),
            )
//            ->renderHook(
//                PanelsRenderHook::PAGE_FOOTER_WIDGETS_BEFORE,
//                fn (array $scopes) => view(
//                    'components.listing-stats',
//                    [
//                        'scopes' => $scopes,
//                        'dashboard' => false,
//                        'model' => Agent::class,
//                        'title' => 'Agent Listing Stats',
//                    ]
//                ),
//                scopes: $this->getAgentListingScopes(),
//            )
            ->plugins($this->getPlugins());
    }

    protected function getPlugins(): array
    {
        $plugins = [
            $this->getEnvironmentIndicatorPlugin(),
            FilamentShieldPlugin::make()
                ->gridColumns([
                    'default' => 1,
                    'sm' => 2,
                ])
                ->sectionColumnSpan(1)
                ->checkboxListColumns([
                    'default' => 1,
                    'sm' => 2,
                    'lg' => 3,
                ])
                ->resourceCheckboxListColumns([
                    'default' => 1,
                    'sm' => 2,
                ]),
            FilamentApexChartsPlugin::make(),
            FilamentAuthenticationLogPlugin::make(),
            new FilamentEmail,
            SpatieLaravelTranslatablePlugin::make()
                ->defaultLocales(array_keys(config('agent.languages'))),
            FilamentRecordFinder::make(),
            FilamentPennantPlugin::make()
                ->withoutResource(),
        ];

        //        if (Gatekeeper::isSuperadmin()) {
        $plugins = array_merge(
            $plugins,
            [
                DebuggerPlugin::make(),
                ApprovalPlugin::make(),
                FilamentSpatieLaravelBackupPlugin::make(),
                FilamentSpatieLaravelHealthPlugin::make(),
            ]
        );
        //        }

        return $plugins;

    }

    /**
     * @return array[]
     */
    protected function getNav(): array
    {

        // Dashboard
        $nav = [
            'default' => [
                'Dashboard' => [
                    'icon' => 'chart-bar-square',
                    'route' => 'filament.admin.pages.dashboard',
                ],
            ],
        ];

        // user / team / org management
        $nav['Administration'] = [
            UserResource::class,
            TeamResource::class,
            OrganizationResource::class,
            AgentResource::class,
            TokenResource::class,
        ];

        // Superadmin only
        if (Gatekeeper::isSuperadmin()) {
            $nav['Monitoring & Backups'] = [
                'Telescope ↗' => [
                    'icon' => 'code-bracket-square',
                    'route' => 'telescope',
                    'params' => ['view' => 'requests'],
                    'tab' => true,
                ],
                'Horizon ↗' => [
                    'icon' => 'queue-list',
                    'route' => 'horizon.index',
                    'params' => ['view' => 'dashboard'],
                    'tab' => true,
                ],
                'App Health' => [
                    'icon' => 'heart',
                    'route' => 'filament.admin.pages.health-check-results',
                ],
                'Authentication Logs' => [
                    'icon' => 'finger-print',
                    'route' => 'filament.admin.pages.list-authentication-logs',
                ],
                'Email Logs' => [
                    'icon' => 'envelope',
                    'route' => 'filament.admin.resources.emails.index',
                ],
                'Backups' => [
                    'icon' => 'arrow-down-on-square-stack',
                    'route' => 'filament.admin.pages.backups',
                ],
            ];
        }

        // Configuration

        if (Gatekeeper::isSuperadmin()) {
            $nav['Configuration'] = [
                CategoryResource::class,
                ClassificationResource::class,
                AgentModelProviderResource::class,
                AgentModelResource::class,
                AgentIntegrationPlatformResource::class,
                'Roles' => [
                    'icon' => 'shield-check',
                    'route' => 'filament.admin.resources.shield.roles.index',
                ],
                'Approval Flows' => [
                    'icon' => 'document-check',
                    'route' => 'filament.admin.resources.approval-flows.index',
                ],
            ];
        }

        return $nav;

    }
}
