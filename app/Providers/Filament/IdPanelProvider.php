<?php

namespace App\Providers\Filament;

use App\Filament\Pages\Id\Dashboard;
use App\Filament\Pages\Id\Login;
use <PERSON>zhanSalleh\FilamentShield\FilamentShieldPlugin;
use Exception;
use Filament\Panel;
use Filament\View\PanelsRenderHook;
use Stephenjude\FilamentDebugger\DebuggerPlugin;
use Tapp\FilamentAuthenticationLog\FilamentAuthenticationLogPlugin;

class IdPanelProvider extends PanelProvider
{
    /**
     * @throws Exception
     */
    public function panel(Panel $panel): Panel
    {
        $panel = $this->apgUi($panel);
        $panel = $this->apgMiddleware($panel);

        return $panel
            ->default()
            ->id(env('APP_SUBDOMAIN_IDENTITY', 'id'))
            ->domain(app_domain(env('APP_SUBDOMAIN_IDENTITY', 'id')))
            ->path('')
            ->login(Login::class)
            ->registration()
            ->passwordReset()
            ->emailVerification()
            ->brandLogo(secure_asset('img/apologist-light.svg'))
            ->darkModeBrandLogo(secure_asset('img/apologist-dark.svg'))
            ->brandLogoHeight('2rem')
            ->globalSearch(false)
            ->pages([
                Dashboard::class,
            ])
            ->navigation(false)
            ->plugins($this->getPlugins())
            ->renderHook(PanelsRenderHook::AUTH_LOGIN_FORM_BEFORE, fn () => view('components.id.auth-invitation'))
            ->renderHook(PanelsRenderHook::AUTH_REGISTER_FORM_BEFORE, fn () => view('components.id.auth-invitation'));
    }

    protected function getPlugins(): array
    {

        return [
            $this->getAuthPlugin(),
            $this->getEnvironmentIndicatorPlugin(),
            //            SocialmentPlugin::make(),
            //            FilamentShieldPlugin::make(),
            DebuggerPlugin::make(),
            FilamentAuthenticationLogPlugin::make(),
            FilamentShieldPlugin::make(),
        ];

    }
}
