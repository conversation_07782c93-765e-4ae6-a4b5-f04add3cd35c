<?php

namespace App\Providers\Filament;

use App\Filament\Plugins\ApiServicePlugin;
use App\Http\Middleware\ExtendAccessToken;
use Exception;
use Filament\Panel;

class ApiV1PanelProvider extends PanelProvider
{
    public function boot(): void {}

    /**
     * @throws Exception
     */
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id(env('APP_API_VERSION', 'api'))
            ->domain(app_domain(env('APP_SUBDOMAIN_API', 'api')))
            ->authGuard('api')
            ->resources([
                'category' => \App\Filament\Resources\CategoryResource::class,
                'collection' => \App\Filament\Resources\CollectionResource::class,
                'contributor' => \App\Filament\Resources\ContributorResource::class,
                'source' => \App\Filament\Resources\SourceResource::class,
                'contribution' => \App\Filament\Resources\SourceContributionResource::class,
                'organization' => \App\Filament\Resources\OrganizationResource::class,
                'discussion' => \App\Filament\Resources\DiscussionResource::class,
                'model' => \App\Filament\Resources\AgentModelResource::class,
            ])
            ->navigation(false)
            ->plugins([
                ApiServicePlugin::make()
                    ->middleware([
                        ExtendAccessToken::class,
                    ]),
            ]);
    }
}
