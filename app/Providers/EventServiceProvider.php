<?php

namespace App\Providers;

use Altek\Accountant\Events\Recorded;
use Altek\Accountant\Events\Recording;
use App\Events\CollectionSavedEvent;
use App\Events\SourceSavedEvent;
use App\Events\TeamCreatedEvent;
use App\Events\UserCreatedEvent;
use App\Listeners\BackupSubscriber;
use App\Listeners\CollectionSavedListener;
use App\Listeners\LedgerRecordedListener;
use App\Listeners\LedgerRecordingListener;
use App\Listeners\ModelApprovedListener;
use App\Listeners\ModelRejectedListener;
use App\Listeners\ModelSubmittedListener;
use App\Listeners\SourceSavedListener;
use App\Listeners\TeamSetupListener;
use App\Listeners\UserSubscribeListListener;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use RingleSoft\LaravelProcessApproval\Events\ProcessApprovedEvent;
use RingleSoft\LaravelProcessApproval\Events\ProcessRejectedEvent;
use RingleSoft\LaravelProcessApproval\Events\ProcessSubmittedEvent;
use SocialiteProviders\Apple\AppleExtendSocialite;
use SocialiteProviders\Facebook\FacebookExtendSocialite;
use SocialiteProviders\Google\GoogleExtendSocialite;
use SocialiteProviders\LinkedIn\LinkedInExtendSocialite;
use SocialiteProviders\Manager\SocialiteWasCalled;
use SocialiteProviders\Microsoft\MicrosoftExtendSocialite;
use SocialiteProviders\Twitter\TwitterExtendSocialite;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The subscriber classes to register.
     *
     * @var array
     */
    protected $subscribe = [
        BackupSubscriber::class,
    ];

    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [

        Registered::class => [
            SendEmailVerificationNotification::class,
        ],

        // Ledgers
        Recording::class => [
            LedgerRecordingListener::class,
        ],
        Recorded::class => [
            LedgerRecordedListener::class,
        ],

        // Socialite
        SocialiteWasCalled::class => [
            AppleExtendSocialite::class.'@handle',
            GoogleExtendSocialite::class.'@handle',
            MicrosoftExtendSocialite::class.'@handle',
            FacebookExtendSocialite::class.'@handle',
            LinkedInExtendSocialite::class.'@handle',
            TwitterExtendSocialite::class.'@handle',
        ],

        // Various automations around Sources
        SourceSavedEvent::class => [
            SourceSavedListener::class,
        ],

        // Kick off scrapers when Collections are saved
        CollectionSavedEvent::class => [
            CollectionSavedListener::class,
        ],

        // Team setup
        TeamCreatedEvent::class => [
            TeamSetupListener::class,
        ],

        // User setup
        UserCreatedEvent::class => [
            UserSubscribeListListener::class,
        ],

        // Model approval lifecycle
        ProcessSubmittedEvent::class => [
            ModelSubmittedListener::class,
        ],
        ProcessApprovedEvent::class => [
            ModelApprovedListener::class,
        ],
        ProcessRejectedEvent::class => [
            ModelRejectedListener::class,
        ],

    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
