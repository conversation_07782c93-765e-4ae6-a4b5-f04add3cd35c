<?php

namespace App\Models\Traits;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

trait SyncsToFrontend
{

    public function syncToFrontend(): void
    {
        $this->pushFieldsToFrontend();
        $this->pruneDeletedFromFrontend();
    }

    protected function pushFieldsToFrontend(array $fields = []): void
    {

        if (empty($fields)) {
            $fields = $this->getFillable();
        }

        $values = array_merge(
            ['id' => $this->id, 'synced_at' => DB::raw('NOW()')],
            Arr::only(
                Arr::map(
                    $this->toArray(),
                    function ($value, $key) {
                        if (is_array($value)) {
                            return json_encode($value);
                        } else if ($this->isEncryptedCastable($key)) {
                            return $value ? encrypt_frontend($value) : null;
                        } else {
                            return $value;
                        }
                    }
                ),
                $fields
            )
        );

        DB::connection(env('FRONTEND_DB_CONNECTION'))->table($this->getTable())->upsert(
            $values,
            ['id'],
            array_merge(['synced_at'], $fields)
        );

    }

    protected function pruneDeletedFromFrontend(): void
    {
        if ($this->trashed()) {
            DB::connection(env('FRONTEND_DB_CONNECTION'))->table($this->getTable())->delete($this->id);
        }
    }
}
