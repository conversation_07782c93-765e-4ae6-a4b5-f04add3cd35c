<?php

namespace App\Models\Traits;

use App\Models\Membership;
use App\Models\Team;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait BelongsToTeam
{
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class)->withTrashed();
    }

    public function scopeTeam($query, int $teamId = null)
    {
        return $query->where('team_id', $teamId ?? get_current_team_id());
    }

    public function setDefaultTeam(): void
    {
        if (
            $this->isFillable('team_id') &&
            (static::class != Membership::class) &&
            $this->isClean('team_id')
        ) {
            $this->team_id = get_current_team_id();
        }
    }

}
