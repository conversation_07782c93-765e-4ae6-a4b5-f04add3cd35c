<?php

namespace App\Models\Traits;

use App\Jobs\ManageListingSearchDocumentJob;
use App\Models\FrontendListingImpression;
use App\Models\FrontendListingReferral;
use App\Models\FrontendListingUrl;
use App\Models\FrontendListingView;
use App\Services\VectaraClient;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Psr\Http\Message\ResponseInterface;

trait HasFrontendListing
{
    public function invalidateFrontendCache(): void
    {

        try {

            $permalink = $this->getFrontendListingUrl();

            $client = new Client(['defaults' => [
                'verify' => false,
            ]]);
            $response = $client->head(
                $permalink,
                [
                    'headers' => [
                        'x-prerender-revalidate' => env('ISR_BYPASS_TOKEN'),
                    ],
                ]
            );
            $headers = $response->getHeaders();

            if (
                isset($headers[env('ISR_CACHE_HEADER')]) &&
                is_array($headers[env('ISR_CACHE_HEADER')]) &&
                in_array(env('ISR_CACHE_VALUE'), $headers[env('ISR_CACHE_HEADER')])
            ) {
                Log::debug("Cache invalidation succeeded: {$permalink}");
            } else {
                Log::debug("Cache invalidation FAILED: {$permalink}");
            }

        } catch (Exception $e) {
            Log::error("Request for permalink FAILED: {$permalink}");
            Log::error($e->getMessage());
        }

    }

    public function getFrontendListingUrl(): ?string
    {

        $url = replace_tokens(
            env('ISR_PERMALINK_URL'),
            [
                'model' => strtolower(class_basename($this)),
                'type' => $this->type ? $this->type->value : '',
                'slug' => $this->slug ? $this->slug : '',
            ]
        );
//        Log::debug($url);

        try {

            $response = file_get_contents($url);
            $json = json_decode($response, true);

            return $json['url'];

        } catch (Exception $e) {
            Log::error("Request for permalink FAILED: {$url}");
            Log::error($e->getMessage());
        }

        return null;

    }

    /**
     * @returns MorphOne
     */
    public function frontendListingUrl(): MorphOne
    {
        return $this->morphOne(FrontendListingUrl::class, 'listable');
    }

    /**
     * @throws Exception
     */
    public function syncFrontendListingUrl(): mixed
    {
        if ($url = $this->getFrontendListingUrl()) {
            return FrontendListingUrl::updateOrCreate(
                [
                    'listable_type' => static::class,
                    'listable_id' => $this->id,
                ],
                ['url' => $url]
            );
        }

        return null;
    }

    public function getSearchDocumentId(): string
    {
        return implode('.', [$this->getSearchModel(), strval($this->id)]);
    }

    public function getSearchModel(): string
    {
        return strtolower(class_basename($this));
    }

    public function shouldIndexSearch(): bool
    {
        return !$this->trashed() && $this->social_active;
    }

    /**
     * @throws GuzzleException
     */
    public function indexSearchDocument(?VectaraClient $client = null): ResponseInterface
    {

        $client = $client ?? get_search_client();
        $bodyParts = [$this->name, $this->description];

        $model = strtolower(class_basename($this));
        $categories = $this->categories()->pluck('categories.name', 'categories.id')->toArray();
        $imageUrl = null;
        if (($model == 'contributor') && ! empty($this->avatar_path)) {
            $imageUrl = get_path_url($this->avatar_path);
        } elseif (! empty($this->image_path)) {
            $imageUrl = get_path_url($this->image_path);
        }

        $meta = [

            // Summary
            'title' => $this->name,
            'model' => $model,
            'type' => $this->type,
            'url' => $this->url,
            'image_url' => $imageUrl,
            'referral_url' => $this->referral_url,
            'social_active' => $this->social_active,
            'listing_url' => $this->frontendListingUrl?->url,
            'is_locked' => $this->is_locked,
            'is_approved' => $this->is_approved,
            'language' => $this->language,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'user_id' => $this->user_id,
            'team_id' => $this->team_id,
            'external_id' => $this->external_id,

            // Taxonomy
            'category_ids' => array_keys($categories),
            'category_names' => array_values($categories),

            // Stats
            'rating' => floatval($this->rating),
            'num_views' => $this->getFrontendStat(FrontendListingView::class, $model, $this->id),
            'num_impressions' => $this->getFrontendStat(FrontendListingImpression::class, $model, $this->id),
            'num_referrals' => $this->getFrontendStat(FrontendListingReferral::class, $model, $this->id),

        ];

        // Organization
        if ($model != 'organization') {
            $organization = $this->team->organization;
            if ($organization) {
                $meta = array_merge(
                    $meta,
                    [
                        'organization_id' => $organization->id,
                        'organization_name' => $organization->name,
                        'organization_url' => $organization->frontendListingUrl?->url,
                    ]
                );
                $bodyParts[] = $organization->name;
            }

        }

        // Source
        if ($model == 'source') {

            $authors = $this->getIndirectRelations(
                $this->contributions()->where('role', 'author'),
                'contributor'
            );
            $contributors = $this->getIndirectRelations(
                $this->contributions(),
                'contributor'
            );
            $collections = $this->collections()->pluck('collections.name', 'collections.id')->toArray();
            $tags = $this->tags()->pluck('tags.name', 'tags.id')->toArray();

            $contributorNames = array_values($contributors);
            $authorNames = array_values($authors);
            $collectionNames = array_values($collections);
            $categoryNames = array_values($categories);
            $tagNames = array_values($tags);

            $meta = array_merge(
                $meta,
                [
                    'subtitle' => $this->subtitle,
                    'published_on' => $this->published_on,
                    'contributor_ids' => array_keys($contributors),
                    'contributor_names' => $contributorNames,
                    'author_ids' => array_keys($authors),
                    'author_names' => $authorNames,
                    'collection_ids' => array_keys($collections),
                    'collection_names' => $collectionNames,
                    'importer_collection_id' => $this->importer_collection_id,
                    'importer_collection_name' => $this->importerCollection?->name,
                    'importer_collection_url' => $this->importerCollection?->referral_url ?? $this->importerCollection?->url,
                    'category_ids' => array_keys($categories),
                    'category_names' => $categoryNames,
                    'tag_ids' => array_keys($tags),
                    'tag_names' => $tagNames,
                    'weight' => $this->weight,
                    'agent_active' => $this->agent_active,
                    'media_url' => $this->media_url,
                ]
            );

            if (!empty($this->text_path)) {
                $bodyParts[] = Storage::disk('public')->get($this->text_path);
            }

            array_push(
                $bodyParts,
                $this->subtitle,
                implode(', ', $tagNames),
                implode(', ', $contributorNames),
                implode(', ', $collectionNames),
                implode(', ', $categoryNames),
            );

        }

        $body = implode("\n---\n", array_filter(array_map('trim', $bodyParts), fn ($x) => !empty($x)));
//        Log::debug($body);
        $res = $client->indexDocument($this->getSearchDocumentId(), $meta, remove_html($body));

        // If it was successful, update the agent_indexed_at timestamp
        if (
            ($res->getStatusCode() == 409) ||
            (
                ($res->getStatusCode() >= 200) &&
                ($res->getStatusCode() < 300)
            )
        ) {
            $this->updateTimestamp('social_indexed_at');


        // If it was rate limited, try again in 3s
        } else if ($res->getStatusCode() == 429) {
            dispatch(new ManageListingSearchDocumentJob($this))->delay(now()->addSeconds(3));

        // Otherwise send a debug email
        } else {
            send_debug_email(
                "Search Index Failed: {$this->name} [{$this->id}]",
                "{$res->getStatusCode()}: {$res->getBody()->getContents()}"
            );
        }

        return $res;

    }

    /**
     * @throws GuzzleException
     */
    public function deleteSearchDocument(?VectaraClient $client = null, bool $unindex = false): ResponseInterface
    {

        $client = $client ?? get_search_client();
        $res = $client->deleteDocument($this->getSearchDocumentId());

        // If it was successful, update the social_indexed_at timestamp
        if (
            $unindex ||
            ($res->getStatusCode() == 404) ||
            (
                ($res->getStatusCode() >= 200) &&
                ($res->getStatusCode() < 300)
            )
        ) {
            $this->updateQuietly(['social_indexed_at'=>null]);

        // Otherwise send a debug email
        } else {
            send_debug_email(
                "Social Unindex Failed: {$this->name} [{$this->id}]",
                "{$res->getStatusCode()}: {$res->getBody()->getContents()}"
            );
        }

        return $res;

    }

    /**
     * @throws GuzzleException
     */
    public function reindexSearchDocument($client = null, $unindex = false): void
    {
        if (is_null($client)) {
            $client = get_search_client();
        }
        $this->deleteSearchDocument($client, $unindex);
        if ($this->shouldIndexSearch()) {
            $this->indexSearchDocument($client);
        }
    }

    public function getFrontendStat(string $statClass, string $model, string $id): int
    {
        return $statClass::select(DB::raw('COUNT(*) AS count'))
            ->where('frontend', 'social')
            ->where('item_model', $model)
            ->where('item_id', $id)
            ->first()
            ->count;
    }
}
