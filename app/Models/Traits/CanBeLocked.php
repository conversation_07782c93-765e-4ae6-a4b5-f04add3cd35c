<?php

namespace App\Models\Traits;

use Illuminate\Database\Eloquent\Builder;

trait CanBeLocked
{
    public function scopeLocked($query): Builder
    {
        return $query->where('is_locked', true);
    }

    public function scopeUnlocked($query): Builder
    {
        return $query->where('is_locked', false);
    }

    public function lock(): bool
    {
        return $this->update(['is_locked' => true]);
    }

    public function unlock(): bool
    {
        return $this->update(['is_locked' => false]);
    }
}
