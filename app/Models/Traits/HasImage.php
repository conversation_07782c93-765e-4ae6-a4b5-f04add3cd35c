<?php

namespace App\Models\Traits;

use App\Jobs\DownloadMediaJob;
use Illuminate\Foundation\Bus\PendingClosureDispatch;
use Illuminate\Foundation\Bus\PendingDispatch;

trait HasImage
{
    public function saveImageFromUrl(string $url): PendingDispatch|PendingClosureDispatch
    {
        return dispatch(new DownloadMediaJob(
            $this,
            'image_path',
            $this->getImageDirectory(),
            $url
        ));
    }

    protected function getImageDirectory(): string
    {
        return "{$this->getTable()}/images";
    }
}
