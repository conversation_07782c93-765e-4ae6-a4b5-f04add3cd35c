<?php

namespace App\Models\Traits;

use App\Models\Category;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait HasCategories
{
    /**
     * @returns MorphToMany
     */
    public function categories(): MorphToMany
    {
        return $this->morphToMany(Category::class, 'categorizable');
    }

    public function saveCategoryRollup(array $categoryIds): array
    {
        $parentIds = array_map('strval', Category::whereIn('id', $categoryIds)->pluck('parent_id')->toArray());
        $grandparentIds = array_map('strval', Category::whereIn('id', $parentIds)->pluck('parent_id')->toArray());
        $ids = array_filter(array_unique(array_merge(
            $categoryIds,
            $parentIds,
            $grandparentIds
        )));

        return $this->categories()->sync($ids);
    }
}
