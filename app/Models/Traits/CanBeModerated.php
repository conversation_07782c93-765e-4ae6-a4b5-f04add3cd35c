<?php

namespace App\Models\Traits;

use App\Models\User;
use EightyNine\Approvals\Traits\Approvable;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use RingleSoft\LaravelProcessApproval\Enums\ApprovalActionEnum;
use RingleSoft\LaravelProcessApproval\Enums\ApprovalStatusEnum;
use RingleSoft\LaravelProcessApproval\Events\ProcessApprovalCompletedEvent;
use RingleSoft\LaravelProcessApproval\Events\ProcessApprovedEvent;
use RingleSoft\LaravelProcessApproval\Events\ProcessSubmittedEvent;
use RingleSoft\LaravelProcessApproval\Exceptions\ApprovalCompletedCallbackFailedException;
use RingleSoft\LaravelProcessApproval\Exceptions\ApprovalsPausedException;
use RingleSoft\LaravelProcessApproval\Exceptions\NoFurtherApprovalStepsException;
use RingleSoft\LaravelProcessApproval\Models\ProcessApproval;
use RuntimeException;
use Throwable;

trait CanBeModerated
{
    use Approvable;

    protected static array $approvalStatusColors = [
        'created' => 'info',
        'submitted' => 'warning',
        'pending' => 'warning',
        'rejected' => 'danger',
        'approved' => 'success',
        'discarded' => 'gray',
        'default' => 'gray',
    ];

    protected static array $approvalStatusIcons = [
        'created' => 'exclamation-circle',
        'submitted' => 'clock',
        'pending' => 'clock',
        'rejected' => 'x-circle',
        'approved' => 'check-circle',
        'discarded' => 'trash',
        'default' => 'question-mark-circle',
    ];

    protected static array $approvalStatusNames = [
        'created' => 'Not Contributed',
        'submitted' => 'Submitted',
        'pending' => 'Resubmitted',
        'rejected' => 'Rejected',
        'approved' => 'Approved',
        'discarded' => 'Discarded',
        'default' => 'Other',
    ];

    public function getApprovalStatusColor(): string
    {
        return static::$approvalStatusColors[strtolower($this->approvalStatus->status)] ?? static::$approvalStatusColors['default'];
    }

    public function getApprovalStatusIcon(): string
    {
        $icon = static::$approvalStatusIcons[strtolower($this->approvalStatus->status)] ?? static::$approvalStatusIcons['default'];

        return "heroicon-o-{$icon}";
    }

    public function getApprovalStatusName(): string
    {
        return strtoupper(static::$approvalStatusNames[strtolower($this->approvalStatus->status)] ?? static::$approvalStatusNames['default']);
    }

    public static function getApprovalStatusColors(): array
    {
        return static::$approvalStatusColors;
    }

    public static function getApprovalStatusIcons(): array
    {
        return static::$approvalStatusIcons;
    }

    public static function getApprovalStatusNames(): array
    {
        return static::$approvalStatusNames;
    }

    public function getRejectionComment(): ?string
    {
        return $this->lastApproval->comment;
    }

    public function isApproved(): bool
    {
        return ! is_null($this->approvalStatus) && ($this->approvalStatus->status == ApprovalActionEnum::APPROVED->value);
    }

    public function isPendingApproval(): bool
    {
        return ! is_null($this->approvalStatus) && ($this->approvalStatus->status == ApprovalActionEnum::SUBMITTED->value);
    }

    public function scopeApproved($query): Builder
    {
        return $query->where('is_approved', true);
    }

    public function scopeNonApproved($query): Builder
    {
        return $query->where('is_approved', false);
    }

    /**
     * @throws Exception
     */
    public function autoApprove(?string $comment = null): void
    {
        if (! $this->is_approved) {
            // TODO: clean this up later ... don't like to copy vendor code
            try {
                $this->autoSubmit();
                $this->refresh();
                $nextStep = $this->nextApprovalStep();
                try {
                    DB::beginTransaction();
                    $approval = ProcessApproval::query()->updateOrCreate([
                        'approvable_type' => self::getApprovableType(),
                        'approvable_id' => $this->id,
                        'process_approval_flow_step_id' => $nextStep->id,
                        'approval_action' => ApprovalActionEnum::APPROVED,
                        'comment' => $comment,
                        'user_id' => $this->user?->id,
                        'approver_name' => $this->user?->name ?? 'Unknown'
                    ]);
                    if ($approval) {
                        $this->updateStatus($nextStep->id, $approval);
                        if ($this->refresh()->isApprovalCompleted()) {
                            try {
                                $approvalCompleted = $this->onApprovalCompleted($approval);
                            } catch (Exception $e) {
                                throw ApprovalCompletedCallbackFailedException::create($this, $e->getMessage());
                            }
                            if (!$approvalCompleted) {
                                throw ApprovalCompletedCallbackFailedException::create($this);
                            }
                        }
                    }
                    DB::commit();
                    if ($approval) {
                        ProcessApprovedEvent::dispatch($approval);
                        if ($this->isApprovalCompleted()) {
                            ProcessApprovalCompletedEvent::dispatch($this);
                        }
                    }
                } catch (Exception $e) {
                    Log::error('Process approval failure: ', [$e]);
                    DB::rollBack();
                    throw $e;
                }
                $this->update(['is_approved' => true]);
            } catch (Throwable $e) {
                Log::error($e->getMessage());
            }
        }
    }

    /**
     * @throws Exception
     */
    public function unapprove(): void
    {
        if ($this->is_approved) {
            try {
                $this->undoLastApproval();
                $this->update(['is_approved' => false]);
            } catch (Throwable $e) {
                Log::error($e->getMessage());
            }
        }
    }

    /**
     * NOTE: This was copied from parts of RingleSoft\LaravelProcessApproval\Traits\Approvable::submit()
     *
     * @throws Exception
     */
    public function autoSubmit()
    {
        if (! $this->is_approved) {
            // TODO: clean this up later ... don't like to copy vendor code
            try {
                DB::beginTransaction();
                $approval = ProcessApproval::query()->create([
                    'approvable_type' => self::getApprovableType(),
                    'approvable_id' => $this->id,
                    'process_approval_flow_step_id' => $this->approvalFlowSteps()?->first()?->id ?? null, // Backward compatibility
                    'approval_action' => ApprovalActionEnum::SUBMITTED->value,
                    'comment' => '',
                    'user_id' => $this->user?->id,
                    'approver_name' => $this->user?->name ?? 'Unknown'
                ]);
                $this->approvalStatus()->update(['status' => ApprovalStatusEnum::SUBMITTED]);
                if ($this->isApprovalCompleted()) {
                    if (method_exists($this, 'onApprovalCompleted') && $this->onApprovalCompleted($approval)) {
                        // Approval went well, no need to rollback
                    } else {
                        throw new RuntimeException('Callback action after approval failed');
                    }
                }
                DB::commit();
                if ($approval) {
                    ProcessSubmittedEvent::dispatch($this);
                }
            } catch (Exception $e) {
                Log::debug('Process approval failure: ', [$e]);
                DB::rollBack();
                throw $e;
            }
        }
    }

    public function isApprovalCreator(?int $userId = null): bool
    {
        return $this->approvalStatus->creator_id && $this->approvalStatus->creator_id === ($userId ?? auth()->id());
    }

    public function scopePrivateOrContributed(Builder $query, int $teamId): Builder
    {
        return $query->where(function($qry) use ($teamId) {
            $qry->where('team_id', $teamId)
                ->orWhere('is_approved', true)
            ;
        });
    }

}
