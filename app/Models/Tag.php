<?php

namespace App\Models;

use App\Models\Traits\BelongsToTeam;
use App\Models\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Builder;
use Spatie\EloquentSortable\Sortable;
use Spatie\Tags\Tag as BaseTag;

class Tag extends BaseTag implements Sortable
{
    use BelongsToTeam;
    use BelongsToUser;

    public static function booted(): void
    {
        static::creating(function ($model) {
            $model->setDefaultTeam();
            $model->setDefaultOwner();
        });
    }

    public function scopeContaining(Builder $query, string $name, $locale = null): Builder
    {
        return parent::scopeContaining($query, $name, $locale)->team();
    }

    public static function findFromString(string $name, ?string $type = null, ?string $locale = null)
    {
        $locale = $locale ?? static::getLocale();
        return static::query()
            ->where('type', $type)
            ->where(function ($query) use ($name, $locale) {
                $query->where("name->{$locale}", $name)
                    ->orWhere("slug->{$locale}", $name);
            })
            ->team()
            ->first();
    }

    public static function findFromStringOfAnyType(string $name, ?string $locale = null)
    {
        $locale = $locale ?? static::getLocale();
        return static::query()
            ->where(function ($query) use ($name, $locale) {
                $query->where("name->{$locale}", $name)
                    ->orWhere("slug->{$locale}", $name);
            })
            ->team()
            ->get();
    }

}
