<?php

namespace App\Models;

use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Models\Traits\BelongsToTeam;
use App\Notifications\TeamInvitationEmail;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;

class TeamInvitation extends Model implements Recordable
{
    use BelongsToTeam;
    use Notifiable;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'email',
        'team_id',
        'role_id',
        'body',
        'last_sent_at',
        'accepted_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'last_sent_at' => 'timestamp',
            'accepted_at' => 'timestamp',
        ];
    }

    public static function booted(): void
    {
        static::created(function (Model $model) {
            TeamInvitation::disableRecording();
            $model->send();
            TeamInvitation::enableRecording();
        });
    }

    public function getNameAttribute(): string
    {
        return $this->email;
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    public function send(): void
    {
        $this->notify(new TeamInvitationEmail);
        $this->update(['last_sent_at' => $this->freshTimestamp()]);
    }
}
