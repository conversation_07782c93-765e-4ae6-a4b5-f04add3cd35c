<?php

namespace App\Models;

use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Models\Traits\HasFrontendListing;
use App\Models\Traits\HasSlug;
use Cyrilde<PERSON>it\EloquentViewable\Contracts\Viewable;
use Cyrilde<PERSON>it\EloquentViewable\InteractsWithViews;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Staudenmeir\LaravelAdjacencyList\Eloquent\Builder;

class Classification extends NestableModel implements Viewable
{
    use HasFactory;
    use HasFrontendListing;
    use HasSlug;
    use InteractsWithViews;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'parent_id',
        'tagline',
        'description',
        'slug',
        'url',
        'is_active',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFields = [
        'id',
        'name',
        'parent_id',
        'tagline',
        'description',
        'url',
        'slug',
        'is_active',
        'created_at',
        'updated_at',
        'deleted_at',
        'parent',
        'children',
        'sources',
        'sources.source_id',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedSorts = [
        'id',
        'name',
        'slug',
        'tagline',
        'description',
        'parent_id',
        'created_at',
        'updated_at',
        'deleted_at',
        'num_views',
        'rating',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFilters = [
        'id',
        'name',
        'parent_id',
        'tagline',
        'description',
        'slug',
        'is_active',
        'parent.slug',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedIncludes = [
        'parent',
        'children',
        'children.sources',
        'children.children',
        'children.children.sources',
        'sources',
    ];

    public static string $defaultSort = 'name';

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    public function sources(): HasMany
    {
        return $this->hasMany(Source::class);
    }

    public function collections(): HasMany
    {
        return $this->hasMany(Collection::class);
    }

    public function organizations(): HasMany
    {
        return $this->hasMany(Organization::class);
    }

    public function scopeActive($query): Builder
    {
        return $query->where('is_active', true);
    }
}
