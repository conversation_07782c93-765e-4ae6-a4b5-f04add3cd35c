<?php

namespace App\Models;

use App\Enums\OrganizationType;
use App\Models\Traits\BelongsToTeam;
use App\Models\Traits\BelongsToUser;
use App\Models\Traits\CanBeLocked;
use App\Models\Traits\CanBeModerated;
use App\Models\Traits\HasCategories;
use App\Models\Traits\HasClassification;
use App\Models\Traits\HasFrontendListing;
use App\Models\Traits\HasImage;
use App\Models\Traits\HasSlug;
use App\Models\Traits\HasType;
use CyrildeWit\EloquentViewable\Contracts\Viewable;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use RingleSoft\LaravelProcessApproval\Contracts\ApprovableModel;

class Organization extends NestableModel implements ApprovableModel, Viewable
{
    use BelongsToTeam;
    use BelongsToUser;
    use CanBeLocked;
    use CanBeModerated;
    use HasCategories;
    use HasClassification;
    use HasFrontendListing;
    use HasImage;
    use HasSlug;
    use HasType;
    use InteractsWithViews;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'type',
        'description',
        'image_path',
        'tax_id',
        'classification_id',
        'referral_url',
        'slug',
        'rating',
        'featured_seq',
        'header_image_path',
        'social_active',
        'social_indexed_at',
        'is_approved',
        'is_locked',
        'user_id',
        'team_id',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFields = [
        'id',
        'name',
        'type',
        'description',
        'image_path',
        'tax_id',
        'referral_url',
        'slug',
        'rating',
        'featured_seq',
        'num_views',
        'header_image_path',
        'social_active',
        'social_indexed_at',
        'is_approved',
        'is_locked',
        'user_id',
        'team_id',
        'created_at',
        'updated_at',
        'deleted_at',
        'categories.id',
        'categories.name',
        'teams.id',
        'classification.name',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedSorts = [
        'name',
        'type',
        'tax_id',
        'referral_url',
        'slug',
        'social_indexed_at',
        'rating',
        'featured_seq',
        'num_views',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFilters = [
        'name',
        'type',
        'tax_id',
        'classification_id',
        'slug',
        'rating',
        'featured_seq',
        'num_views',
        'social_active',
        'social_indexed_at',
        'is_approved',
        'user_id',
        'team_id',
        'is_locked',
        'created_at',
        'updated_at',
        'deleted_at',
        'categories.id',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedIncludes = [
        'team',
        'user',
        'categories',
        'team.organization',
        'classification',
    ];

    public static string $defaultSort = 'name';

    /**
     * @var array|string[]
     */
    protected array $ignored = [
        'is_approved',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'type' => OrganizationType::class,
            'social_active' => 'boolean',
            'is_approved' => 'boolean',
            'featured_seq' => 'integer',
            'is_locked' => 'boolean',
            'social_indexed_at' => 'datetime',
        ];
    }
}
