<?php

namespace App\Models;

use App\Models\Traits\BelongsToUser;
use App\Models\Traits\CanBeModerated;
use App\Models\Traits\HasCategories;
use App\Models\Traits\HasFrontendListing;
use App\Models\Traits\HasSlug;
use Cyrilde<PERSON>it\EloquentViewable\Contracts\Viewable;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use RingleSoft\LaravelProcessApproval\Contracts\ApprovableModel;

class Discussion extends NestableModel implements ApprovableModel, Viewable
{
    use BelongsToUser;
    use CanBeModerated;
    use HasCategories;
    use HasFrontendListing;
    use HasSlug;
    use InteractsWithViews;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'content',
        'slug',
        'user_id',
        'is_approved',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFields = [
        'title',
        'content',
        'slug',
        'user_id',
        'is_approved',
        'created_at',
        'updated_at',
        'deleted_at',
        'categories.id',
        'categories.name',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedSorts = [
        'title',
        'content',
        'slug',
        'user_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFilters = [
        'title',
        'content',
        'slug',
        'user_id',
        'is_approved',
        'created_at',
        'updated_at',
        'deleted_at',
        'categories.id',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedIncludes = [
        'user',
        'categories',
    ];

    public static string $defaultSort = 'title';

    /**
     * @var array|string[]
     */
    protected array $ignored = [
        'is_approved',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_approved' => 'boolean',
        ];
    }
}
