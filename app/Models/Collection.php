<?php

namespace App\Models;

use App\Enums\CollectionType;
use App\Enums\SourceImageHandling;
use App\Enums\SourceType;
use App\Events\CollectionSavedEvent;
use App\Jobs\ImportCollectionSourceJob;
use App\Jobs\ImportRssFeedPageSourcesJob;
use App\Models\Traits\BelongsToTeam;
use App\Models\Traits\BelongsToUser;
use App\Models\Traits\CanBeLocked;
use App\Models\Traits\CanBeModerated;
use App\Models\Traits\HasCategories;
use App\Models\Traits\HasClassification;
use App\Models\Traits\HasExternalId;
use App\Models\Traits\HasFrontendListing;
use App\Models\Traits\HasImage;
use App\Models\Traits\HasSlug;
use App\Models\Traits\HasType;
use App\Services\Formatter;
use App\Services\YoutubeClient;
use App\Spiders\WebsiteSpider;
use Carbon\Carbon;
use CyrildeWit\EloquentViewable\Contracts\Viewable;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use PhanAn\Poddle\Poddle;
use RingleSoft\LaravelProcessApproval\Contracts\ApprovableModel;
use RoachPHP\Roach;
use RoachPHP\Spider\Configuration\Overrides;
use SimplePie\SimplePie;
use Vedmant\FeedReader\Facades\FeedReader;
use willvincent\Rateable\Rateable;
use function Psy\debug;

class Collection extends NestableModel implements ApprovableModel, Viewable
{
    use BelongsToTeam;
    use BelongsToUser;
    use CanBeLocked;
    use CanBeModerated;
    use HasCategories;
    use HasClassification;
    use HasExternalId;
    use HasFrontendListing;
    use HasImage;
    use HasSlug;
    use HasType;
    use InteractsWithViews;
    use Rateable;

    protected const RSS_FEED_PAGING_PARAMS = [
        'page',
        'paged',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'type',
        'url',
        'source_url_content_selector',
        'source_url_content_strip',
        'source_url_author_selector',
        'source_url_published_selector',
        'source_url_image_selector',
        'source_url_render_js',
        'parent_id',
        'external_id',
        'classification_id',
        'referral_url',
        'description',
        'is_approved',
        'is_locked',
        'social_active',
        'social_indexed_at',
        'image_path',
        'language',
        'auto_import_sources',
        'auto_transcribe_sources',
        'auto_categorize_sources',
        'auto_approve_sources',
        'auto_list_sources',
        'auto_index_sources',
        'default_source_weight',
        'source_image_handling',
        'enforce_source_excerpts',
        'enforce_source_categories',
        'source_title_strip',
        'source_url_restriction',
        'source_contributor_id',
        'source_contributor_role',
        'override_source_restrictions',
        'team_id',
        'user_id',
        'slug',
        'rating',
        'featured_seq',
        'num_views',
    ];

    /**
     * @var string[]
     */
    protected $dispatchesEvents = [
        'saved' => CollectionSavedEvent::class,
        'softDeleted' => CollectionSavedEvent::class,
        'restored' => CollectionSavedEvent::class,
        'forceDeleting' => CollectionSavedEvent::class,
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFields = [
        'id',
        'name',
        'type',
        'url',
        'parent_id',
        'external_id',
        'classification_id',
        'referral_url',
        'description',
        'language',
        'team_id',
        'user_id',
        'image_path',
        'created_at',
        'updated_at',
        'deleted_at',
        'sources',
        'sources.source_id',
        'sources.name',
        'is_approved',
        'social_active',
        'social_indexed_at',
        'is_locked',
        'auto_import_sources',
        'auto_transcribe_sources',
        'auto_categorize_sources',
        'auto_approve_sources',
        'auto_list_sources',
        'auto_index_sources',
        'default_source_weight',
        'source_image_handling',
        'enforce_source_excerpts',
        'source_contributor_id',
        'source_contributor_role',
        'enforce_source_categories',
        'source_url_content_selector',
        'source_url_content_strip',
        'source_url_author_selector',
        'source_url_published_selector',
        'source_url_image_selector',
        'source_url_render_js',
        'override_source_restrictions',
        'slug',
        'rating',
        'featured_seq',
        'num_views',
        'teams.id',
        'organizations.id',
        'organizations.name',
        'organizations.referral_url',
        'organizations.type',
        'organizations.slug',
        'categories.id',
        'categories.name',
        'classification.name',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedSorts = [
        'id',
        'name',
        'type',
        'parent_id',
        'team_id',
        'user_id',
        'social_indexed_at',
        'created_at',
        'updated_at',
        'deleted_at',
        'slug',
        'rating',
        'featured_seq',
        'num_views',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFilters = [
        'id',
        'name',
        'type',
        'url',
        'parent_id',
        'referral_url',
        'external_id',
        'language',
        'team_id',
        'user_id',
        'created_at',
        'updated_at',
        'deleted_at',
        'is_approved',
        'social_active',
        'social_indexed_at',
        'auto_import_sources',
        'auto_transcribe_sources',
        'auto_categorize_sources',
        'auto_approve_sources',
        'auto_list_sources',
        'auto_index_sources',
        'default_source_weight',
        'source_image_handling',
        'enforce_source_excerpts',
        'source_contributor_id',
        'source_contributor_role',
        'source_url_render_js',
        'override_source_restrictions',
        'slug',
        'rating',
        'is_locked',
        'featured_seq',
        'num_views',
        'organizations.id',
        'organizations.type',
        'categories.id',
        'sources.id',
        'classification.id',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedIncludes = [
        'parent',
        'children',
        'children.sources',
        'sources',
        'team',
        'user',
        'team.organization',
        'categories',
        'classification',
    ];

    public static string $defaultSort = 'name';

    /**
     * @var array|string[]
     */
    protected array $ignored = [
        'is_approved',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'type' => CollectionType::class,
            'is_approved' => 'boolean',
            'auto_import_sources' => 'boolean',
            'auto_transcribe_sources' => 'boolean',
            'auto_categorize_sources' => 'boolean',
            'auto_approve_sources' => 'boolean',
            'auto_list_sources' => 'boolean',
            'auto_index_sources' => 'boolean',
            'default_source_weight' => 'integer',
            'source_image_handling' => SourceImageHandling::class,
            'source_url_render_js' => 'boolean',
            'enforce_source_excerpts' => 'boolean',
            'enforce_source_categories' => 'array',
            'featured_seq' => 'integer',
            'is_locked' => 'boolean',
            'social_active' => 'boolean',
            'social_indexed_at' => 'datetime',
            'override_source_restrictions' => 'boolean',
        ];
    }

    public static function booted(): void
    {

        parent::booted();

        // Ensure enforced source category IDs are integers
        static::saving(function (Collection $model) {
            if (! empty($model->enforce_source_categories)) {
                $model->enforce_source_categories = array_map('intval', $model->enforce_source_categories);
            }
        });

        static::forceDeleting(function ($model) {
            $model->sources()->detach();
        });

    }

    public function sources(): BelongsToMany
    {
        return $this->belongsToMany(Source::class);
    }

    public function sourceContributor(): BelongsTo
    {
        return $this->belongsTo(Contributor::class);
    }

    public function importSources(bool $full = false): void
    {
        if ($this->type == CollectionType::RSS) {
            $this->importRssFeed($full);
        } else if ($this->type == CollectionType::PODCAST) {
            $this->importPodcastEpisodes($full);
        } else if ($this->type == CollectionType::WEBSITE) {
            $this->crawlWebsite($full);
        } else if ($this->type == CollectionType::CHANNEL) {
            $this->importYoutubeVideos($full);
        }
    }

    public function importRssFeed(bool $full = false, bool $synchronous = false, int $numPages = 0): void
    {

        if ($this->type == CollectionType::RSS) {

            increase_timeout('HIGH');

            // Only try to find additional pages if this is a 'full' operation
            $maxItems = $full ? 0 : env_int('NUM_LATEST_SOURCE_IMPORTS');
            $pageParam = null;
            $pageNum = null;
            if ($full) {

                // Get the first item on the feed's first page
                $feed = $this->loadRssFeed($this->url);

                $items = $feed->get_items();
//                Log::debug(print_r($items[0]->get_id(), true));
                if (count($items) > 0) {

                    $firstItemId = $items[0]->get_id();

                    // Try to figure out the paging param
                    foreach (static::RSS_FEED_PAGING_PARAMS as $param) {
                        try {
                            $url = add_url_params($this->url, ["{$param}" => 2]);
                            if (! empty($url)) {
                                $feed = $this->loadRssFeed($url);
                                if (
                                    (count($feed->get_items()) > 0) &&
                                    $feed->get_items()[0]->get_id() != $firstItemId
                                ) {
                                    $pageParam = $param;
                                    break;
                                }
                            }
                        } catch (\Exception $e) {
                            Log::error("Can't parse {$this->url}");
                            Log::error($e->getMessage());
                        }
                    }

                    if (!is_null($pageParam)) {
                        $maxItems = 0;
                        $pageNum = 1;
                    }

                }

            }

            Log::debug("PAGE PARAM: {$pageParam}");
            Log::debug("MAX ITEMS: {$maxItems}");
            Log::debug("PAGE NUM: {$pageNum}");

            if ($numPages !== 0) {
                for ($i = 1; $i <= $numPages; $i++) {
                    $this->dispatchRssFeedPageJob($maxItems, $pageParam, $i, $synchronous, false);
                }
            } else {
                $this->dispatchRssFeedPageJob($maxItems, $pageParam, $pageNum, $synchronous);
            }

        }

    }

    protected function loadRssFeed(string $url): SimplePie
    {
        try {
            $feed = FeedReader::read($url);
        } catch (\Exception $e) {
            $feed = FeedReader::read(get_proxied_url($url));
        }
        return $feed;
    }

    protected function dispatchRssFeedPageJob(
        int $maxItems = 0,
        ?string $pageParam = null,
        ?int $pageNum = null,
        bool $synchronous = false,
        bool $chain = true
    ): void {
        if ($synchronous) {
            dispatch_sync(new ImportRssFeedPageSourcesJob($this, $maxItems, $pageParam, $pageNum, $synchronous, $chain));
        } else {
            dispatch(new ImportRssFeedPageSourcesJob($this, $maxItems, $pageParam, $pageNum, $synchronous, $chain));
        }
    }

    public function importRssFeedPage(
        int $maxItems = 0,
        ?string $pageParam = null,
        ?int $pageNum = null,
        bool $synchronous = false,
        bool $chain = true
    ): void {

        if ($this->type == CollectionType::RSS) {

            increase_timeout('HIGH');

            if (! is_null($pageParam)) {
                $url = add_url_params($this->url, ["{$pageParam}" => $pageNum]);
            } else {
                $url = $this->url;
            }

            Log::debug("Fetching RSS Feed Page: {$url}");
            $feed = $this->loadRssFeed($url);
            if (count($feed->get_items()) > 0) {

                // Set the Collection image as the feed image if it's not already set
                $feedImage = $feed->get_image_link();
                if (empty($this->image_path) && ! empty($feedImage)) {
                    $this->saveImageFromUrl($feedImage);
                }

                $numItems = 0;
                foreach ($feed->get_items() as $item) {

                    $authors = [];
                    if (! empty($item->get_authors())) {
                        foreach ($item->get_authors() as $author) {
                            $authors[] = $author->get_name();
                        }
                    }

                    $sourceUrl = str_replace('&amp;', '&', $item->get_permalink());
                    $this->addUniqueSource(
                        'url',
                        $sourceUrl,
                        [
                            'name' => html_entity_decode($item->get_title()),
                            'type' => SourceType::URL,
                            'url' => $sourceUrl,
                            'description' => $item->get_description(),
                            'published_on' => $item->get_gmdate(),
                            'external_id' => $item->get_id(),
                            'url_content_selector' => $this->source_url_content_selector ?? null,
                            'url_content_strip' => $this->source_url_content_strip ?? null,
                            'url_author_selector' => $this->source_url_author_selector ?? null,
                            'url_published_selector' => $this->source_url_published_selector ?? null,
                            'url_image_selector' => $this->source_url_image_selector ?? null,
                            'url_render_js' => $this->source_url_render_js,
                            'social_active' => $this->auto_list_sources,
                            'image_url' => $feedImage ?? null,
                            'agent_terms_accepted' => $this->auto_index_sources,
                            'agent_active' => $this->auto_index_sources,
                        ],
                        $authors
                    );
                    $numItems++;

                    if ($maxItems && ($numItems >= $maxItems)) {
                        break;
                    }

                }

                Log::debug("{$url}: Imported {$numItems} items");

                if (! is_null($pageParam) && $chain) {
                    $this->dispatchRssFeedPageJob($maxItems, $pageParam, ++$pageNum, $synchronous);
                }

            }

        }

    }

    public function importPodcastEpisodes(bool $full = false): void
    {

        if (($this->type == CollectionType::PODCAST) && !empty($this->url)) {

            increase_timeout('HIGH');

//            Log::debug(strtoupper($this->name)."\n");
            $feed = Poddle::fromUrl($this->url);
            $channel = $feed->getChannel();

            // Backfill any fields that are empty using the feed
            $attrs = [];
            if (empty($this->description) && ! empty($channel->description)) {
                $attrs['description'] = $channel->description;
            }
            if (empty($this->referral_url) && ! empty($channel->link)) {
                $attrs['referral_url'] = $channel->link;
            }
            if (empty($this->language) && ! empty($channel->language)) {
                $attrs['language'] = parse_language_code($channel->language);
            }
            if (empty($this->image_path) && ! empty($channel->image)) {
                $this->saveImageFromUrl($channel->image);
            }
            if (! empty($attrs)) {
                $this->update($attrs);
            }

            $episodes = $feed->getEpisodes();
            $maxEpisodes = $full ? 0 : env('NUM_LATEST_SOURCE_IMPORTS');
            $numEpisodes = 0;
            foreach ($episodes as $episode) {

                //                Log::debug('--------------------------------');
                //                Log::debug($episode->title);
                //                Log::debug('--------------------------------');
                //                Log::debug("GUID: {$episode->guid->value}");
                //                Log::debug("URL: {$episode->metadata->link}");
                //                Log::debug("Date: {$episode->metadata->pubDate->format('Y-m-d')}");
                //                Log::debug("Media URL: {$episode->enclosure->url}");
                //                Log::debug("Description: {$episode->metadata->description}");
                //                Log::debug("Image URL: {$episode->metadata->image}");
                //                Log::debug(json_encode($episode->metadata->transcripts, JSON_UNESCAPED_SLASHES));
                //                Log::debug("\n");

                $guid = $episode->guid->value;
                $source = $this->addUniqueSource(
                    'external_id',
                    $guid,
                    [
                        'name' => html_entity_decode($episode->title),
                        'type' => SourceType::EPISODE,
                        'url' => ! empty($episode->metadata->link) ? $episode->metadata->link : $channel->link,
                        'description' => ! empty($episode->metadata->description) ? $episode->metadata->description : $channel->description,
                        'published_on' => $episode->metadata->pubDate->format('Y-m-d'),
                        'media_url' => $episode->enclosure->url,
                        'language' => Formatter::shortLang($channel->language),
                        'external_id' => $guid,
                        'image_url' => $episode->metadata->image ?? $channel->image ?? null,
                        'social_active' => $this->auto_list_sources,
                        'agent_terms_accepted' => $this->auto_index_sources,
                        'agent_active' => $this->auto_index_sources,
                    ],
                    [$channel->metadata->author],
                    'host',
                );
                $numEpisodes++;

                if ($maxEpisodes && ($numEpisodes >= $maxEpisodes)) {
                    break;
                }

            }

        }

    }

    public function crawlWebsite(int|bool|string $resumeAt = true): void
    {
        if (($this->type == CollectionType::WEBSITE) && !empty($this->url)) {

            increase_timeout('HIGH');

            $seedUrl = $this->source_url_render_js ? get_proxied_url($this->url, true) : $this->url;
            if ($this->isWebsiteSitemap()) {
                $sourceCount = $this->sources()->withTrashed()->count();
                if (is_int($resumeAt)) {
                    if ($resumeAt < 0) {
                        $resumeAt = $sourceCount + $resumeAt;
                        if ($resumeAt < 0) {
                            $resumeAt = 0;
                        }
                    }
                } else {
                    if ($resumeAt) {
                        $resumeAt = $sourceCount;
                    } else {
                        $resumeAt = 0;
                    }
                }
            } else if (is_string($resumeAt)) {
                $seedUrl = $this->source_url_render_js ? get_proxied_url($resumeAt, true) : $resumeAt;
            }

            Log::debug("WEBSITE CRAWL: {$seedUrl}");

            Roach::startSpider(
                WebsiteSpider::class,
                new Overrides(startUrls: [$seedUrl]),
                [
                    'model'     => $this,
                    'resumeAt'  => $resumeAt,
                    'useProxy'  => $this->source_url_render_js,
                ]
            );

        }
    }

    public function isWebsiteSitemap(): bool
    {
        return str_ends_with($this->url, '.xml');
    }

    public function importYoutubeVideos(bool $full = false, ?int $year = null): void
    {

        if ($this->type == CollectionType::CHANNEL) {

            increase_timeout('HIGH');

            $maxResults = $full ? env_int('MAX_YOUTUBE_RESULTS') : env_int('NUM_LATEST_SOURCE_IMPORTS');
            $client = new YoutubeClient(env('YOUTUBE_API_KEY'), $this->override_source_restrictions);
            $videos = $client->getVideos($this->external_id, $maxResults, $year);
            $numVideos = count($videos);
            $yearStr = is_null($year) ? '' : "[Year: {$year}]";
            Log::debug("Importing {$numVideos} videos from {$this->name} {$yearStr} ... ");

            if (!empty($videos)) {

                foreach ($videos as $video) {
                    if (! isset($video->id)) {
                        Log::error("NO VIDEO ID:\n".print_r($video, JSON_PRETTY_PRINT));
                    } else {
                        $this->addUniqueSource(
                            'external_id',
                            $video->id,
                            [
                                'name' => html_entity_decode($video->snippet->title),
                                'external_id' => $video->id,
                                'url' => YoutubeClient::getVideoUrl($video->id),
                                'published_on' => $video->snippet->publishedAt,
                                'description' => $video->snippet->description,
                                'image_url' => YoutubeClient::getBestThumbnailUrl($video->snippet->thumbnails),
                                'language' => YoutubeClient::getVideoLanguage($video->snippet),
                                'type' => SourceType::YOUTUBE,
                                'social_active' => $this->auto_list_sources,
                                'last_imported_at' => get_now_timestamp(),
                                'agent_terms_accepted' => $this->auto_index_sources,
                                'agent_active' => $this->auto_index_sources,
                            ]
                        );
                    }
                }

                if ($full) {
                    if (is_null($year)) {
                        $year = Carbon::now()->subYear()->format('Y');
                    } else {
                        --$year;
                    }
                    $this->importYoutubeVideos(true, $year);
                }

            }

        }

    }

    public function addUniqueSource(
        string $field,
        string $id,
        array $attrs,
        array $contributors = [],
        string $contributorRole = 'author'
    ): Source|bool {

        // Only create the Source if it isn't there already
        if (!Source::withTrashed()->where('team_id', $this->team_id)->where($field, $id)->exists()) {

            // If an enforced Contributor was selected, set it on the Source now
            if ($this->sourceContributor) {
                $contributors = [$this->sourceContributor->name];
                $contributorRole = $this->source_contributor_role;
            }

            // Dispatch the job to import the Source
            dispatch(new ImportCollectionSourceJob(
                $this,
                $attrs,
                $field,
                $id,
                $contributors,
                $contributorRole,
            ));

        } else {
            Log::debug("SKIP DUPLICATE SOURCE: {$field}: {$id}");
        }

        return false;

    }

    public function importSource(
        array $attrs,
        array $contributors = [],
        string $contributorRole = 'author',
        ?array $categories = [],
        bool $autoCategorize = false,
        bool $autoTranscribe = false
    ): Source|bool {

        // Set field defaults
        $attrs['importer_collection_id'] = $this->id;
        $attrs['inherit_importer_settings'] = true;
        if (! isset($attrs['name'])) {
            $attrs['name'] = Source::getPrefixedName();
        }
        if (isset($attrs['description'])) {
            $attrs['description'] = convert_html(Str::limit($attrs['description'], 64000, ' ...'));
        }
        if (isset($attrs['published_on'])) {
            $attrs['published_on'] = Formatter::date($attrs['published_on']);
        }
        if (! isset($attrs['weight'])) {
            $attrs['weight'] = $this->default_source_weight;
        }
        $attrs['team_id'] = $this->team_id;
        $attrs['user_id'] = $this->user_id;

        // Set the new Source to be listed automatically if toggle is active
        if ($this->auto_list_sources) {
            $attrs['social_active'] = true;
        }

        // Create the new Source
        $source = Source::create(Arr::except($attrs, ['image_url']));

        // Add the categories to the Source if passed
        if (! empty($categories)) {
            $source->categories()->sync($categories);
        }

        // Auto-categorize any available category slots if set to do so
        if ($autoCategorize) {
            $source->autoCategorize();
        }

        // Approve the new Source if the Collection auto-approve toggle is on
        $approvalComment = "Auto-approved from Collection: {$this->name}";
        if ($this->auto_approve_sources) {
            $source->autoApprove($approvalComment);
        }

        if ($source && isset($attrs['image_url']) && ! empty($attrs['image_url'])) {
            $source->saveImageFromUrl($attrs['image_url']);
        }

        // Add the Source to the Collection (feed)
        $this->sources()->save($source);

        // Add the contributors to the Source
        foreach ($contributors as $contributor) {
            $contribution = SourceContribution::firstOrCreateByContributorName($contributor, $source, $contributorRole);
            if ($this->auto_approve_sources && ! $contribution->contributor->approved()) {
                $contribution->contributor->autoApprove($approvalComment, $this->user);
            }
        }

        // Auto-transcribe
        if ($autoTranscribe) {
            $source->transcribeAsync();
        }

        return $source;

    }

    public function scopeShouldAutoImportSources($query): Builder
    {
        return $query->where('auto_import_sources', true)->approved();
    }
}
