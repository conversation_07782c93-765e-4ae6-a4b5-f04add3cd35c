<?php

namespace App\Models;

use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Events\TeamCreatedEvent;
use App\Models\Traits\BelongsToUser;
use App\Models\Traits\CanBeLocked;
use App\Models\Traits\SyncsToFrontend;
use App\Services\Gatekeeper;
use Filament\Models\Contracts\HasAvatar;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Laravel\Pennant\Concerns\HasFeatures as HasLaravelFeatures;
use Maartenpaauw\Filament\Pennant\Concerns\HasFeatures;
use Opcodes\Spike\Facades\Spike;
use Opcodes\Spike\Stripe\SpikeBillable;

class Team extends Model implements HasAvatar, Recordable
{
    use BelongsToUser;
    use CanBeLocked;
    use HasFactory;
    use HasFeatures;
    use HasLaravelFeatures;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SpikeBillable;
    use SoftDeletes;
    use SyncsToFrontend;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'is_active',
        'avatar_path',
        'user_id',
        'is_locked',
        'is_prepaid_only',
        'is_nonbillable',
        'is_partner',
        'is_trial',
        'has_custom_corpus',
        'corpus_api_key',
    ];

    /**
     * @var string[]
     */
    protected $dispatchesEvents = [
        'created' => TeamCreatedEvent::class,
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedIncludes = [

    ];

    protected array $planOptions = [];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'is_locked' => 'boolean',
            'has_beta_access' => 'boolean',
            'trial_ends_at' => 'datetime',
            'is_prepaid_only' => 'boolean',
            'is_nonbillable' => 'boolean',
            'is_partner' => 'boolean',
            'is_trial' => 'boolean',
            'has_custom_corpus' => 'boolean',
            'corpus_api_key' => 'encrypted',
        ];
    }

    public static function booted(): void
    {

        static::forceDeleting(function ($model) {
            $model->memberships()->forceDelete();
        });

        static::saved(function ($model) {
            if ($model->hasChanged('has_custom_corpus')) {
                $model->agents()->update([
                    'use_team_corpus' => $model->has_custom_corpus,
                    'disable_community_corpus' => $model->has_custom_corpus,
                ]);
            }
        });

        parent::booted();

    }

    public function stripeEmail(): ?string
    {
        return $this->owner->email;
    }

    public function getFilamentAvatarUrl(): ?string
    {
        return $this->avatar_path ? Storage::disk('public')->url($this->avatar_path) : null;
    }

    public function memberships(): HasMany
    {
        return $this->hasMany(Membership::class);
    }

    public function members(): HasOneThrough
    {
        return $this->hasOneThrough(User::class, Membership::class, 'model_id', 'id');
    }

    public function admins(): \Illuminate\Database\Eloquent\Builder
    {
        return User::join('memberships', 'users.id', 'memberships.model_id')
            ->select('users.*')
            ->where('memberships.team_id', $this->id)
            ->where('memberships.role_id', Gatekeeper::getManagerRoleId());
    }

    public function getNumAdmins(): int
    {
        return $this->admins()->get()->count();
    }

    public function invitations(): HasMany
    {
        return $this->hasMany(TeamInvitation::class);
    }

    public function organization(): HasOne
    {
        return $this->hasOne(Organization::class);
    }

    public function hasOrganization(): bool
    {
        return $this->organization()->exists();
    }

    public function agent(): HasOne
    {
        return $this->hasOne(Agent::class);
    }

    public function agents(): HasMany
    {
        return $this->hasMany(Agent::class);
    }

    public function hasAgent(): bool
    {
        return $this->agent()->exists();
    }

    public function getPlanOptions(): array
    {
        return (Gatekeeper::isAdmin() || $this->is_partner || $this->is_trial) ? Spike::findSubscriptionPlan('pro')->options : $this->currentSubscriptionPlan()->options;
    }

    public function getPlanOption(string $option): mixed
    {

        if (empty($this->planOptions)) {
            $this->planOptions = Arr::dot($this->getPlanOptions());
//            Log::debug(print_r($this->planOptions, true));
        }

        $value = Arr::get($this->planOptions, $option);

        if (is_null($value) && Arr::has($this->planOptions, "{$option}.0")) {
            $value = [];
            $i = 0;
            while (Arr::has($this->planOptions, "{$option}.{$i}")) {
                $value[] = Arr::get($this->planOptions, "{$option}.{$i}");
                ++$i;
            }
        }

        return $value;

    }

    public function makeNonBillable(): void
    {
        $this->update(['is_nonbillable'=>true]);
        $agentIds = $this->agents()->pluck('id')->toArray();
        Agent::whereIn('id', $agentIds)->update(['is_nonbillable'=>true]);
        AgentPrompt::whereIn('agent_id', $agentIds)->update(['is_nonbillable'=>true]);
    }

    public function syncToFrontend(): void
    {
        $this->pushFieldsToFrontend([
            'name',
            'is_active',
            'is_trial',
            'has_custom_corpus',
            'corpus_api_key',
        ]);
        $this->pruneDeletedFromFrontend();
    }

}
