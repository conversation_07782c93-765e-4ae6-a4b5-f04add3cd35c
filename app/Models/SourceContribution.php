<?php

namespace App\Models;

use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Enums\SourceContributionRole;
use App\Models\Traits\CanBeLocked;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class SourceContribution extends Model implements Recordable
{
    use CanBeLocked;
    use HasFactory;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'source_id',
        'contributor_id',
        'is_locked',
        'role',
        'seq',
        'team_id',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFields = [
        'id',
        'source_id',
        'contributor_id',
        'is_locked',
        'role',
        'seq',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedSorts = [
        'id',
        'source_id',
        'contributor_id',
        'role',
        'seq',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFilters = [
        'id',
        'source_id',
        'contributor_id',
        'is_locked',
        'role',
        'seq',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedIncludes = [
        'source',
        'contributor',
    ];

    public static string $defaultSort = 'seq';

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'role' => SourceContributionRole::class,
            'seq' => 'integer',
            'is_locked' => 'boolean',
        ];
    }

    public function getNameAttribute(): string
    {
        $role = ucwords($this->role->value);

        return "{$role}: {$this->contributor->name}";
    }

    public function source(): BelongsTo
    {
        return $this->belongsTo(Source::class)->withTrashed();
    }

    public function contributor(): BelongsTo
    {
        return $this->belongsTo(Contributor::class)->withTrashed();
    }

    /**
     * @return mixed
     */
    public static function firstOrCreateByContributorName(string $name, ?Model $source, string $role = 'author'): ?SourceContribution
    {
        $contributor = Contributor::firstOrCreateByName($name, $source->team_id, $source->user_id);
        return SourceContribution::firstOrCreate([
            'source_id' => $source->id,
            'contributor_id' => $contributor->id,
            'role' => $role,
            'team_id' => $source->team_id,
        ]);
    }
}
