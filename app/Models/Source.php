<?php

namespace App\Models;

use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Enums\CollectionType;
use App\Enums\SourceContributionRole;
use App\Enums\SourceImageHandling;
use App\Enums\SourceMediaStatus;
use App\Enums\SourceType;
use App\Events\SourceSavedEvent;
use App\Jobs\AutoCategorizeSourceJob;
use App\Jobs\CascadeSourceCategoriesJob;
use App\Jobs\DownloadSourceYoutubeTranscriptJob;
use App\Jobs\GetSourceYoutubeMediaUrlJob;
use App\Jobs\ImportBookSourceDataJob;
use App\Jobs\ImportEpisodeSourceDataJob;
use App\Jobs\ImportSourceTranscriptJob;
use App\Jobs\ImportUrlSourceDataJob;
use App\Jobs\ImportYoutubeSourceDataJob;
use App\Jobs\ManageSourceRagDocumentJob;
use App\Jobs\TranscribeSourceMediaJob;
use App\Models\Traits\BelongsToTeam;
use App\Models\Traits\BelongsToUser;
use App\Models\Traits\CanBeLocked;
use App\Models\Traits\CanBeModerated;
use App\Models\Traits\HasCategories;
use App\Models\Traits\HasClassification;
use App\Models\Traits\HasExternalId;
use App\Models\Traits\HasFrontendListing;
use App\Models\Traits\HasImage;
use App\Models\Traits\HasSlug;
use App\Models\Traits\HasType;
use App\Services\LlmClient;
use App\Services\AmazonClient;
use App\Services\TranscriptionClient;
use App\Services\VectaraClient;
use App\Services\YoutubeClient;
use App\Services\YoutubeTranscriptionClient;
use App\Spiders\EpisodeSpider;
use App\Spiders\UrlSpider;
use ConvertApi\ConvertApi;
use CyrildeWit\EloquentViewable\Contracts\Viewable;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Bus\PendingClosureDispatch;
use Illuminate\Foundation\Bus\PendingDispatch;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Psr\Http\Message\ResponseInterface;
use RingleSoft\LaravelProcessApproval\Contracts\ApprovableModel;
use RoachPHP\Roach;
use RoachPHP\Spider\Configuration\Overrides;
use Spatie\Tags\HasTags;
use willvincent\Rateable\Rateable;
use YouTube\Exception\YouTubeException;
use YouTube\YouTubeDownloader;

class Source extends Model implements ApprovableModel, Recordable, Viewable
{
    use BelongsToTeam;
    use BelongsToUser;
    use CanBeLocked;
    use CanBeModerated;
    use Eventually;
    use HasCategories;
    use HasClassification;
    use HasExternalId;
    use HasFactory;
    use HasFrontendListing;
    use HasImage;
    use HasSlug;
    use HasType;
    use InteractsWithViews;
    use Rateable;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;
    use HasTags;

    protected const TITLE_PLACEHOLDER_PREFIX = 'placeholder_';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'subtitle',
        'type',
        'url',
        'url_content_selector',
        'url_content_strip',
        'url_author_selector',
        'url_published_selector',
        'url_image_selector',
        'url_render_js',
        'published_on',
        'written_on',
        'language',
        'team_id',
        'user_id',
        'external_id',
        'image_path',
        'media_path',
        'media_url',
        'media_process_id',
        'media_status',
        'media_error',
        'text_path',
        'text_imported_at',
        'referral_url',
        'description',
        'status',
        'agent_terms_accepted',
        'agent_active',
        'agent_hidden',
        'agent_indexed_at',
        'social_active',
        'social_indexed_at',
        'weight',
        'importer_collection_id',
        'inherit_importer_settings',
        'last_imported_at',
        'is_locked',
        'is_approved',
        'slug',
        'rating',
        'featured_seq',
        'num_views',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFields = [
        'id',
        'name',
        'subtitle',
        'type',
        'url',
        'url_content_selector',
        'url_render_js',
        'published_on',
        'written_on',
        'language',
        'team_id',
        'user_id',
        'external_id',
        'image_path',
        'media_path',
        'media_url',
        'media_process_id',
        'media_status',
        'media_error',
        'text_imported_at',
        'referral_url',
        'description',
        'status',
        'agent_terms_accepted',
        'agent_active',
        'agent_hidden',
        'agent_indexed_at',
        'social_active',
        'social_indexed_at',
        'weight',
        'is_locked',
        'importer_collection_id',
        'inherit_importer_settings',
        'last_imported_at',
        'created_at',
        'updated_at',
        'deleted_at',
        'is_approved',
        'is_locked',
        'contributions.source_id',
        'contributions.role',
        'contributions.seq',
        'contributions.contributor_id',
        'contributors.id',
        'contributors.title',
        'contributors.first_name',
        'contributors.last_name',
        'contributors.suffix',
        'contributors.born_in',
        'contributors.died_id',
        'contributors.url',
        'contributors.avatar_path',
        'slug',
        'rating',
        'featured_seq',
        'num_views',
        'teams.id',
        'organizations.id',
        'organizations.name',
        'organizations.type',
        'organizations.slug',
        'categories.id',
        'categories.name',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedSorts = [
        'id',
        'name',
        'subtitle',
        'type',
        'url',
        'published_on',
        'written_on',
        'language',
        'media_status',
        'text_imported_at',
        'team_id',
        'user_id',
        'status',
        'agent_terms_accepted',
        'agent_active',
        'agent_hidden',
        'agent_indexed_at',
        'social_active',
        'social_indexed_at',
        'weight',
        'is_locked',
        'last_imported_at',
        'created_at',
        'updated_at',
        'deleted_at',
        'slug',
        'rating',
        'featured_seq',
        'num_views',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFilters = [
        'id',
        'name',
        'subtitle',
        'type',
        'url',
        'url_content_selector',
        'published_on',
        'written_on',
        'language',
        'media_status',
        'text_imported_at',
        'team_id',
        'user_id',
        'external_id',
        'referral_url',
        'description',
        'status',
        'agent_terms_accepted',
        'agent_active',
        'agent_hidden',
        'agent_indexed_at',
        'social_active',
        'social_indexed_at',
        'weight',
        'is_locked',
        'importer_collection_id',
        'inherit_importer_settings',
        'last_imported_at',
        'created_at',
        'updated_at',
        'deleted_at',
        'is_approved',
        'slug',
        'rating',
        'featured_seq',
        'num_views',
        'organizations.id',
        'organizations.type',
        'categories.id',
        'contributions.contributor.id',
        'collections.id',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedIncludes = [
        'contributions',
        'collections',
        'categories',
        'team',
        'user',
        'ledgers',
        'contributions.contributor',
        'team.organization',
        'categories',
        'importerCollection',
    ];

    /**
     * @var string[]
     */
    protected $dispatchesEvents = [
        'saved' => SourceSavedEvent::class,
        'softDeleted' => SourceSavedEvent::class,
        'restored' => SourceSavedEvent::class,
        'forceDeleting' => SourceSavedEvent::class,
    ];

    public static string $defaultSort = 'name';

    /**
     * @var array|string[]
     */
    protected array $ignored = [
        'is_approved',
    ];

    protected TranscriptionClient $transcriptionClient;

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'type' => SourceType::class,
            'published_on' => 'date',
            'written_on' => 'date',
            'media_status' => SourceMediaStatus::class,
            'agent_terms_accepted' => 'boolean',
            'agent_active' => 'boolean',
            'agent_hidden' => 'boolean',
            'agent_indexed_at' => 'datetime',
            'social_active' => 'boolean',
            'weight' => 'integer',
            'is_locked' => 'boolean',
            'is_approved' => 'boolean',
            'text_imported_at' => 'datetime',
            'last_imported_at' => 'datetime',
            'featured_seq' => 'integer',
            'inherit_importer_settings' => 'boolean',
            'url_render_js' => 'boolean',
            'social_indexed_at' => 'datetime',
        ];
    }

    protected const CATEGORIZE_TITLE = 'SOURCE INFORMATION';

    protected const CATEGORIZE_BODY = '
        TITLE: {title}
        AUTHOR: {author}
        DESCRIPTION:
        {description}
    ';

    protected const CATEGORY_IDS_CACHE_KEY = 'source.{id}.previous_categories';

    public static function booted(): void
    {

        parent::booted();

        /* TODO:
            There must be a better way to do this. For now, we're caching a source's category IDs prior to sync,
            then checking against that after the sync to see if they've changed, then cascading the categories.
        */

        static::syncing(function (Source $source, $relation, $properties) {
            if ($relation == 'categories') {
                Cache::rememberForever(replace_tokens(static::CATEGORY_IDS_CACHE_KEY, ['id' => $source->id]), function () use ($source) {
                    $oldCategoryIds = $source->categories->pluck('pivot')->pluck('category_id')->toArray();
                    sort($oldCategoryIds);
                    return $oldCategoryIds;
                });
            }
        });

        static::synced(function (Source $source, $relation, $properties) {
            if ($relation == 'categories') {
                $cacheKey = replace_tokens(static::CATEGORY_IDS_CACHE_KEY, ['id' => $source->id]);
                $oldCategoryIds = Cache::get($cacheKey);
                $newCategoryIds = array_map('intval', Arr::pluck($properties, 'category_id'));
                sort($newCategoryIds);
                if ($oldCategoryIds !== $newCategoryIds) {
                    Log::debug('SOURCE CATEGORIES CHANGED');
                    dispatch(new CascadeSourceCategoriesJob($source));
                }
                Cache::forget($cacheKey);
            }
        });

        static::deleting(function (Source $source) {
            Log::debug("SOURCE ARCHIVED: {$source->id}");
            $source->deleteRagDocument(get_rag_client($source->team), true);
            $source->deleteSearchDocument(get_search_client(), true);
        });

        static::forceDeleting(function (Source $source) {
            Log::debug("SOURCE DELETED: {$source->id}");
            $source->deleteRagDocument(get_rag_client($source->team), true);
            $source->deleteSearchDocument(get_search_client(), true);
            $source->contributions()->forceDelete();
            $source->collections()->detach();
        });

    }

    public function setDefaultAttributes(): void
    {
        parent::setDefaultAttributes();
        if (!$this->isDirty('agent_active')) {
            $this->agent_active = false;
        }
        if (!$this->isDirty('agent_terms_accepted')) {
            $this->agent_terms_accepted = $this->agent_active;
        }
    }

    public function contributions(): HasMany
    {
        return $this->hasMany(SourceContribution::class);
    }

    public function contributors(): HasManyThrough
    {
        return $this->hasManyThrough(
            Contributor::class,
            SourceContribution::class,
            'source_id',
            'id',
            'id',
            'contributor_id'
        );
    }

    public function collections(): BelongsToMany
    {
        return $this->belongsToMany(Collection::class);
    }

    public function importerCollection(): BelongsTo
    {
        return $this->belongsTo(Collection::class);
    }

    protected function urlContentSelector(): Attribute
    {
        return $this->getInheritedImporterSetting('url_content_selector');
    }

    protected function urlContentStrip(): Attribute
    {
        return $this->getInheritedImporterSetting('url_content_strip');
    }

    protected function urlImageSelector(): Attribute
    {
        return $this->getInheritedImporterSetting('url_image_selector');
    }

    protected function urlRenderJs(): Attribute
    {
        return $this->getInheritedImporterSetting('url_render_js');
    }

    protected function urlAuthorSelector(): Attribute
    {
        return $this->getInheritedImporterSetting('url_author_selector');
    }

    protected function urlPublishedSelector(): Attribute
    {
        return $this->getInheritedImporterSetting('url_published_selector');
    }

    protected function getInheritedImporterSetting($attr)
    {
        $collectionAttr = "source_{$attr}";
        return Attribute::make(
            get: function ($value) use ($collectionAttr) {
                return $this->inherit_importer_settings ? $this->importerCollection?->$collectionAttr : $value;
            }
        );
    }

    protected function imagePath(): Attribute
    {
        return Attribute::make(
            get: function ($value) {

                // See if any parent collections wants to dictate image handling and use that if so
                $parentCollection = $this->getImageHandlingCollection();
                if (
                    $parentCollection &&
                    (
                        ($parentCollection->source_image_handling == SourceImageHandling::OVERRIDE) ||
                        (
                            ($parentCollection->source_image_handling == SourceImageHandling::INHERIT) &&
                            empty($value)
                        )
                    )
                ) {
                    return $parentCollection->image_path;
                } else {
                    return $value;
                }

            }
        );
    }

    public function imageIsOverridden(): bool
    {
        $parentCollection = $this->getImageHandlingCollection();
        return $parentCollection && ($parentCollection->source_image_handling == SourceImageHandling::OVERRIDE);
    }

    protected function description(): Attribute
    {
        return Attribute::make(
            get: function ($value) {

                if (is_null($value)) {
                    return null;
                }

                // See if any parent collections want sources descriptions to be truncated
                $parentCollection = $this->getEnforceExcerptsCollection();

                return $parentCollection ?
                    truncate_excerpt($value) :
                    $value;
            }
        );
    }

    public function getImageHandlingCollection(): ?Collection
    {
        return $this->collections()
            ->where('source_image_handling', '!=', SourceImageHandling::SELF->value)
            ->orderBy('created_at', 'desc')
            ->first();
    }

    public function getEnforceExcerptsCollection(): ?Collection
    {
        return $this->collections()
            ->where('enforce_source_excerpts', true)
            ->orderBy('created_at', 'desc')
            ->first();
    }

    public function canIndexRag(bool $textSelected = false): bool
    {
        return
            $this->exists &&
            (
                $textSelected ||
                ! is_null($this->text_path)
            ) &&
            ! $this->trashed();
    }

    public function shouldIndexRag(): bool
    {
        return
            $this->canIndexRag()
            && $this->agent_active;
    }

    /**
     * @throws GuzzleException
     */
    public function deleteRagDocument(?VectaraClient $client = null, bool $unindex = false): ResponseInterface
    {

        $client = $client ?? get_rag_client($this->team);
        $res = $client->deleteDocument(strval($this->id));

        // If it was successful, update the agent_indexed_at timestamp
        if (
            $unindex ||
            ($res->getStatusCode() == 404) ||
            (
                ($res->getStatusCode() >= 200) &&
                ($res->getStatusCode() < 300)
            )
        ) {
            $this->updateQuietly(['agent_indexed_at'=>null]);

        // Otherwise send a debug email
        } else {
            send_debug_email(
                "RAG Unindex Failed: {$this->name} [{$this->id}]",
                "{$res->getStatusCode()}: {$res->getBody()->getContents()}"
            );
        }

        return $res;
    }

    /**
     * @throws GuzzleException
     */
    public function indexRagDocument(?VectaraClient $client = null): ResponseInterface
    {

        $client = $client ?? get_rag_client($this->team);

        $authors = array_values($this->getIndirectRelations(
            $this->contributions()->whereNotIn('role', [SourceContributionRole::FOREWORD->value, SourceContributionRole::TRANSLATOR->value]),
            'contributor'
        ));
        if (empty($authors)) {
            $authors = ['Various Authors'];
        }
        $categories = $this->categories()->pluck('categories.id');
        $collections = $this->collections()->pluck('collections.id');
        $contributors = $this->contributors()->pluck('contributors.id');
        $tags = $this->tags()->pluck('tags.id');

        $meta = [
            'title' => $this->name,
            'description' => remove_html($this->description),
            'lang' => $this->language,
            'type' => $this->type,
            'image_url' => get_path_url($this->image_path),
            'referral_url' => $this->referral_url ?? $this->url,
            'authors' => $authors,
            'organization' => $this->team->organization ? $this->team->organization->name : null,
            'categories' => $categories,
            'collections' => $collections,
            'contributors' => $contributors,
            'tags' => $tags,
            'primary_collection' => $this->importer_collection_id,
            'primary_collection_name' => $this->importerCollection?->name,
            'classification' => $this->classification_id,
            'team' => $this->team_id,
            'weight' => $this->weight,
            'is_hidden' => $this->agent_hidden,
            'is_approved' => $this->is_approved,
        ];

//        Log::debug(json_encode($meta));
//        Log::debug(Storage::disk('public')->path($this->text_path));
        $res = $client->indexDocument(strval($this->id), $meta, Storage::disk('public')->get($this->text_path));

        // If it was successful, update the agent_indexed_at timestamp
        if (
            ($res->getStatusCode() == 409) ||
            (
                ($res->getStatusCode() >= 200) &&
                ($res->getStatusCode() < 300)
            )
        ) {
            $this->updateTimestamp('agent_indexed_at');


        // If it was rate limited, try again in 3s
        } else if ($res->getStatusCode() == 429) {
            dispatch(new ManageSourceRagDocumentJob($this))->delay(now()->addSeconds(3));

        // Otherwise send a debug email
        } else {
            send_debug_email(
                "RAG Index Failed: {$this->name} [{$this->id}]",
                "{$res->getStatusCode()}: {$res->getBody()->getContents()}"
            );
        }

        return $res;

    }

    /**
     * @throws GuzzleException
     */
    public function reindexRagDocument(?VectaraClient $client = null, bool $unindex = false): void
    {
        if (is_null($client)) {
            $client = get_rag_client($this->team);
        }
        $this->deleteRagDocument($client, $unindex);
        if ($this->shouldIndexRag()) {
            $this->indexRagDocument($client);
        }
    }

    /**
     * @throws GuzzleException
     */
    public function import(bool $override = false, bool $useProxy = false): bool
    {
        return match ($this->type) {
            SourceType::URL => $this->importFromUrl($override, $useProxy),
            SourceType::EPISODE => $this->importPodcastEpisodeFromUrl($override, $useProxy),
            SourceType::YOUTUBE => $this->importFromYoutube($override, $useProxy),
            SourceType::BOOK => $this->importFromBookProvider($override),
            default => false,
        };
    }

    public function importAsync(bool $override = false, bool $useProxy = false, int $delay = 0): PendingDispatch|PendingClosureDispatch|false
    {
        return match ($this->type) {
            SourceType::URL => dispatch(new ImportUrlSourceDataJob($this, $override, $useProxy))->delay(now()->addSeconds($delay)),
            SourceType::EPISODE => dispatch(new ImportEpisodeSourceDataJob($this, $override, $useProxy))->delay(now()->addSeconds($delay)),
            SourceType::YOUTUBE => dispatch(new ImportYoutubeSourceDataJob($this, $override, $useProxy))->delay(now()->addSeconds($delay)),
            SourceType::BOOK => dispatch(new ImportBookSourceDataJob($this, $override))->delay(now()->addSeconds($delay)),
            default => false,
        };
    }

    public function importFromUrl(bool $override = false, bool $useProxy = false): bool
    {

        if (($this->type == SourceType::URL) && ! is_null($this->url)) {

            // Must use proxy if we want to render JS
            if ($this->url_render_js) {
                $useProxy = true;
            }

            $url = $useProxy ? get_proxied_url($this->url, $this->url_render_js) : $this->url;

//            Log::debug($url);

            Roach::startSpider(
                UrlSpider::class,
                new Overrides(startUrls: [$url]),
                [
                    'model' => $this,
                    'override' => $override,
                    'useProxy' => $useProxy,
                ]
            );

            return true;

        }

        return false;
    }

    public function importPodcastEpisodeFromUrl(bool $override = false, bool $useProxy = false): bool
    {
        if (
            $this->type == SourceType::EPISODE &&
            ! $this->isAutoImported()
        ) {
            increase_timeout('HIGH');
            $url = $useProxy ? get_proxied_url($this->url) : $this->url;
            Roach::startSpider(
                EpisodeSpider::class,
                new Overrides(startUrls: [$url]),
                [
                    'model' => $this,
                    'override' => $override,
                ]
            );

            return true;
        }

        return false;
    }

    public function canConvertDocument(bool $mediaUrlAdded = false, bool $mediaFileAdded = false): bool
    {
        return
            in_array($this->type, [SourceType::BOOK, SourceType::ARTICLE]) &&
            $this->hasValidMedia($mediaUrlAdded, $mediaFileAdded) &&
            !$this->trashed() &&
            $this->hasConvertibleDocument()
        ;
    }

    public function convertFromDocument(bool $newUrl = false): bool
    {

        Log::debug("{$this->name}: ".($this->canConvertDocument() ? 'CAN' : 'CANNOT').' CONVERT');

        if ($this->canConvertDocument()) {

            ConvertApi::setApiCredentials(env('DOCUMENT_CONVERT_API_KEY'));

            // If there is no media file downloaded, there must be a media URL ... download now
            $this->saveMediaFileFromUrl($newUrl);

            // Convert the doc to text
            $docPath = $this->getMediaPath();
            Log::debug("FILE TO CONVERT: {$docPath}");
            $result = ConvertApi::convert(
                'txt',
                ['File' => $docPath],
                get_file_extension($docPath)
            );
            $textPath = $this->saveTextFile(trim($result->getFile()->getContents()));
            $this->updateQuietly(['text_path' => $textPath]);

            $this->extractImageFromDocument();

            return true;

        }

        return false;

    }

    public function extractImageFromDocument(bool $newUrl = false): bool
    {

        Log::debug("{$this->name}: ".($this->canConvertDocument() ? 'CAN' : 'CANNOT').' CONVERT');

        if ($this->canConvertDocument()) {

            ConvertApi::setApiCredentials(env('DOCUMENT_CONVERT_API_KEY'));

            // If there is no media file downloaded, there must be a media URL ... download now
            $this->saveMediaFileFromUrl($newUrl);

            // Attempt to convert the doc's first page to an image
            $docPath = $this->getMediaPath();
            $result = ConvertApi::convert(
                'png',
                [
                    'File' => $docPath,
                    'PageRange' => '1-1',
                ],
                get_file_extension($docPath)
            );
            $imagePath = save_file(
                trim($result->getFile()->getContents()),
                'png',
                'sources/images'
            );
            $this->updateQuietly(['image_path' => $imagePath]);

            return true;

        }

        return false;

    }

    public function saveMediaFileFromUrl(bool $newUrl = false): bool
    {
        if ((empty($this->media_path) && !empty($this->media_url)) || $newUrl) {
            $mediaPath = save_file_from_url($this->media_url, 'sources/media');
            return $this->updateQuietly(['media_path' => $mediaPath]);
        }
        return false;
    }

    protected function getMediaPath()
    {
        return (env('FILESYSTEM_DRIVER') == 'local') ?
            Storage::disk('public')->path($this->media_path) :
            Storage::disk('public')->url($this->media_path)
        ;
    }

    public function canTranscribe(bool $mediaUrlAdded = false, bool $mediaFileAdded = false): bool
    {
        return (
            in_array($this->type, [SourceType::MEDIA, SourceType::YOUTUBE, SourceType::EPISODE]) &&
            (
                $this->hasValidMedia($mediaUrlAdded, $mediaFileAdded) ||
                ($this->type == SourceType::YOUTUBE)
            ) &&
            ! $this->trashed()
        );
    }

    protected function hasValidMedia(bool $mediaUrlAdded = false, bool $mediaFileAdded = false): bool
    {
        return (
            $mediaUrlAdded ||
            $mediaFileAdded ||
            !empty($this->media_url) ||
            !empty($this->media_path)
        );
    }

    protected function hasConvertibleDocument(): bool
    {
        $convertibleTypes = explode(',', env('DOCUMENT_CONVERT_TYPES'));
        return (
            (
                !empty($this->media_url) &&
                in_array(
                    substr(strtolower($this->media_url), strrpos($this->media_url, '.')+1),
                    $convertibleTypes
                )
            ) ||
            (
                !empty($this->media_path) &&
                in_array(
                    substr(strtolower($this->media_path), strrpos($this->media_path, '.')+1),
                    $convertibleTypes
                )
            )
        );
    }

    public function transcribe(): bool
    {
        $this->refresh();
        if (in_array($this->type, [SourceType::YOUTUBE])) {
            return $this->getYoutubeTranscript();
        } else if (in_array($this->type, [SourceType::MEDIA, SourceType::EPISODE])) {
            return $this->transcribeFromMedia();
        }
        return false;
    }

    public function transcribeAsync(int $delay = 0): bool
    {
        if (in_array($this->type, [SourceType::YOUTUBE])) {
            dispatch(new DownloadSourceYoutubeTranscriptJob($this))->delay($delay);
            return true;
        } else if (in_array($this->type, [SourceType::MEDIA, SourceType::EPISODE])) {
            dispatch(new TranscribeSourceMediaJob($this))->delay($delay);
            return true;
        }
        return false;
    }

    public function transcribeFromMedia(): bool
    {

        $this->refresh();
        $transcriptionCredits = env_int('TRANSCRIBE_MEDIA_CREDITS');
        Log::debug("{$this->name}: ".($this->canTranscribe() ? 'CAN' : 'CANNOT').' TRANSCRIBE');
        if (
            $this->canTranscribe() &&
            $this->team->credits()->canSpend($transcriptionCredits) &&
            ($this->media_status !== SourceMediaStatus::PENDING) // only proceed if the transcription isn't already pending
        ) {

            Log::debug("MEDIA FILE: {$this->media_path}");

            $this->updateQuietly(['media_status' => SourceMediaStatus::PENDING]);

            // Use uploaded media if available, otherwise use the URL; otherwise this is an invalid operation and we should bail
            if (! empty($this->media_path)) {
                $url = get_path_url($this->media_path);
            } elseif (! empty($this->media_url)) {
                $url = $this->media_url;
            } else {
                return false;
            }

            $client = $this->getTranscriptionClient();
            $res = $client->transcribe($url, $this->id);

            $success = false;
            $contents = $res->getBody()->getContents();
//            Log::debug($contents);
            if ($res->getStatusCode() == 200) {

                $json = json_decode($contents, true);
                if (isset($json[$client->getField('id')])) {
                    $data = [
                        'media_status' => SourceMediaStatus::PROCESSING,
                        'media_error' => null,
                        'media_process_id' => $json[$client->getField('id')],
                    ];
                    $success = true;
                    dispatch(new ImportSourceTranscriptJob($this, 1))->delay(now()->addMinutes(1));
                }

            }

            if (! $success) {
                $data = [
                    'media_status' => SourceMediaStatus::FAILED,
                    'media_error' => $contents,
//                    'media_process_id' => null,
                ];
            }

            $this->updateQuietly($data);

            return $success;

        }

        return false;

    }

    public function importMediaTranscript(int $backoff = 1): bool|array
    {

        $transcriptionCredits = env_int('TRANSCRIBE_MEDIA_CREDITS');
        Log::debug("{$this->name}: ".($this->canTranscribe() ? 'CHECK' : 'DO NOT CHECK').' TRANSCRIPTION');
        $success = false;
        if ($this->canTranscribe()) {

            $client = $this->getTranscriptionClient();
            $res = $client->query($this->media_process_id);

            $res->getBody()->rewind();
            $contents = $res->getBody()->getContents();
//            Log::debug($contents);
            if ($res->getStatusCode() == 200) {

                $json = json_decode($contents, true);
                if ($client->transcriptIsReady($json)) {

                    $transcript = null;
                    if ($client->getField('txt')) {
                        $transcript = $json[$client->getField('txt')];
                    } elseif ($client->getField('url')) {
                        Log::debug("Downloading file: {$json[$client->getField('url')]} ...");
                        $transcript = file_get_contents($json[$client->getField('url')]);
                    }
                    Log::debug("Transcript File:\n{$transcript}");

                    $textPath = $this->saveTextFile($transcript);
                    $data = [
                        'media_status' => SourceMediaStatus::COMPLETE->value,
                        'media_error' => null,
                        'text_path' => $textPath,
                        'text_imported_at' => get_now_timestamp(),
                    ];
                    $this->team->credits()->spend($transcriptionCredits);

                // If the transcript isn't processing and also didn't succeed, it failed
                } elseif (! $client->transcriptIsProcessing($json)) {

                    $data = [
                        'media_status' => SourceMediaStatus::FAILED->value,
                        'media_error' => $contents,
                        'text_path' => null,
                        'text_imported_at' => null,
                    ];

                // Check again according to the passed backoff
                } else {
                    Log::debug("TRANSCRIPTION PENDING: Check again in {$backoff} minutes.");
                    dispatch(new ImportSourceTranscriptJob($this, $backoff))->delay(now()->addMinutes($backoff));
                }

            // If the server is down or it returned something other than a 200, something else is up. Fail!
            } else {
                $data = [
                    'media_status' => SourceMediaStatus::FAILED->value,
                    'media_error' => $contents,
                    'text_path' => null,
                    'text_imported_at' => null,
                ];
            }

            if (isset($data)) {
                $success = $this->updateQuietly($data);
            }

        }

        return $success;

    }

    public function getYoutubeTranscript(): bool
    {

        $transcriptionCredits = env_int('TRANSCRIBE_YOUTUBE_CREDITS');
        if (
            ($this->type == SourceType::YOUTUBE) &&
            $this->team->credits()->canSpend($transcriptionCredits)
        ) {

            Log::debug('DOWNLOAD YOUTUBE TRANSCRIPT: ' . $this->external_id);
            $transcript = YoutubeTranscriptionClient::getText($this->external_id, [$this->language, env('AGENT_DEFAULT_LANGUAGE')]);
            Log::debug($transcript);
            $textPath = $this->saveTextFile($transcript);
            $success = $this->updateQuietly(['text_path' => $textPath]);
            if ($success) {
                $this->team->credits()->spend($transcriptionCredits);
            }

            dispatch(new GetSourceYoutubeMediaUrlJob($this));

            return $success;

        }

        return false;

    }

    public function getYoutubeMediaUrl(): bool
    {

        Log::debug('GET YOUTUBE MEDIA URL');
        $downloadClient = new YouTubeDownloader;
        try {

//            Log::debug(strtoupper($this->type->value));
//            Log::debug($this->url);
            $links = $downloadClient->getDownloadLinks($this->url);
//            Log::debug(json_encode($links));
            Log::debug('Available Formats: '.json_encode($links->getAllFormats()));

            $audioLinks = $links->getAudioFormats();
            if (! empty($audioLinks)) {

                $qualities = ['HIGH', 'MEDIUM', 'LOW'];
                $formats = ['m4a', 'mp4'];
                foreach ($qualities as $quality) {
                    foreach ($audioLinks as $audioLink) {
                        if ($audioLink->audioQuality == "AUDIO_QUALITY_{$quality}") {
                            foreach ($formats as $format) {
                                if (str_contains($audioLink->mimeType, $format)) {
                                    $bestLink = $audioLink;
                                    break 3;
                                }
                            }
                        }
                    }
                }

                if (! isset($bestLink)) {
                    $bestLink = $audioLinks[0];
                }

            } elseif (! empty($links->getAllFormats())) {
                $bestLink = $links->getFirstCombinedFormat();
            }

            if (isset($bestLink)) {

                Log::debug('BEST MEDIA: '.json_encode($bestLink, JSON_UNESCAPED_SLASHES));
                return $this->updateQuietly([
                    'media_url' => $bestLink->url,
                ]);

            } else {
                Log::debug('No media URLs!');
            }

        } catch (YouTubeException $e) {
            Log::debug("YouTube Media Error: {$e->getMessage()}");
        }

        return false;

    }

    public function hasMediaError(): bool
    {
        return $this->media_status == SourceMediaStatus::FAILED;
    }

    public function getFormattedMediaStatus(): string
    {
        return strtoupper($this->media_status->value);
    }

    public function getMediaError(): ?string
    {
        if ($this->hasMediaError() && ! empty($this->media_error)) {
            return $this->getTranscriptionClient()->getErrorMessage($this->media_error);
        }

        return null;
    }

    public function getTranscriptionClient(): TranscriptionClient
    {
        if (! isset($this->transcriptionClient)) {
            $this->transcriptionClient = new TranscriptionClient(env('TRANSCRIPTION_PROVIDER'), $this->language);
        }

        return $this->transcriptionClient;
    }

    public function importFromYoutube(bool $override = false, bool $useProxy = false): bool
    {

        if (
            $this->type == SourceType::YOUTUBE &&
            (! $this->isImported() || $override)
        ) {

            $videoId = $this->external_id;
            if (is_null($videoId)) {
                $videoId = YoutubeClient::parseVideoIdFromUrl($this->url);
            }
            $video = YoutubeClient::getVideoById($videoId);
//            Log::debug(print_r($video, JSON_PRETTY_PRINT));

            $result = $this->update([
                'name' => html_entity_decode($video->snippet->title),
                'external_id' => $video->id,
                'url' => YoutubeClient::getVideoUrl($video->id),
                'published_on' => $video->snippet->publishedAt,
                'description' => $video->snippet->description,
                'language' => YoutubeClient::getVideoLanguage($video->snippet),
                'last_imported_at' => get_now_timestamp(),
            ]);

            // Update the thumbnail image
            $this->saveImageFromUrl(YoutubeClient::getBestThumbnailUrl($video->snippet->thumbnails));

            return $result;

        }

        return false;

    }

    /**
     * @throws GuzzleException
     */
    public function importFromBookProvider(
        bool $override = false,
        array $data = [],
        bool $aiDescription = false,
        bool $autoCategorize = false,
        bool $autoApprove = false
    ): bool {

        increase_timeout();

        if ($override || empty($data)) {
            $client = new AmazonClient(env('RAINFOREST_API_KEY'), env('AMAZON_ACCOUNT_ID'));
            $data = $client->getBook($this->external_id, env('BOOK_TOPIC_MATCH'));
        }

        // Set Source attributes from book data
        $attrs = [
            'name' => html_entity_decode($data['name']),
            'subtitle' => $data['subtitle'],
            'slug' => Str::slug($data['name']),
            'referral_url' => $data['referral_url'],
            'external_id' => $data['external_id'],
            'rating' => $data['rating'],
            'description' => $data['description'],
            'published_on' => $data['published_on'],
            'last_imported_at' => get_now_timestamp(),
        ];

        if (isset($data['image_url'])) {
            $this->saveImageFromUrl($data['image_url']);
        }

        // If AI descriptions are toggled, rewrite using AI
        $client = new LlmClient();
        if ($aiDescription && ! empty($data['description'])) {
            $attrs['description'] = $client->rewrite($data['description'], 'book summary');
        }
        $attrs['description'] = convert_html($attrs['description']);

        $success = $this->update($attrs);

        if (! $this->categories()->exists() && $autoCategorize) {
            $this->autoCategorize();
        }

        if ($success && $autoApprove) {
            $this->autoApprove();
        }

        // Create authors if they don't exist
        foreach ($data['authors'] as $author) {
            if (isset($author['name'])) {
                $contributor = SourceContribution::firstOrCreateByContributorName($author['name'], $this);
                if (isset($author['link']) && empty($contributor->referral_url)) {
                    $contributor->update(['referral_url' => $author['link']]);
                }
            }
        }

        return $success;

    }

    public function saveTextFile(string $contents): bool|string
    {
        return save_file(
            add_utf8_bom(remove_html($contents)),
            'txt',
            'sources/texts'
        );
    }

    public function isAutoImported(): bool
    {
        $autoImported = false;
        foreach ($this->collections as $collection) {
            if (in_array($collection->type, [CollectionType::RSS, CollectionType::WEBSITE, CollectionType::CHANNEL, CollectionType::PODCAST])) {
                $autoImported = true;
                break;
            }
        }

        return $autoImported;
    }

    public static function getPrefixedName(?string $str = null): string
    {
        if (is_null($str)) {
            $str = Str::ulid();
        }
        return static::TITLE_PLACEHOLDER_PREFIX . $str;
    }

    /**
     * @throws GuzzleException
     */
    public function suggestCategories(?int $numCategories = null, ?array $categories = []): ?array
    {

        $existingCategories = $this->subcategories();
        if (is_null($numCategories)) {
            $numCategories = env_int('MAX_CATEGORIES', 3) - $existingCategories->count();
        }

        if ($numCategories >= 1) {

            if (empty($categories)) {
                $categories = Category::getAssignable();
            }

            // If the source already has categories, remove those from the possible categories
            if ($existingCategories->count() > 0) {
                $existingCategoryIds = $existingCategories->pluck('categories.id')->toArray();
                $categories = Arr::except($categories, $existingCategoryIds);
            }

            $title = $this->name;
            if (! empty($this->subtitle)) {
                $title .= ": {$this->subtitle}";
            }

            $author = ! $this->contributions->isEmpty() ? $this->contributions[0]->contributor->short_name : null;
            $contextBody = replace_tokens(
                static::CATEGORIZE_BODY,
                [
                    'title' => $title,
                    'author' => $author,
                    'description' => $this->description,
                ]
            );

            try {

                $client = new LlmClient();

                $suggestedCategoryIds = $client->suggestCategories(
                    $categories,
                    static::CATEGORIZE_TITLE,
                    $contextBody,
                    $numCategories
                );

            } catch (GuzzleException $e) {
                Log::debug($e->getMessage());
                $suggestedCategoryIds = [];
            }

            return $suggestedCategoryIds;

        } else {
            return [];
        }

    }

    public function autoCategorize(array $categories = []): void
    {
        dispatch(new AutoCategorizeSourceJob($this, $categories));
    }

    public function subcategories(): MorphToMany
    {
        return $this->categories()->whereNotNull('parent_id');
    }

    public function syncContributorCategories(): int // TODO: I'm sure this could be optimized to rely on SQL more
    {
        $contributorIds = $this->contributions()->pluck('contributor_id')->toArray();
        $contributors = Contributor::whereIn('id', $contributorIds)->get();
        foreach ($contributors as $contributor) {
            $sourceIds = $contributor->contributions()->pluck('source_id')->toArray();
            $this->syncModelCategories($contributor, $sourceIds);
        }

        return $contributors->count();
    }

    public function syncCollectionCategories(): int // TODO: I'm sure this could be optimized to rely on SQL more
    {
        $collectionIds = $this->collections()->pluck('collection_id')->toArray();
        $collections = Collection::whereIn('id', $collectionIds)->get();
        foreach ($collections as $collection) {
            $sourceIds = $collection->sources()->pluck('source_id')->toArray();
            $this->syncModelCategories($collection, $sourceIds);
        }

        return $collections->count();
    }

    public function syncOrganizationCategories(): array // TODO: I'm sure this could be optimized to rely on SQL more
    {
        $organization = Organization::where('team_id', $this->team_id)->first();
        if ($organization) {
            $sourceIds = Source::where('team_id', $organization->team_id)->pluck('id')->toArray();

            return $this->syncModelCategories($organization, $sourceIds);
        } else {
            return [];
        }
    }

    protected function syncModelCategories(Model $model, array $sourceIds): array
    {
        $sources = Source::whereIn('id', $sourceIds)->get();
        $categoryIds = [];
        foreach ($sources as $source) {
            $categoryIds = array_merge($categoryIds, $source->categories()->pluck('categories.id')->toArray());
        }

        return $model->categories()->sync($categoryIds);
    }

    public function isImported(): bool
    {
        return ! is_null($this->last_imported_at);
    }

    public function shouldUseImporterSettings(): bool
    {
        return $this->inherit_importer_settings && $this->importerCollection;
    }

    public static function getTagClassName(): string
    {
        return Tag::class;
    }

    public function tags(): MorphToMany
    {
        return $this
            ->morphToMany(self::getTagClassName(), 'taggable', 'taggables', null, 'tag_id')
            ->team()
            ->orderBy('order_column');
    }

}
