<?php

namespace App\Models;

use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Models\Traits\BelongsToTeam;
use App\Models\Traits\CanBeLocked;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Membership extends Model implements Recordable
{
    use BelongsToTeam;
    use CanBeLocked;
    use HasFactory;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'model_type',
        'model_id',
        'team_id',
        'role_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'model_type',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_locked' => 'boolean',
        ];
    }

    public static function booted(): void
    {

        parent::booted();

        static::creating(function (Model $model) {
            $model->model_type = User::class;
        });

        static::created(function (Model $model) {
            $model->user->addToMailingList(env('SENDGRID_TEAM_LIST_ID'));
        });

    }

    public function getNameAttribute(): string
    {
        return $this->model->name;
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    public function model(): BelongsTo
    {
        return $this->belongsTo(User::class, 'model_id')->withTrashed();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'model_id')->withTrashed();
    }

    public function owner(): BelongsTo
    {
        return $this->user();
    }
}
