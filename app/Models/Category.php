<?php

namespace App\Models;

use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Models\Traits\HasFrontendListing;
use App\Models\Traits\HasSlug;
use App\Models\Traits\SyncsToFrontend;
use Cyrilde<PERSON>it\EloquentViewable\Contracts\Viewable;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Staudenmeir\LaravelAdjacencyList\Eloquent\Builder;

class Category extends NestableModel implements Viewable
{
    use HasFactory;
    use HasFrontendListing;
    use HasSlug;
    use InteractsWithViews;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;
    use SyncsToFrontend;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'parent_id',
        'tagline',
        'description',
        'slug',
        'is_active',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFields = [
        'id',
        'name',
        'parent_id',
        'tagline',
        'description',
        'slug',
        'is_active',
        'created_at',
        'updated_at',
        'deleted_at',
        'parent',
        'children',
        'sources',
        'sources.source_id',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedSorts = [
        'id',
        'name',
        'slug',
        'tagline',
        'description',
        'parent_id',
        'created_at',
        'updated_at',
        'deleted_at',
        'num_views',
        'rating',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFilters = [
        'id',
        'name',
        'parent_id',
        'tagline',
        'description',
        'slug',
        'is_active',
        'parent.slug',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedIncludes = [
        'parent',
        'children',
        'children.sources',
        'children.children',
        'children.children.sources',
        'sources',
    ];

    public static string $defaultSort = 'name';

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    public function collections(): MorphToMany
    {
        return $this->morphedByMany(Collection::class, 'categorizable');
    }

    public function sources(): MorphToMany
    {
        return $this->morphedByMany(Source::class, 'categorizable');
    }

    public function contributors(): MorphToMany
    {
        return $this->morphedByMany(Contributor::class, 'categorizable');
    }

    public function discussions(): MorphToMany
    {
        return $this->morphedByMany(Discussion::class, 'categorizable');
    }

    public function organizations(): MorphToMany
    {
        return $this->morphedByMany(Organization::class, 'categorizable');
    }

    public static function getAssignable(): array
    {

        $subcategories = static::join('categories AS c2', 'categories.parent_id', '=', 'c2.id')
            ->select(['categories.id', 'categories.name AS subcategoryName', 'c2.name AS categoryName'])
            ->where('categories.is_active', true)
            ->where('c2.is_active', true)
            ->orderBy('c2.name', 'asc')
            ->orderBy('categories.name', 'asc')
            ->get();

        $categories = [];
        foreach ($subcategories as $subcategory) {
            $categories[$subcategory->id] = $subcategory->subcategoryName;
        }

        return $categories;

    }

    public function scopeActive($query): Builder
    {
        return $query->where('is_active', true);
    }
}
