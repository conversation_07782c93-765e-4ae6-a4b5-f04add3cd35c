<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AgentPrompt extends Model
{

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'language',
        'prompted_at',
        'response_started_at',
        'response_completed_at',
        'prompt_chars',
        'response_chars',
        'prompt_tokens',
        'response_tokens',
        'chat_tokens',
        'agent_id',
        'model_id',
        'agent_token_id',
        'client',
        'is_translated',
        'is_nonbillable',
        'is_billed',
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'prompted_at' => 'timestamp',
            'response_started_at' => 'timestamp',
            'response_completed_at' => 'timestamp',
            'prompt_chars' => 'integer',
            'response_chars' => 'integer',
            'prompt_tokens' => 'integer',
            'response_tokens' => 'integer',
            'chat_tokens' => 'integer',
            'agent_id' => 'integer',
            'model_id' => 'integer',
            'agent_token_id' => 'integer',
            'is_translated' => 'boolean',
            'is_nonbillable' => 'boolean',
            'is_billed' => 'boolean',
            'synced_at' => 'timestamp',
        ];
    }

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

}
