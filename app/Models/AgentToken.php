<?php

namespace App\Models;

use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Models\Traits\SyncsToFrontend;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AgentToken extends Model implements Recordable
{
    use HasFactory;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;
    use SyncsToFrontend;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'token',
        'agent_id',
        'is_active',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'token' => 'encrypted',
            'is_active' => 'boolean',
        ];
    }

    public static function booted(): void
    {

        parent::booted();

        static::creating(function (Model $model) {
            $model->token = env('AGENT_API_TOKEN_PREFIX').Str::random(28);
        });

    }

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * @returns void
     */
    public function syncToFrontend(): void
    {

        $this->pushFieldsToFrontend();
        $this->pruneDeletedFromFrontend();

        DB::connection(env('FRONTEND_DB_CONNECTION'))
            ->table($this->getTable())
            ->where('id', $this->id)
            ->update([
                'token' => $this->token ? DB::raw("crypt('{$this->token}', gen_salt('md5'))") : null,
            ]);

    }
}
