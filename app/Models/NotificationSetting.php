<?php

namespace App\Models;

use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class NotificationSetting extends Model implements Recordable
{
    use HasFactory;
    use RecordsPivotsToLedger;
    use RecordsToLedger;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'type',
        'frequency',
    ];
}
