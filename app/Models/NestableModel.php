<?php

namespace App\Models;

use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Staudenmeir\LaravelAdjacencyList\Eloquent\HasRecursiveRelationships;

class NestableModel extends Model implements Recordable
{
    use HasFactory;
    use HasRecursiveRelationships;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;

    /**
     * @return array[]
     */
    public function getCustomPaths(): array
    {
        return [
            [
                'name' => 'name_nested',
                'column' => 'name',
                'separator' => '.',
            ],
        ];
    }
}
