<?php

namespace App\Models;

use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Enums\AgentModelType;
use App\Models\Traits\SyncsToFrontend;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Spatie\Translatable\HasTranslations;

class AgentIntegration extends Model implements Recordable
{
    use HasFactory;
    use HasTranslations;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;
    use SyncsToFrontend;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'agent_id',
        'platform_id',
        'is_active',
        'account',
        'secret',
        'token',
        'cue',
        'welcome',
        'cta',
        'languages',
        'auto_initialize',
        'param_1',
        'param_2',
        'param_3',
    ];

    /**
     * @var string[]
     */
    public $translatable = [
        'cue',
        'welcome',
        'cta',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedIncludes = [
        'agent',
        'platform',
    ];

    public static string $defaultSort = 'name';

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'cue' => 'json',
            'welcome' => 'json',
            'cta' => 'json',
            'languages' => 'json',
            'secret' => 'encrypted',
            'token' => 'encrypted',
            'auto_initialize' => 'boolean',
        ];
    }

    public function platform(): BelongsTo
    {
        return $this->belongsTo(AgentIntegrationPlatform::class, 'platform_id');
    }

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

}
