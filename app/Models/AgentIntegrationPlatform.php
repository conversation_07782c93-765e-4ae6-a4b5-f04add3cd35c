<?php

namespace App\Models;

use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Models\Traits\SyncsToFrontend;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class AgentIntegrationPlatform extends Model implements Recordable
{
    use HasFactory;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;
    use SyncsToFrontend;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'key',
        'description',
        'is_active',
        'image_path',
        'endpoint_url',
        'languages',
        'has_account',
        'has_secret',
        'has_cue',
        'has_welcome',
        'has_cta',
        'can_auto_initialize',
        'param_1_label',
        'param_2_label',
        'param_3_label',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'has_account' => 'boolean',
            'has_secret' => 'boolean',
            'has_cue' => 'boolean',
            'has_welcome' => 'boolean',
            'has_cta' => 'boolean',
            'can_auto_initialize' => 'boolean',
            'languages' => 'json',
        ];
    }

    public function integrations(): HasMany
    {
        return $this->hasMany(AgentIntegration::class);
    }

}
