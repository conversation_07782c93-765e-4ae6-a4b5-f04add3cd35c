<?php

namespace App\Models;

use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Enums\AgentModelType;
use App\Models\Traits\SyncsToFrontend;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class AgentModel extends Model implements Recordable
{
    use HasFactory;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;
    use SyncsToFrontend;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'key',
        'provider_id',
        'provider_model',
        'type',
        'is_recommended',
        'max_tokens',
        'stop_sequence',
        'strip_sequence',
        'languages',
        'supports_reasoning_effort',
        'supports_verbosity',
        'supports_temperature',
        'supports_top_p',
        'supports_frequency_penalty',
        'supports_presence_penalty',
        'fallback_model_id',
        'input_cost',
        'output_cost',
        'num_credits',
        'seq',
        'is_active',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFields = [
        'id',
        'name',
        'key',
        'provider_id',
        'provider_model',
        'type',
        'is_recommended',
        'max_tokens',
        'stop_sequence',
        'strip_sequence',
        'languages',
        'supports_reasoning_effort',
        'supports_verbosity',
        'supports_temperature',
        'supports_top_p',
        'supports_frequency_penalty',
        'supports_presence_penalty',
        'fallback_model_id',
        'num_credits',
        'is_active',
        'seq',
        'provider.id',
        'provider.name',
        'provider.key',
        'provider.is_active',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedSorts = [
        'id',
        'name',
        'key',
        'type',
        'num_credits',
        'seq',
        'provider.name',
        'provider.key',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFilters = [
        'id',
        'name',
        'key',
        'type',
        'is_active',
        'is_recommended',
        'supports_reasoning_effort',
        'supports_verbosity',
        'supports_temperature',
        'supports_top_p',
        'supports_frequency_penalty',
        'supports_presence_penalty',
        'fallback_model_id',
        'provider_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedIncludes = [
        'provider',
        'fallbackModel',
    ];

    public static string $defaultSort = 'name';

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'type' => AgentModelType::class,
            'is_recommended' => 'boolean',
            'max_tokens' => 'integer',
            'languages' => 'json',
            'input_cost' => 'double',
            'output_cost' => 'double',
            'num_credits' => 'integer',
            'seq' => 'integer',
            'is_active' => 'boolean',
            'supports_reasoning_effort' => 'boolean',
            'supports_verbosity' => 'boolean',
            'supports_temperature' => 'boolean',
            'supports_top_p' => 'boolean',
            'supports_frequency_penalty' => 'boolean',
            'supports_presence_penalty' => 'boolean',
        ];
    }

    public function provider(): BelongsTo
    {
        return $this->belongsTo(AgentModelProvider::class, 'provider_id');
    }

    public function fallbackModel(): BelongsTo
    {
        return $this->belongsTo(AgentModel::class, 'fallback_model_id');
    }

    public function scopeType($query, $type): Builder
    {
        return $query->where('type', $type);
    }

}
