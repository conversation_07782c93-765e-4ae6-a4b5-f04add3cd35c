<?php

namespace App\Models;

use Altek\Accountant\Contracts\Identifiable;
use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Enums\NotificationFrequency;
use App\Enums\NotificationType;
use App\Events\UserCreatedEvent;
use App\Models\Traits\CanBeLocked;
use App\Notifications\AgentDeactivatedEmail;
use App\Notifications\ModelApprovedEmail;
use App\Notifications\ModelChangedEmail;
use App\Notifications\ModelRejectedEmail;
use App\Notifications\ModelSubmittedEmail;
use App\Notifications\MultipleModelsChangedEmail;
use App\Services\Gatekeeper;
use BezhanSalleh\FilamentShield\Traits\HasPanelShield;
use Exception;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasAvatar;
use Filament\Models\Contracts\HasDefaultTenant;
use Filament\Models\Contracts\HasTenants;
use Filament\Panel;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Jeffgreco13\FilamentBreezy\Traits\TwoFactorAuthenticatable;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Sanctum\PersonalAccessToken;
use Rappasoft\LaravelAuthenticationLog\Traits\AuthenticationLoggable;
use RickDBCN\FilamentEmail\Models\Email;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements FilamentUser, HasAvatar, HasDefaultTenant, HasTenants, Identifiable, MustVerifyEmail, Recordable
{
    use AuthenticationLoggable;
    use CanBeLocked;
    use HasApiTokens;
    use HasFactory;
    use HasPanelShield;
    use HasRoles;
    use Notifiable;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;
    use TwoFactorAuthenticatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'current_team_id',
        'password',
        'avatar_path',
        'api_token',
        'is_locked',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'api_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFields = [
        'id',
        'name',
        'email',
        'current_team_id',
        'avatar_path',
        'is_locked',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedSorts = [
        'id',
        'name',
        'email',
        'current_team_id',
        'avatar_path',
        'is_locked',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFilters = [
        'id',
        'name',
        'email',
        'current_team_id',
        'avatar_path',
        'is_locked',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedIncludes = [
        'teams',
        'currentTeam',
        'ownedTeams',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'timestamp',
            'is_locked' => 'boolean',
        ];
    }

    /**
     * @var string[]
     */
    protected $dispatchesEvents = [
        'created' => UserCreatedEvent::class,
    ];

    public static function booted(): void
    {

        static::forceDeleting(function ($model) {
            $model->memberships()->forceDelete();
        });

        parent::booted();

    }

    public function getTenants(Panel $panel): Collection
    {
        return $this->teams;
    }

    public function teams(): BelongsToMany
    {
        return $this->belongsToMany(Team::class, 'memberships', 'model_id');
    }

    public function hasTeams(): bool
    {
        return $this->teams()->count();
    }

    public function getFirstActiveTeam(): Team
    {
        return $this->teams()->first();
    }

    public function getDefaultTenant(Panel $panel): ?Model
    {
        return $this->currentTeam;
    }

    /**
     * @throws \Exception
     */
    public function canAccessPanel(Panel $panel): bool
    {

        // All users can access the ID and API panels; admins can access all panels
        if (
            in_array($panel->getId(), ['id', env('APP_API_VERSION', 'api')]) ||
            Gatekeeper::hasAdminAccess($this->id)
        ) {
            return true;

        // Ignite: User must have teams to access
        } else if ($panel->getId() == 'app') {
            return $this->hasTeams();
        }

        return false;

    }

    public function canAccessTenant(Model|int $tenant): bool
    {
        if (! is_int($tenant)) {
            $tenant = $tenant->id;
        }
        $nonTeamRoleIds = Role::where('is_team', false)->pluck('id')->toArray();

        return
            Team::find($tenant)
            && Membership::where('model_type', \App\Models\User::class)
                ->where('model_id', $this->id)
                ->where(function (Builder $qry) use ($tenant, $nonTeamRoleIds) {
                    $qry->where('team_id', $tenant)
                        ->orWhereIn('role_id', $nonTeamRoleIds);
                })
                ->exists();
    }

    public function switchTenant(int $tenant): int
    {
        if ($this->canAccessTenant($tenant)) {
            $this->update(['current_team_id' => $tenant]);
        } else {
            $tenant = $this->current_team_id;
        }

        return $tenant;
    }

    /**
     * @return true
     */
    public function canImpersonate(): bool
    {
        return Membership::where('model_type', \App\Models\User::class)
            ->where('model_id', $this->id)
            ->where('role_id', Gatekeeper::getSuperadminRoleId())
            ->exists();
    }

    /**
     * @return true
     */
    public function canBeImpersonated(): bool
    {
        return Membership::where('model_type', \App\Models\User::class)
            ->where('model_id', $this->id)
            ->where('role_id', Gatekeeper::getSuperadminRoleId())
            ->doesntExist();
    }

    public function getFilamentAvatarUrl(): ?string
    {
        return $this->avatar_path ? Storage::disk('public')->url($this->avatar_path) : null;
    }

    public function ownedTeams(): HasMany
    {
        return $this->hasMany(Team::class);
    }

    public function ownsTeam(Team $team): bool
    {
        return ($this->id === $team->user_id);
    }

    public function currentTeam(): BelongsTo
    {
        return $this->belongsTo(Team::class, 'current_team_id')->withTrashed();
    }

    public function memberships(): HasMany
    {
        return $this->hasMany(Membership::class, 'model_id');
    }

    public function currentTeamMembership(): HasOneThrough
    {
        return $this->hasOneThrough(Membership::class, static::class, 'id', 'model_id')
            ->where(function (Builder $qry) {
                $qry->where('role_id', Gatekeeper::getSuperadminRoleId())
                    ->orWhere('team_id', $this->current_team_id);
            });
    }

    /**
     * {@inheritdoc}
     */
    public function getIdentifier()
    {
        return $this->getKey();
    }

    public function emailLogs(): HasMany
    {
        return $this->hasMany(Email::class);
    }

    /**
     * Scope a query to superadmins.
     */
    public function scopeSuperadmins(Builder $query): void
    {
        $query->join('memberships', 'users.id', 'memberships.model_id')
            ->where('memberships.role_id', Gatekeeper::getSuperadminRoleId())
            ->select('users.*');
    }

    public function sendModelSubmissionNotification(Model $model): void
    {
        $this->notify(new ModelSubmittedEmail($model));
    }

    public function sendModelApprovalNotification(Model $model): void
    {
        $this->notify(new ModelApprovedEmail($model));
    }

    public function sendModelRejectionNotification(Model $model, string $comment): void
    {
        $this->notify(new ModelRejectedEmail($model, $comment));
    }

    public function sendModelChangeNotification($event): void
    {
        $this->notify(new ModelChangedEmail($event));
    }

    public function sendMultipleModelsChangedEmail(array $ledgers, string $title): void
    {
        $this->notify(new MultipleModelsChangedEmail($ledgers, $title));
    }

    public function sendAgentDeactivatedEmail(Agent $agent): void
    {
        $this->notify(new AgentDeactivatedEmail($agent));
    }

    /**
     * @return array
     */
    /*public function toArray(): array
    {
        return ApiService::transformResource(parent::toArray(), $this);
    }*/

    public function getApiToken(string $name): ?PersonalAccessToken
    {
        return $this->tokens()
            ->where('name', $name)
            ->whereNotNull('expires_at')
//            ->where('expires_at', '>', now())
            ->first();
    }

    public function getIgnored(): array
    {
        return [];
    }

    public function notificationSettings(): HasMany
    {
        return $this->hasMany(NotificationSetting::class);
    }

    public function getNotificationFrequency(string $type): string
    {
        return $this->notificationSettings()->firstOrCreate([
            'type' => $type,
        ])->refresh()->frequency;
    }

    /**
     * Scope a query to users that have a notification type frequency.
     */
    public function scopeNotifyFrequency(Builder $query, NotificationType|string $type, NotificationFrequency|string $frequency): void
    {
        if (! is_string($type)) {
            $type = $type->value;
        }
        if (! is_string($frequency)) {
            $frequency = $frequency->value;
        }
        $query->leftJoin('notification_settings', 'users.id', 'notification_settings.user_id')
            ->where('notification_settings.type', $type)
            ->where('notification_settings.frequency', $frequency);
    }

    public function scopeNotifyNow(Builder $query, NotificationType $type): void
    {
        $query->notifyFrequency($type, NotificationFrequency::IMMEDIATELY);
    }

    public function addToMailingList(string $listId): void
    {

        $sg = new \SendGrid(env('SENDGRID_API_KEY'));

        $nameParts = explode(' ', $this->name);
        $data = [
            'contacts' => [
                [
                    'first_name' => $nameParts[0],
                    'last_name' => trim(str_replace($nameParts[0], '', $this->name)),
                    'email' => $this->email,
                ]
            ],
            'list_ids' => [$listId],
        ];

        try {
            $response = $sg->client
                ->marketing()
                ->contacts()
                ->put($data)
            ;
            if ($response->statusCode() == 202) {
                Log::debug("SUCCESSFULLY ADDED CONTACT TO LIST {$listId}: {$this->email}");
            }
        } catch (Exception $e) {
            Log::debug("FAILED TO ADD CONTACT LIST {$listId}: {$this->email}");
        }

    }

    public function removeFromMailingList(string $listId): void
    {

        $sg = new \SendGrid(env('SENDGRID_API_KEY'));

        $data = [
            'emails' => [$this->email],
        ];
        try {
            $response = $sg->client
                ->marketing()
                ->contacts()
                ->search()
                ->emails()
                ->post($data)
            ;
            $contact = json_decode($response->body(), true);
            $contactId = $contact['result'][$this->email]['contact']['id'];
        } catch (Exception $e) {
            Log::debug("FAILED TO GET CONTACT: {$e->getMessage()}");
        }

        $data = [
            'contact_ids' => [$contactId],
        ];
        try {
            $response = $sg->client
                ->marketing()
                ->lists()
                ->_($listId)
                ->contacts()
                ->delete(null, $data)
            ;
            if ($response->statusCode() == 202) {
                Log::debug("SUCCESSFULLY REMOVED CONTACT TO LIST {$listId}: {$this->email}");
            }
        } catch (Exception $e) {
            Log::error("FAILED TO REMOVE CONTACT FROM LIST {$listId}: {$this->email}");
        }

    }

}
