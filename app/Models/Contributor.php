<?php

namespace App\Models;

use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Enums\ContributorTitle;
use App\Enums\SourceContributionRole;
use App\Models\Traits\BelongsToTeam;
use App\Models\Traits\BelongsToUser;
use App\Models\Traits\CanBeLocked;
use App\Models\Traits\CanBeModerated;
use App\Models\Traits\HasCategories;
use App\Models\Traits\HasFrontendListing;
use App\Models\Traits\HasSlug;
use CyrildeWit\EloquentViewable\Contracts\Viewable;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use RingleSoft\LaravelProcessApproval\Contracts\ApprovableModel;
use willvincent\Rateable\Rateable;

class Contributor extends Model implements ApprovableModel, Recordable, Viewable
{
    use BelongsToTeam;
    use BelongsToUser;
    use CanBeLocked;
    use CanBeModerated;
    use HasCategories;
    use HasFactory;
    use HasFrontendListing;
    use HasSlug;
    use InteractsWithViews;
    use Rateable;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'first_name',
        'last_name',
        'suffix',
        'bio',
        'born_in',
        'died_in',
        'referral_url',
        'avatar_path',
        'is_controversial',
        'is_approved',
        'is_locked',
        'merged_contributor_id',
        'social_active',
        'social_indexed_at',
        'team_id',
        'user_id',
        'slug',
        'rating',
        'featured_seq',
        'num_views',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFields = [
        'id',
        'title',
        'first_name',
        'last_name',
        'suffix',
        'name',
        'bio',
        'born_in',
        'died_in',
        'referral_url',
        'avatar_path',
        'is_controversial',
        'team_id',
        'user_id',
        'created_at',
        'updated_at',
        'deleted_at',
        'is_approved',
        'social_active',
        'social_indexed_at',
        'is_locked',
        'merged_contributor_id',
        'slug',
        'rating',
        'featured_seq',
        'num_views',
        'organizations.id',
        'organizations.name',
        'organizations.type',
        'organizations.slug',
        'categories.id',
        'categories.name',
        'teams.id',
        'contributions.id',
        'contributions.role',
        'contributions.source_id',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedSorts = [
        'id',
        'title',
        'first_name',
        'last_name',
        'suffix',
        'name',
        'bio',
        'born_in',
        'died_in',
        'referral_url',
        'social_indexed_at',
        'avatar_path',
        'team_id',
        'user_id',
        'created_at',
        'updated_at',
        'deleted_at',
        'slug',
        'rating',
        'featured_seq',
        'num_views',
        'num_views',
        'source_contributions.seq',
        'contributions.seq',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedFilters = [
        'id',
        'title',
        'first_name',
        'last_name',
        'suffix',
        'name',
        'bio',
        'born_in',
        'died_in',
        'referral_url',
        'avatar_path',
        'is_controversial',
        'team_id',
        'user_id',
        'slug',
        'rating',
        'featured_seq',
        'num_views',
        'created_at',
        'updated_at',
        'deleted_at',
        'is_approved',
        'is_locked',
        'merged_contributor_id',
        'social_active',
        'social_indexed_at',
        'organizations.id',
        'organizations.type',
        'categories.id',
        'contributions.role',
        'contributions.source_id',
    ];

    /**
     * API
     *
     * @var array|string[]
     */
    public static array $allowedIncludes = [
        'team',
        'user',
        'categories',
        'team.organization',
        'contributions',
    ];

    public static string $defaultSort = 'last_name,first_name,title,suffix';

    /**
     * @var array|string[]
     */
    protected array $ignored = [
        'is_approved',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'title' => ContributorTitle::class,
            'born_in' => 'integer',
            'died_in' => 'integer',
            'is_approved' => 'boolean',
            'is_controversial' => 'boolean',
            'featured_seq' => 'integer',
            'is_locked' => 'boolean',
            'social_active' => 'boolean',
            'social_indexed_at' => 'datetime',
        ];
    }

    /**
     * The "booted" method of the model.
     */
    public static function booted(): void
    {

        static::saving(function (Contributor $contributor) {
            $contributor->name = '';
            if (! empty($contributor->title)) {
                $contributor->name .= "{$contributor->title->value} ";
            }
            $contributor->name .= "{$contributor->first_name} {$contributor->last_name}";
            if (! empty($contributor->suffix)) {
                $contributor->name .= ", {$contributor->suffix}";
            }
        });

        static::forceDeleting(function ($model) {
            $model->contributions()->forceDelete();
        });

        parent::booted();

    }

    public function getShortNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    public function contributions(): HasMany
    {
        return $this->hasMany(SourceContribution::class);
    }

    public function authored(): HasMany
    {
        return $this->hasMany(SourceContribution::class)
            ->where('role', SourceContributionRole::AUTHOR);
    }

    public function nonauthored(): HasMany
    {
        return $this->hasMany(SourceContribution::class)
            ->where('role', '!=', SourceContributionRole::AUTHOR);
    }

    public function translated(): HasMany
    {
        return $this->hasMany(SourceContribution::class)
            ->where('role', SourceContributionRole::TRANSLATOR);
    }

    public function edited(): HasMany
    {
        return $this->hasMany(SourceContribution::class)
            ->where('role', SourceContributionRole::EDITOR);
    }

    public function commentated(): HasMany
    {
        return $this->hasMany(SourceContribution::class)
            ->where('role', SourceContributionRole::COMMENTATOR);
    }

    public function foreworded(): HasMany
    {
        return $this->hasMany(SourceContribution::class)
            ->where('role', SourceContributionRole::FOREWORD);
    }

    public function mergedContributor(): HasOne
    {
        return $this->hasOne(Contributor::class, 'id', 'merged_contributor_id');
    }

    public static function firstOrCreateByName(string $name, int $teamId, int $userId): ?Contributor
    {

        $parsedName = static::parseName($name);
        $firstNameParts = explode(' ', $parsedName['first_name']);
        $nameToMatch = str_replace('.', '', strtolower($firstNameParts[0]).'%'.strtolower($parsedName['last_name']));
        $existingContributor = Contributor::where(DB::raw("REPLACE(LOWER(name), '.', '')"), 'like', "%{$nameToMatch}%")
//            ->where('team_id', $teamId)
            ->withTrashed()
            ->first();

        if ($existingContributor) {
            if ($existingContributor->mergedContributor) {
                $existingContributor = $existingContributor->mergedContributor;
            }

            return $existingContributor;
        } else {
            return Contributor::create(array_merge(
                $parsedName,
                [
                    'slug' => Str::slug(trim(implode(
                        ' ',
                        array_filter([
                            $parsedName['title'],
                            $parsedName['first_name'],
                            $parsedName['last_name'],
                            $parsedName['suffix'],
                        ])
                    ))),
                    'team_id' => $teamId,
                    'user_id' => $userId,
                ]
            ));
        }

    }

    public static function parseName(string $name): array
    {

        // Strip out 'by' if the name starts with it
        if (str_starts_with(strtolower($name), 'by ')) {
            $name = substr($name, 3);
        }

        // Parse out the suffix
        $suffix = null;
        $commaPos = strrpos($name, ',');
        if ($commaPos !== false) {
            $suffix = trim(str_replace('.', '', substr($name, $commaPos + 1)));
            $name = trim(substr($name, 0, $commaPos));
        }

        // Parse out the title
        $title = null;
        foreach (ContributorTitle::asOptions() as $value => $label) {
            $value .= ' ';
            $label .= ' ';
            if (str_contains($name, $value) || str_contains($name, $label)) {
                $title = trim($value);
                $name = trim(str_replace([$value, $label], '', $name));
                break;
            }
        }

        // Parse out the first/last names
        $spacePos = strrpos($name, ' ');
        $firstName = substr($name, 0, $spacePos);
        $lastName = substr($name, $spacePos + 1);

        // Make sure the initials are properly formatted
        $oldWords = preg_split('/( |\.)/', $firstName);
        $newWords = [];
        foreach ($oldWords as $word) {
            $str = trim($word);
            if (! empty($str)) {
                if (strlen($word) == 1) {
                    $str .= '.';
                }
                $newWords[] = $str;
            }
        }
        $firstName = trim(implode(' ', $newWords));

        return [
            'title' => $title,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'suffix' => $suffix,
        ];

    }

    public function getSlug(): string
    {
        return Str::slug($this->name);
    }
}
