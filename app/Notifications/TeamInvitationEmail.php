<?php

namespace App\Notifications;

use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasTags;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\HtmlString;

class TeamInvitationEmail extends Notification implements ShouldQueue
{
    use HasTags, Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct() {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {

        $url = URL::temporarySignedRoute(
            'filament.app.pages.invitation',
            now()->addDays(3),
            [
                'tenant' => 'team',
                'id' => $notifiable->getKey(),
                'hash' => sha1($notifiable->email),
            ]
        );

        $team = $notifiable->team;
        $sender = $team->owner;

        return (new MailMessage)
            ->subject("Join the {$team->name} Team on Apologist Ignite")
            ->replyTo($sender->email, $sender->name)
            ->greeting("Join the {$team->name} Team on Apologist Ignite")
            ->line(new HtmlString(nl2br($notifiable->body)))
            ->action('Accept Invitation', $url)
            ->line('If you do not wish to accept the invitation, no further action is required.');

    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::NOTIFY, JobCategoryTag::TEAM]);
    }
}
