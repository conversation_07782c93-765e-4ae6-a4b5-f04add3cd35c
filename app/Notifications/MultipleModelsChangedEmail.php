<?php

namespace App\Notifications;

use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasTags;
use App\Notifications\Traits\ShowsModelChanges;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\HtmlString;

class MultipleModelsChangedEmail extends Notification implements ShouldQueue
{
    use HasTags, Queueable, ShowsModelChanges;

    /**
     * Create a new notification instance.
     */
    public function __construct(public array $ledgers, public string $title)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {

        $notification = (new MailMessage)
            ->subject($this->title)
            ->greeting($this->title)
            ->line(new HtmlString('<br>'));

        foreach ($this->ledgers as $ledger) {
            $notification = $this->getModelChangeSummary($notification, $ledger, false)->line(new HtmlString('<br><br>'));
        }

        return $notification;

    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::NOTIFY, JobCategoryTag::AUDIT]);
    }
}
