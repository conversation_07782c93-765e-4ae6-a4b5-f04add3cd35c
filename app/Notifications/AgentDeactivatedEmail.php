<?php

namespace App\Notifications;

use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasTags;
use App\Models\Agent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AgentDeactivatedEmail extends Notification implements ShouldQueue
{
    use HasTags, Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(public Agent $agent)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {

        $buyCreditsUrl = route('spike.purchase');

        $title = "Agent Deactivated: {$this->agent->name}";
        $msg = "
            The \"{$this->agent->name}\" Agent has been deactivated due to insufficient credits.
            You have chosen a \"pay-as-you-go\" pricing model, which means you must prepay for all credits.
            To get your Agent back online, you can either purchase more credits or disable the \"pay-as-you-go\" Team option to just get billed retroactively for credits your Agent uses — then reactivate the Agent.
        ";

        return (new MailMessage)
            ->subject($title)
            ->greeting($title)
            ->line($msg)
            ->action("Buy Credits", $buyCreditsUrl);

    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::NOTIFY, JobCategoryTag::BILLING], $this->agent);
    }
}
