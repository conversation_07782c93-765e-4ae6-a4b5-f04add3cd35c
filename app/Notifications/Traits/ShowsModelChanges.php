<?php

namespace App\Notifications\Traits;

use Altek\Accountant\Models\Ledger;
use App\Models\Membership;
use App\Models\Model;
use App\Models\SourceContribution;
use App\Models\Team;
use App\Models\TeamInvitation;
use App\Services\Auditor;
use App\Services\Gatekeeper;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

trait ShowsModelChanges
{
    /**
     * @var array|string[]
     */
    public const SUBSTITUTE_PARENT_MODELS = [
        Membership::class => 'team',
        SourceContribution::class => 'source',
        TeamInvitation::class => 'team',
    ];

    /**
     * @var array|string[]
     */
    public const UPDATE_ACTIONS = [
        'updated',
        'toggled',
        'synced',
        'existingPivotUpdated',
        'attached',
        'detached',
    ];

    protected function getModelChangeSummary(MailMessage $notification, Ledger $ledger, bool $showAction = true): MailMessage
    {

        $model = $ledger->recordable;
        $action = Auditor::getActionLabel($ledger->event, $model);
        $actionLower = Str::lower($action);
        $modelLabel = Auditor::getModelLabel($model);
        $editUrl = $this->getEditUrl($model);

        $line = "The <a href='{$editUrl}'><strong>{$model->name}</strong></a> {$modelLabel} has been {$actionLower} by <strong>{$ledger->user->name}</strong>";
        $substitutedModel = $this->getSubstitutedModel($model);
        if (Gatekeeper::isTeamOwnable($substitutedModel)) {
            $line .= " in the <strong>{$substitutedModel->team->name}</strong> Team";
        }
        $line .= '.';
        $notification->line(new HtmlString($line));

        $fields = Auditor::getModifiedFields($ledger);
        if (! empty($fields)) {
            if ($ledger->event == 'created') {
                $notification->line($this->getValuesList($fields));
            } elseif (in_array($ledger->event, static::UPDATE_ACTIONS)) {
                $notification->line($this->getChangesTable($fields));
            }
        }

        if ($ledger->event == 'forceDeleted') {
            $notification->line('This action cannot be undone.');
        } elseif ($showAction) {
            $notification->action("Review {$modelLabel}", $editUrl);
        }

        return $notification;

    }

    protected function getEditUrl(Model $model): string
    {

        $model = $this->getSubstitutedModel($model);

        $modelLabel = Auditor::getModelLabel($model);
        $resourcePlural = Str::plural(Str::lower($modelLabel));
        $route = "filament.app.resources.{$resourcePlural}.edit";

        $teamId = ($model::class == Team::class) ? $model->id : $model->team_id;

        return route($route, ['tenant' => $teamId, 'record' => $model->id]);

    }

    protected function getSubstitutedModel(Model $model): Model
    {
        if (isset(static::SUBSTITUTE_PARENT_MODELS[$model::class])) {
            $model = $model->{static::SUBSTITUTE_PARENT_MODELS[$model::class]};
        }

        return $model;
    }

    protected function getValuesList(array $fields): HtmlString
    {
        $list = '<dl>';
        foreach ($fields as $field => $changed) {
            if (is_array($changed['to'])) {
                $changed['to'] = json_encode($changed['to']);
            }
            $list .= "<dt style='float: left; clear: left; min-width: 6rem;'><strong>{$changed['label']}:</strong></dt><dd style='margin-left: 7rem;'>{$changed['to']}</dd>";
        }
        $list .= '</dl>';

        return new HtmlString($list);
    }

    protected function getChangesTable(array $fields): HtmlString
    {
        $table = '<table
            width="100%"
            cellpadding="8"
            cellspacing="0"
            border="1"
            role="presentation"
            style="
                box-sizing: border-box;
                font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, Helvetica, Arial, sans-serif, \'Apple Color Emoji\', \'Segoe UI Emoji\', \'Segoe UI Symbol\';
                position: relative;
                -premailer-cellpadding: 8px;
                -premailer-cellspacing: 0;
                -premailer-width: 100%;
                margin: 0;
                padding: 0;
                width: 100%;
                text-align: left;
                border-collapse: collapse;
                border-color: #9ca3af;
            "
        >
            <thead>
                <tr>
                    <th>&nbsp;</th>
                    <th><strong>From</strong></th>
                    <th><strong>To</strong></th>
                </tr>
            </thead>
            <tbody>
        ';
        foreach ($fields as $field => $changed) {
            if (is_array($changed['from'])) {
                $changed['from'] = implode(', ', $changed['from']);
            }
            if (is_array($changed['to'])) {
                $changed['to'] = implode(', ', $changed['to']);
            }
            $table .= "<tr><th><strong>{$changed['label']}</strong><td>{$changed['from']}</td><td>{$changed['to']}</td></th>";
        }
        $table .= '</tbody></table>';

        return new HtmlString($table);
    }
}
