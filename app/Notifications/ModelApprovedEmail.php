<?php

namespace App\Notifications;

use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasTags;
use App\Models\Model;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Str;

class ModelApprovedEmail extends Notification implements ShouldQueue
{
    use HasTags, Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(public Model $model)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {

        $modelLabel = class_basename($this->model);
        $resourcePlural = Str::plural(Str::lower($modelLabel));
        $route = "filament.app.resources.{$resourcePlural}.edit";
        $editUrl = route($route, ['tenant' => $this->model->team_id, 'record' => $this->model->id]);
        $title = "{$modelLabel} Approved: {$this->model->name}";

        return (new MailMessage)
            ->subject($title)
            ->greeting($title)
            ->line("
                Congratulations, the {$modelLabel} you submitted has been approved!
                It is now visible to users outside your team.
            ")
            ->action("Review {$modelLabel}", $editUrl);

    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::NOTIFY, JobCategoryTag::WORKFLOW], $this->model);
    }
}
