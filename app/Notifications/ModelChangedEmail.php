<?php

namespace App\Notifications;

use Altek\Accountant\Events\Recorded;
use App\Enums\JobCategoryTag;
use App\Jobs\Traits\HasTags;
use App\Notifications\Traits\ShowsModelChanges;
use App\Services\Auditor;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Str;

class ModelChangedEmail extends Notification implements ShouldQueue
{
    use HasTags, Queueable, ShowsModelChanges;

    /**
     * Create a new notification instance.
     */
    public function __construct(public Recorded $recorded)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {

        $ledger = $this->recorded->ledger;
        $model = $this->recorded->model;
        $action = Auditor::getActionLabel($ledger->event, $model);

        $modelLabel = Auditor::getModelLabel($model);
        $title = Str::limit("{$modelLabel} {$action}: {$model->name}", 250);

        $notification = (new MailMessage)
            ->subject($title)
            ->greeting($title);

        return $this->getModelChangeSummary($notification, $ledger);

    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return $this->generateTags([JobCategoryTag::NOTIFY, JobCategoryTag::AUDIT], $this->recorded->model);
    }
}
