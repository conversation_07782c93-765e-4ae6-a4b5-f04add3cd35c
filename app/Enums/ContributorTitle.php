<?php

namespace App\Enums;

enum ContributorTitle: string
{
    use Enum;

    case ARCHBISHOP = 'Abp.';
    case BISHOP = 'Bp.';
    case BLESSED = 'Bl.';
    case BROTHER = 'Br.';
    case DEACON = 'Dcn.';
    case DOCTOR = 'Dr.';
    case FATHER = 'Fr.';
    case LADY = 'Lady';
    case LORD = 'Lord';
    case PATRIARCH = 'Patr.';
    case POPE = 'Pp.';
    case PRIEST = 'Pr.';
    case REVEREND = 'Rev.';
    case SAINT = 'St.';
    case SUBDEACON = 'Sdn.';
    case SIR = 'Sir';
    case SISTER = 'Sr.';

}
