<?php

namespace App\Enums;

enum JobCategoryTag: string
{
    use Enum;

    case AUDIT = 'audit';
    case BACKUP = 'backup';
    case CATEGORIZE = 'categorize';
    case IMPORT = 'import';
    case INDEX = 'index';
    case MEDIA = 'media';
    case NOTIFY = 'notify';
    case PUBLISH = 'publish';
    case RAG = 'rag';
    case SEARCH = 'search';
    case SYNC = 'sync';
    case TEAM = 'team';
    case USER = 'user';
    case TRANSCRIBE = 'transcribe';
    case CONVERT = 'convert';
    case WORKFLOW = 'workflow';
    case TRANSLATE = 'translate';

    case BILLING = 'billing';

}
