<?php

namespace App\Enums;

use Carbon\Carbon;

enum NotificationFrequency: string
{
    use Enum;

    const __default = self::DAILY;

    const HOURS_MAP = [
        self::NEVER->value => -1,
        self::WEEKLY->value => 168,
        self::DAILY->value => 24,
        self::IMMEDIATELY->value => 0,
    ];

    case NEVER = 'never';
    case WEEKLY = 'weekly';
    case DAILY = 'daily';
    case IMMEDIATELY = 'immediately';

    public static function getThreshold(NotificationFrequency|string $type): Carbon
    {
        if (! is_string($type)) {
            $type = $type->value;
        }

        return Carbon::now()->subHours(self::HOURS_MAP[$type]);
    }
}
