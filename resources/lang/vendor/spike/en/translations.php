<?php

return [
    'credits' => 'credit|credits',
    'add_payment_card' => 'Add Payment Card',
    'add_payment_card_subtitle' => 'Add a new payment card for future purchases and/or subscriptions',
    'close_modal' => 'Close Modal',
    'cardholder_name' => 'Cardholder Name',
    'cardholder_name_placeholder' => 'Your Full Name',
    'add_card' => 'Add card',
    'billing' => 'Manage Payments',
    'checkout' => 'Checkout',
    'credits_in_cart' => 'Credits in your shopping cart',
    'remove' => 'Remove',
    'empty_cart_description' => 'Your shopping cart is empty.',
    'order_summary' => 'Order Summary',
    'credits_to_be_added' => 'Total credits to be added',
    'order_total' => 'Order Total',
    'using_payment_card' => 'Using payment card :last_four',
    'using_payment_method' => 'Using payment method :name',
    'loading' => 'Loading...',
    'setup_payment_card' => 'Click here to set up a payment card',
    'pay' => 'Pay',
    'credit_transactions' => 'Credit Transactions',
    'credit_transactions_description' => 'You can see the full history of your credit usage and adjustments here.',
    'expired_on' => 'expired on :date',
    'credit_transactions_empty' => 'You have no credit transactions yet. They will appear here once you purchase or spend credits.',
    'prorated_from_to_credits' => 'Prorated from :from to :to credits',
    'credits_used_on_date' => 'Credits used on :date',
    'until_time' => 'until :time',
    'invoices' => 'Invoices',
    'invoices_description' => 'You can view and download your invoices here.',
    'invoices_empty' => 'You have no invoices yet. They will appear here once you make a purchase.',
    'download' => 'Download',
    'close_sidebar' => 'Close Sidebar',
    'open_sidebar' => 'Open Sidebar',
    'usage' => 'Monitor Usage',
    'daily_credit_usage' => 'Daily Billable Usage',
    'daily_credit_usage_empty' => 'There is no data to display at the moment.',
    'return_to_app' => 'Return to Apologist Ignite',
    'showing_from_to_results' => 'Showing :from to :to of :total results',
    'previous' => '« Previous',
    'next' => 'Next »',
    'payment_cards' => 'Payment Cards',
    'payment_cards_description' => 'You can view and manage your payment cards here.',
    'add_new_card' => 'Add New Card',
    'card_ending' => ':card_brand ending in :last_four',
    'expires_on_date' => 'Expires on :date',
    'default' => 'Default',
    'make_default' => 'Make Default',
    'payment_cards_empty' => 'You have no payment cards added yet. Please add a new card to purchase credits.',
    'products' => 'Credit Bundles',
    'products_description' => 'You can pre-purchase credits to use. More credits will automatically be purchased if you run out of them.',
    'cart_total' => 'Cart total: :price',
    'credits_in_cart_for_price' => ':credits credits in cart for :price',
    'thank_you' => 'Thank You',
    'payment_successful' => 'Payment Successful',
    'thanks_for_ordering' => 'Credits Purchased',
    'order_final_note_1' => 'We have added',
    'number_credits' => ':credits credits',
    'order_final_note_2' => 'to your account, which are available to use immediately.',
    'order_final_note_3' => 'You can find the invoice for this purchase by going to :billing_portal.',
    'billing_portal' => 'Manage Payments',
    'credits_expire_after_purchase' => 'Expire :duration after disbursement',
    'add_to_cart' => 'Add to Cart',
    'unsubscribe' => 'Unsubscribe',
    'subscribe' => 'Change Subscription',
    'switch_subscription' => 'Change Subscription',
    'credits_added_every_month' => 'Credits to be added every month',
    'charged' => 'Charged',
    'total_price' => 'Total Price',
    'price_per_year' => ':price / year',
    'price_per_month' => ':price / month',
    'per_year' => 'per year',
    'per_month' => 'per month',
    'subscriptions' => 'Subscriptions',
    'subscriptions_description' => 'Higher plan tiers unlock advanced functionality for configuring Agents, as well as more monthly included credits.',
    'annual_billing' => 'Annual Billing',
    'credits_per_month' => ':credits credits per month included',
    'ends_on_date' => 'Ends on :date',
    'current' => 'Current',
    'resume' => 'Resume',
    'switch' => 'Change Subscription',
    'complete_payment' => 'Complete Payment',
    'buy' => 'Buy Credits',
    'discount_code' => 'Discount Code',
    'discount_code_placeholder' => 'Enter discount code',
    'discount_code_invalid' => 'Invalid discount code.',
    'discount_code_inactive' => 'Discount code is no longer valid.',
    'apply' => 'Apply',
    'discount_for_x_months' => 'for one month|for :months months',
    'discount_once' => 'once',
    'after_discount' => 'then',
    'payment_processing' => 'Payment Processing',
    'validating_purchase' => 'Validating Purchase',
    'validating_purchase_title' => 'Please wait ...',
    'validating_purchase_desc_1' => 'Please wait while we validate your purchase. The page will refresh automatically upon success. If you are not redirected automatically, please refresh the page.',
    'redirecting_to' => 'You will be redirected to the next page in <span id="redirect-seconds">:seconds</span> seconds. Click the button below if you are not redirected automatically.',
    'redirect_now' => 'Continue',
    'and' => 'and',
    'payment_method' => 'Payment Method',
    'no_payment_method' => 'You have no payment methods set up.',
    'payment_method_description' => 'Your current payment method is a :Card_brand ending in :last_four.',
    'subscription_past_due' => 'Your subscription payment is past due date.',
    'subscription_past_due_description' => 'Please update your payment method to continue with the subscription.',
    'update_payment_method' => 'Update Payment Method',
    'payment_method_updated' => 'Payment method updated.',
    'payment_method_updated_description' => 'Your payment method has been updated successfully. Any outstanding payments will be processed shortly and your subscription will be resumed.',
    'subscription_success' => 'Success',
    'subscription_success_description' => 'Your subscription has been set up successfully.',

    'incomplete_payment_banner' => 'Your subscription has an incomplete payment. :click_here to resume subscription.',
    'card_declined_please_add' => 'Your card was declined. Please update the payment method to continue.',

    // Credit transaction types
    'transaction_type_subscription' => 'subscription',
    'transaction_type_product' => 'purchase',
    'transaction_type_adjustment' => 'adjustment',
    'transaction_type_usage' => 'usage',
];
