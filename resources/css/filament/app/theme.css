@import '/vendor/filament/filament/resources/css/theme.css';
@import '/vendor/awcodes/filament-tiptap-editor/resources/css/plugin.css';
@import '/vendor/awcodes/filament-table-repeater/resources/css/plugin.css';

@config 'tailwind.config.js';


/* Impersonate bug fix */
div.fi-layout > aside.fi-sidebar {
    padding-top: 0 !important;
}

/* User menu padding */
.fi-topbar > nav {
    @apply !pr-2;
}
@media screen and (min-width: 768px) {
    .fi-topbar > nav {
        @apply !pr-4;
    }
}
@media screen and (min-width: 1024px) {
    .fi-topbar > nav {
        @apply !pr-5;
    }
}
.fi-user-menu .fi-dropdown-trigger {
    @apply p-2;
    @apply rounded-md;
}
.fi-user-menu .fi-dropdown-trigger:hover,
.fi-user-menu .fi-dropdown-trigger:focus-visible
{
     @apply bg-gray-50 focus-visible:bg-gray-50  dark:focus-visible:bg-white/5
}
.dark .fi-user-menu .fi-dropdown-trigger:hover,
.dark .fi-user-menu .fi-dropdown-trigger:focus-visible
{
    @apply bg-white/5;
}

/* Simple Layout */
.fi-product-menu-trigger {
    min-height: 3rem;
}
.fi-simple-layout .fi-user-menu,
.fi-simple-layout .fi-team-menu
{
    display: none;
}
.fi-panel-app .fi-simple-main .fi-logo {
    height: 6rem !important;
}
.fi-panel-id .fi-simple-main .fi-logo {
    height: 4rem !important;
}

/* Make long checkbox lists scrollable */
.fi-fo-checkbox-list {
    /*max-height: 30rem;*/
    max-width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    @apply mt-4;
}
.fi-fo-checkbox-list > div {
    @apply border;
    @apply border-transparent;
    @apply px-3;
    @apply py-2;
    @apply mb-1;
    @apply rounded-lg;
}
.fi-fo-checkbox-list > div:has(input:checked) {
    @apply border-primary-500;
    @apply bg-primary-900/10;
}
.fi-fo-field-wrp-label,
.fi-fo-checkbox-list-option-label,
.fi-fo-radio label
{
    cursor: pointer;
}

/* left nav */
.fi-sidebar-item-grouped-border * {
    /*display: none;*/
}

/* Branding */
body {
    background-position: center center;
    background-attachment: fixed;
    background-size: cover;
    @apply !bg-white;
    /*background-image: url('/public/img/background-md-light.png');*/
}
.fi-section,
.fi-ta-ctn,
.fi-tabs,
.fi-simple-main,
.fi-fieldset,
.fi-fo-tabs,
.fi-ta-header
{
    background-color: rgba(255, 255, 255, .5) !important;
}
.fi-topbar > nav,
.fi-sidebar-header,
.fi-dropdown-panel,
.choices__list--dropdown,
.fi-product-tile,
.fi-fo-wizard,
.fi-modal-window,
.fi-dashboard-create
{
    background-color: rgba(255, 255, 255, .95) !important;
}

@media screen and (max-width: 1023px) {
    .fi-sidebar-nav {
        background-color: rgba(255, 255, 255, .95) !important;
    }
}

/* Dark Mode Branding */
.dark body {
    background-color: rgb(17, 24, 39) !important;
    background-image: url('/public/img/background-md-dark.png');
}
.dark .fi-topbar > nav,
.dark .fi-sidebar-header
{
    @apply shadow-xl;
}
.dark .fi-section,
.dark .fi-ta-ctn,
.dark .fi-tabs,
.dark .fi-simple-main,
.dark .fi-fieldset,
.dark .fi-fo-tabs,
.dark .fi-ta-header,
.dark .fi-topbar > nav,
.dark .fi-sidebar-header,
.dark .fi-dropdown-panel,
.dark .choices__list--dropdown,
.dark .fi-product-tile,
.dark .fi-dashboard-create,
.dark .table-repeater-header-column
{
    background-color: rgb(17, 24, 39) !important;
}
.dark .fi-modal-window,
.dark .fi-dashboard-create,
.dark .fi-fo-wizard
{
    background-color: rgba(17, 24, 39, .75) !important;
}
@media screen and (max-width: 1023px) {
    .dark .fi-sidebar-nav {
        background-color: rgba(17, 24, 39, .95) !important;
    }
}

/*  */
.fi-header > div:last-child {
    @apply relative;
}
.fi-fo-tabs {
    @apply overflow-hidden;
}

/* Iframe template */
.template-iframe .fi-main,
.template-iframe .fi-main > .fi-page > section
{
    @apply !p-0;
}
.template-iframe .fi-main > .fi-page,
.template-iframe .fi-main > .fi-page > section,
.template-iframe .fi-main > .fi-page > section *
{
    @apply !h-full;
}
.template-iframe-content {
    .bg-brand {
        @apply !bg-primary-500;
    }
    .lg\:rounded-md {
        @apply !rounded-xl;
    }
    .bg-gray-500 {
        @apply !bg-white/95;
    }
    .space-y-3 {
        @apply !space-y-0;
    }
    input {
        &:focus-visible {
            @apply !border-primary-500 !ring-0 !outline-none;
        }
    }
    #add-payment-method-form {
        .relative {
            top: 1px;
            #cardholder-name {
                @apply !pl-3;
            }
            svg {
                @apply !hidden;
            }
        }
    }
    #cardholder-name {
        @apply !w-64;
    }
    &.dark {
        .bg-white {
            background-color: rgba(17, 24, 39, .9) !important;
            @apply !ring-1 !ring-white/10;
        }
        .bg-gray-50 {
            @apply !bg-gray-100/5;
        }
        .text-gray-900 {
            @apply !text-white;
        }
        .text-gray-800 {
            @apply !text-gray-200;
        }
        .text-gray-700 {
            @apply !text-gray-300;
        }
        .text-gray-600 {
            @apply !text-gray-400;
        }
        .bg-gray-500 {
            /*@apply !bg-gray-900*/
            background-color: rgba(17, 24, 39, 1) !important;
        }
        .divide-gray-200 {
            @apply !divide-gray-800;
        }
        .border-gray-200 {
            @apply !border-gray-800;
        }
        .bg-gray-200 {
            @apply !bg-gray-700;
        }
        .bg-gray-100 {
            @apply !bg-gray-800;
        }
        input {
            @apply !bg-transparent !text-white;
        }
        .apexcharts-tooltip {
            background-color: rgba(17, 24, 39, .9) !important;
            @apply !ring-1 !ring-white/10 !border-none;
            .apexcharts-tooltip-title {
                @apply !bg-white/5 !border-b-gray-700;
            }
        }
        text.apexcharts-xaxis-label {
            @apply !text-white !fill-gray-400;
        }
    }
}

/* Welcome */
#welcome-modal .fi-modal-footer-actions {
    @apply !justify-between;
}

/* Tours */
.driver-popover {
    @apply !p-8;
    @apply !rounded-xl;
    @apply !shadow-xl;
    @apply !bg-white;
}
.driver-popover-close-btn {
    @apply !text-gray-500;
}
.driver-popover-close-btn:hover {
    @apply !text-white;
}
.driver-popover-title {
    @apply mb-4;
}
.driver-popover-description {
    @apply !mt-0;
    max-width: 32rem;
    font-size: medium;
}
.driver-popover-description a {
    text-decoration: underline;
}
.driver-popover footer {
    @apply !mt-6;
    @apply !justify-end;
}
.driver-popover footer:has(.driver-popover-prev-btn) {
    @apply !justify-between;
}

/* inline rich editor */
.inline-rich-editor {
    min-height: 4.95rem;
}

/* tip-tap editor */
.tiptap-toolbar {
    @apply !bg-transparent;
}
.tiptap-wrapper {
    @apply !bg-transparent;
}
.tiptap-toolbar-left,
.tiptap-toolbar-right
{
    @apply !py-4;
    button,
    .border-l
    {
        @apply !mx-1;
    }
}
.tiptap-editor .ProseMirror a[data-as-button=true] {
    @apply !rounded-full;
}
.tiptap-editor .ProseMirror a[data-as-button=true][data-as-button-theme="primary"],
.tiptap-editor .ProseMirror a[data-as-button=true][data-as-button-theme="accent"]
{
    @apply !bg-primary-500;
}
.tiptap-editor .ProseMirror a[data-as-button=true][data-as-button-theme="primary"]:hover,
.tiptap-editor .ProseMirror a[data-as-button=true][data-as-button-theme="accent"]:hover
{
    @apply !bg-primary-400;
}
.tiptap-editor .ProseMirror a[data-as-button=true][data-as-button-theme="secondary"] {
    @apply bg-gray-500 dark:bg-gray-800 text-gray-900 dark:text-white hover:bg-gray-400 dark:hover:bg-gray-700;
}
.tiptap-editor .ProseMirror a[data-as-button=true][data-as-button-theme="tertiary"] {
    @apply !bg-transparent border border-gray-300 dark:border-gray-700 text-gray-400 dark:text-gray-600 hover:text-white;
}
.tiptap-editor .ProseMirror a[data-as-button=true][data-as-button-theme="tertiary"]:hover {
    @apply !bg-primary-500 !border-primary-500;
}

/* noui-slider */
.noUi-target {
    background: transparent;
    @apply ring-1;
    border-radius: 4px;
    border: none;
    box-shadow: none;
    cursor: pointer;
}
.noUi-connect {
    @apply bg-primary-500;
}
.noUi-horizontal .noUi-handle,
.noUi-vertical .noUi-handle
{
    @apply bg-primary-500;
    box-shadow: none;
    cursor: grab;
}
.noUi-target.noUi-horizontal .noUi-tooltip {
    @apply text-xs;
    @apply bg-white/90;
    @apply text-gray-900;
    @apply dark:bg-gray-900/90;
    @apply dark:text-white;
    /*@apply ring-1;*/
    @apply border;
    @apply border-gray-200;
    @apply dark:border-gray-700;
}

/* domain */
#domain {
    @apply pt-2 px-0 -mb-7;
    background: transparent !important;
    border: none;
}

#domain > .grid {
    @apply gap-x-0;
}

#domain > .grid > div:first-child .fi-fo-field-wrp-label span {
    @apply sr-only;
}

#domain > .grid > div:first-child .fi-input-wrp {
    @apply rounded-r-none -mt-2;
}

#domain > .grid > div:first-child .fi-input-wrp .fi-input {
    @apply text-right pr-0;
}

#domain > .grid > div:last-child {
    @apply -mt-7;
}

#domain > .grid > div:last-child .fi-fo-field-wrp-label span {
    @apply sr-only;
}

#domain > .grid > div:last-child .fi-input-wrp {
    @apply rounded-l-none relative;
    left: 2px;
}

#domain > .grid > div:last-child .fi-input-wrp:before {
    @apply absolute h-9 bg-white dark:bg-[#202633];
    left: -2px;
    width: 2px;
    content: "";
    display: block;
}
#domain > .grid > div:last-child .fi-input-wrp.fi-disabled:before {
    @apply bg-[#fafafa] dark:bg-[#131a27];
}

#domain > .grid > div:last-child .fi-input-wrp .fi-select-input {
    @apply pl-0;
}

#agent-terms {
    @apply !border !rounded-lg !p-4 !bg-yellow-100 !border-yellow-300;
}
#agent-terms * {
    @apply !text-yellow-900;
}
