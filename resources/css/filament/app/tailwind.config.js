/** @type {import('tailwindcss').Config} */
import preset from '../../../../vendor/filament/filament/tailwind.config.preset';
const defaultTheme = require('tailwindcss/defaultTheme');

export default {
    presets: [preset],
    theme: {
        extend: {
            fontFamily: {
                body: ['Inter var', ...defaultTheme.fontFamily.sans],
                display: ['"Outfit"', 'sans-serif'],
            },
            colors: {
                primary: {
                    50: '#f2f0ff',
                    100: '#e8e4ff',
                    200: '#d4ccff',
                    300: '#b5a4ff',
                    400: '#9270ff',
                    500: '#7137ff',
                    600: '#630fff',
                    700: '#5500ff',
                    800: '#4600da',
                    900: '#330099',
                    950: '#21007a',
                },
            },
        },
    },
    content: [
        './app/Filament/**/*.php',
        './resources/views/**/*.blade.php',
        './vendor/filament/**/*.blade.php',
        './vendor/chrisreedio/socialment/resources/**/*.blade.php',
        './vendor/jaocero/radio-deck/resources/views/**/*.blade.php',
        './vendor/ralphjsmit/laravel-filament-record-finder/resources/**/*.blade.php',
        './vendor/awcodes/filament-tiptap-editor/resources/**/*.blade.php',
        './vendor/awcodes/filament-table-repeater/resources/**/*.blade.php',
    ],
}
