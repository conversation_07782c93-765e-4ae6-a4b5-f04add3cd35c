@php
    $agent = request()->has('model_id') ?
        \App\Models\Agent::where('id', request()->get('model_id'))->where('team_id', get_current_team_id())->first() :
        null
    ;
    $title = 'Agent Analytics';
    if ($agent) {
        $title .= ": {$agent->name}";
    }
@endphp

<div x-data="{ activeTab: 'prompts' }" class="agent-usage py-8 col-span-2">

    <div class="agent-usage-header mb-8 md:flex md:gap-x-4 items-center justify-center">

        <h1 class="md:flex-1 text-xl font-bold tracking-tight text-gray-950 dark:text-white sm:text-2xl mb-8 md:mb-0">
            Metrics Over Time
        </h1>

        <x-filament::tabs label="{{$title}}" class="flex justify-center items-center md:flex-none mx-0 justify-self-end">

            <x-filament::tabs.item
                alpine-active="activeTab === 'prompts'"
                x-on:click="activeTab = 'prompts'"
            >
                Prompts
            </x-filament::tabs.item>

            <x-filament::tabs.item
                alpine-active="activeTab === 'conversations'"
                x-on:click="activeTab = 'conversations'"
            >
                Conversations
            </x-filament::tabs.item>

            <x-filament::tabs.item
                alpine-active="activeTab === 'sessions'"
                x-on:click="activeTab = 'sessions'"
            >
                Sessions
            </x-filament::tabs.item>

            <x-filament::tabs.item
                alpine-active="activeTab === 'credits'"
                x-on:click="activeTab = 'credits'"
            >
                Credits
            </x-filament::tabs.item>

        </x-filament::tabs>

    </div>

    <div x-show="activeTab === 'prompts'" class="agent-usage-prompts">
        @livewire(\App\Filament\Widgets\AgentUsagePromptsChart::class, ['agent'=>$agent])
    </div>

    <div x-show="activeTab === 'conversations'" class="agent-usage-conversations">
        @livewire(\App\Filament\Widgets\AgentUsageConversationsChart::class, ['agent'=>$agent])
    </div>

    <div x-show="activeTab === 'sessions'" class="agent-usage-sessions">
        @livewire(\App\Filament\Widgets\AgentUsageSessionsChart::class, ['agent'=>$agent])
    </div>

    <div x-show="activeTab === 'credits'" class="agent-usage-credits">
        @livewire(\App\Filament\Widgets\AgentUsageCreditsChart::class, ['agent'=>$agent])
    </div>

</div>
