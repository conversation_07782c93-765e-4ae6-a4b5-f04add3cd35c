<div>

    @if (\App\Services\Gatekeeper::hasAdminAccess())

        @php
//        $adminUrl = \Filament\Facades\Filament::getPanel('admin')->getHomeUrl();
        $adminUrl = 'https://' . app_domain(env('APP_SUBDOMAIN_ADMIN'));
        @endphp
        <x-filament::dropdown.list>
            <x-filament::dropdown.list.item
                icon="heroicon-s-wrench-screwdriver"
                :href="$adminUrl"
                tag="a"
            >
                Administration
            </x-filament::dropdown.list.item>
        </x-filament::dropdown.list>

{{--        @if (filament()->getId() == 'id')--}}
{{--            @php--}}
{{--            $tokensUrl = \Rupadana\ApiService\Resources\TokenResource::getUrl('index');--}}
{{--            @endphp--}}
{{--            <x-filament::dropdown.list>--}}
{{--                <x-filament::dropdown.list.item--}}
{{--                    icon="heroicon-s-key"--}}
{{--                    :href="$tokensUrl"--}}
{{--                    tag="a"--}}
{{--                >--}}
{{--                    API Tokens--}}
{{--                </x-filament::dropdown.list.item>--}}
{{--            </x-filament::dropdown.list>--}}
{{--        @endif--}}

    @endif

    @if ((filament()->getId() == 'app') && !is_mobile())
        <x-filament::dropdown.list>
            <x-filament::dropdown.list.item
                icon="heroicon-s-play-circle"
                wire:click="$dispatch('filament-tour::open-tour', 'tour_product')"
                tag="button"
            >
                Product Tour
            </x-filament::dropdown.list.item>
        </x-filament::dropdown.list>
    @endif

    <x-filament::dropdown.list>
        <x-filament::dropdown.list.item
            icon="heroicon-s-question-mark-circle"
            :href="env('KNOWLEDGE_BASE_URL')"
            tag="a"
            target="_blank"
        >
            Knowledge Base ↗
        </x-filament::dropdown.list.item>
    </x-filament::dropdown.list>

    <x-filament::dropdown.list>
        <x-filament::dropdown.list.item
            icon="heroicon-s-envelope"
            :href="env('SUPPORT_URL')"
            tag="a"
            target="_blank"
        >
            Contact Support ↗
        </x-filament::dropdown.list.item>
    </x-filament::dropdown.list>

    <x-filament::dropdown.list>
        <x-filament::dropdown.list.item
            icon="heroicon-s-document-check"
            :href="env('TERMS_URL')"
            tag="a"
            target="_blank"
        >
            Terms of Use ↗
        </x-filament::dropdown.list.item>
    </x-filament::dropdown.list>

</div>
