<div class='mt-4 md:columns-2 xl:columns-3 md:gap-4'>
    <div class='break-inside-avoid mb-4'>
        <h3 class='text-lg'>Layout</h3>
        <dl>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-container</code></dt> <dd>overall wrapper for the interface</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-header</code></dt> <dd>top bar with the info icon, product switcher, translation switcher, & language switcher</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-body</code></dt> <dd>main area of the screen with the intro messaging, message list, and prompt input</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-footer</code></dt> <dd>bottom portion of the screen with attribution</dd>
        </dl>
    </div>
    <div class='break-inside-avoid mb-4'>
        <h3 class='text-lg'>Header</h3>
        <dl>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-info-image</code></dt> <dd>Agent logo that launches the info modal</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-translation-switcher</code></dt> <dd>translation dropdown</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-translation-switcher-search</code></dt> <dd>translation dropdown search input</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-language-switcher</code></dt> <dd>language dropdown</dd>
        </dl>
    </div>
    <div class='break-inside-avoid mb-4'>
        <h3 class='text-lg'>Intro Messaging</h3>
        <dl>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-intro</code></dt> <dd>intro messaging section</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-intro-preamble</code></dt> <dd>medium text at top of intro messaging</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-intro-headline</code></dt> <dd>large text in middle of intro messaging, in primary color</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-intro-description</code></dt> <dd>small text at bottom of intro messaging</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-intro-cta</code></dt> <dd>down arrow button that seeds a random question to the prompt input</dd>
        </dl>
    </div>
    <div class='break-inside-avoid mb-4'>
        <h3 class='text-lg'>Info Modal</h3>
        <dl>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-info-modal</code></dt> <dd>modal launched by the info icon</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-info-modal-header</code></dt> <dd>top portion of the info modal</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-info-modal-image</code></dt> <dd>logo / image in info modal header</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-info-modal-body</code></dt> <dd>main area of the info modal</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-info-modal-cta</code></dt> <dd>button in footer of info modal</dd>
        </dl>
    </div>
    <div class='break-inside-avoid mb-4'>
        <h3 class='text-lg'>Message List</h3>
        <dl>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-messages-list</code></dt> <dd>wrapper around the message list</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>.apg-message-user</code></dt> <dd>user prompt bar</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>.apg-message-user-icon</code></dt> <dd>sparkle icon on user prompt bar</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>.apg-message-agent</code></dt> <dd>Agent's response to a prompt</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>.apg-references-btn</code></dt> <dd>references button at the end of Agent's response</dd>
        </dl>
    </div>
    <div class='break-inside-avoid mb-4'>
        <h3 class='text-lg'>Footer</h3>
        <dl>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-footer-text</code></dt> <dd>text in footer</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-footer-cta</code></dt> <dd>donation button at the end of footer</dd>
        </dl>
    </div>
    <div class='break-inside-avoid mb-4'>
        <h3 class='text-lg'>References Modal</h3>
        <dl>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>.apg-references-modal</code></dt> <dd>references modal launched by references button at end of Agent's response</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>.apg-references-modal-header</code></dt> <dd>top portion of the references modal</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>.apg-references-modal-body</code></dt> <dd>wrapper of the list of references in references modal</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>.apg-references-modal-item</code></dt> <dd>individual reference item wrapper in references modal</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>.apg-references-modal-item-image</code></dt> <dd>individual reference item image</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>.apg-references-modal-item-title</code></dt> <dd>individual reference title</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>.apg-references-modal-item-authors</code></dt> <dd>individual reference authors list</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>.apg-references-modal-item-cta</code></dt> <dd>individual reference button</dd>
        </dl>
    </div>
    <div class='break-inside-avoid mb-4'>
        <h3 class='text-lg'>Prompt Input</h3>
        <dl>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-prompt</code></dt> <dd>user prompt section</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-prompt-input</code></dt> <dd>user prompt input</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-prompt-submit</code></dt> <dd>user prompt submit button</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-prompt-dictate</code></dt> <dd>user prompt dictate button</dd>
            <dt class='mt-4'><code class='text-gray-900 dark:text-white'>#apg-prompt-reset</code></dt> <dd>user prompt reset button</dd>
        </dl>
    </div>
</div>
