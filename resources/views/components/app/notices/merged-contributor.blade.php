@php
    $record = $this->getRecord();
    $mergedContributor = $record ? $record->mergedContributor : null;
@endphp
@if ($mergedContributor)
    <x-shout::shout
        type="warning"
        color="warning"
        icon="heroicon-o-exclamation-triangle"
    >
        <h3>
            {{ \App\Services\Labeller::model($this->getRecord()) }}
            Merged with
            <a
                class="font-bold underline"
                href="{{ \App\Filament\Resources\ContributorResource::getUrl('edit', ['record'=>$mergedContributor->id]) }}"
            >
                {{ $mergedContributor->name }}
            </a>
        </h3>
    </x-shout::shout>
@endif
