@if ($this->getRecord()->isRejected())
    <x-shout::shout
        type="danger"
        color="danger"
        icon="heroicon-o-x-circle"
    >
        <h3 class="font-bold">
            {{ \App\Services\Labeller::model($this->getRecord()) }} Submission Rejected
            @if (!empty($this->getRecord()->getRejectionComment()))
                with the Following Comment:
            @endif
        </h3>
        @if (!empty($this->getRecord()->getRejectionComment()))
            <p class="mt-2">
                {{ $this->getRecord()->getRejectionComment() }}
            </p>
        @endif
    </x-shout::shout>
@endif
