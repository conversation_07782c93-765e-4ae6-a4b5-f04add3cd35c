@php
    $user = filament()->auth()->user();
    if ($user->current_team_id) {
        $teams = $user->teams()->where('teams.id', '!=', $user->currentTeam->id)->get();
        $isOwner = ($user->id == $user->currentTeam->owner->id);
    } else {
        $teams = new \Illuminate\Database\Eloquent\Collection();
        $isOwner = false;
    }
    $gray = \Filament\Support\Colors\Color::Gray;
@endphp

<x-filament::dropdown
    placement="bottom-end"
    teleport
    class="fi-team-menu"
>
    <x-slot name="trigger">
        <button
            aria-label="{{ __('filament-panels::layout.actions.open_tenant_menu.label') }}"
            type="button"
            class="shrink-0 flex items-center gap-x-2 p-2 pr-1 cursor-pointer rounded-md hover:bg-gray-50 ml-1 focus-visible:bg-gray-50 dark:hover:bg-white/5 dark:focus-visible:bg-white/5"
        >
            @if (empty($user->current_team_id) || empty($user->currentTeam->avatar_path))
                <x-filament::icon icon="heroicon-s-users" class="inline-block mr-1 !h-6 !w-6 text-gray-500 fi-team-avatar" />
            @else
                <x-filament::avatar :src="$user->currentTeam->getFilamentAvatarUrl()" class="inline-block mr-1 h-8 w-8 fi-team-avatar" />
            @endif
            @if (!empty($user->current_team_id))
                {{ $user->currentTeam->name }}
            @else
                No Team Selected
            @endif
            <x-filament::icon
                icon="heroicon-s-chevron-up-down"
                class="h-5 w-5 text-gray-500 dark:text-gray-400"
            />
        </button>
    </x-slot>

    @if ($isOwner || \App\Services\Gatekeeper::isAdmin())

        <x-filament::dropdown.list class="fi-team-menu-profile">

            <x-filament::dropdown.header
                :color="$gray"
            >
                <span class="block text-center">Credit Balance: <strong>{{ number_format($user->currentTeam->credits()->balance()) }}</strong></span>
            </x-filament::dropdown.header>

            <x-filament::dropdown.list.item
                :href="route('filament.app.pages.billing.usage', ['tenant'=>get_current_team_id()])"
                icon="heroicon-s-chart-bar-square"
                tag="a"
            >
                Monitor Usage
            </x-filament::dropdown.list.item>

            <x-filament::dropdown.list.item
                :href="route('filament.app.pages.billing.products', ['tenant'=>get_current_team_id()])"
                icon="heroicon-s-currency-dollar"
                tag="a"
            >
                Buy Credits
            </x-filament::dropdown.list.item>

            <x-filament::dropdown.list.item
                :href="route('filament.app.pages.billing.subscription', ['tenant'=>get_current_team_id()])"
                icon="heroicon-s-arrow-path"
                tag="a"
            >
                Change Subscription
            </x-filament::dropdown.list.item>

            <x-filament::dropdown.list.item
                :href="route('filament.app.pages.billing.payments', ['tenant'=>get_current_team_id()])"
                icon="heroicon-s-document-currency-dollar"
                tag="a"
            >
                Manage Payments
            </x-filament::dropdown.list.item>

            <x-filament::dropdown.list.item
                :href="route('filament.app.resources.teams.edit', ['tenant'=>$user->current_team_id, 'record'=>$user->current_team_id])"
                icon="heroicon-s-identification"
                tag="a"
            >
                Edit Team Profile
            </x-filament::dropdown.list.item>

        </x-filament::dropdown.list>

    @endif

    @if (!$teams->isEmpty())

        <x-filament::dropdown.list>

            <x-filament::dropdown.header
                :color="$gray"
            >
                <span class="block text-center">Switch to Another Team:</span>
            </x-filament::dropdown.header>

            <div class="max-h-64 overflow-y-auto">
                @foreach ($teams as $team)
                    <x-filament::dropdown.list.item
                        :href="route('filament.app.tenant.switch', ['tenant'=>$team->id])"
                        tag="a"
                    >
                        @if (empty($team->avatar_path))
                            <x-filament::icon icon="heroicon-s-users" class="inline-block mr-1 !h-5 !w-5 text-gray-500" />
                        @else
                            <x-filament::avatar :src="$team->getFilamentAvatarUrl()" class="inline-block mr-1 !h-5 !w-5" />
                        @endif
                        {{ $team->name }}
                    </x-filament::dropdown.list.item>
                @endforeach
            </div>

        </x-filament::dropdown.list>

    @endif

    <x-filament::dropdown.list class="fi-team-menu-create">
        <x-filament::dropdown.list.item
            :href="route('filament.app.tenant.registration')"
            icon="heroicon-s-plus"
            tag="a"
        >
            Create New Team
        </x-filament::dropdown.list.item>
    </x-filament::dropdown.list>

</x-filament::dropdown>
