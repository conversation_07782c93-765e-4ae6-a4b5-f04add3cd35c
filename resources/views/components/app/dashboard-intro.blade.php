@php
    $user = auth()->user();
    $contributor = \App\Filament\Resources\ContributorResource::class;
    $sources = [
        \App\Filament\Resources\BookSourceResource::class,
        \App\Filament\Resources\ArticleSourceResource::class,
        \App\Filament\Resources\UrlSourceResource::class,
        \App\Filament\Resources\EpisodeSourceResource::class,
        \App\Filament\Resources\YoutubeSourceResource::class,
        \App\Filament\Resources\MediaSourceResource::class,
    ];
@endphp

<div class="w-full md:grid md:grid-cols-3 md:gap-4 col-span-2">

    <div class="fi-dashboard-create mb-4 md:mb-0 rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:divide-white/10 dark:bg-gray-900 dark:ring-white/10 p-4 text-center">
        <x-filament::icon icon="heroicon-s-academic-cap" class="mx-auto h-12 w-12 text-primary-500"/>
        <h3 class="mt-2 text-md font-semibold text-gray-900 dark:text-white">Contributors</h3>
        <p class="mt-1 text-sm text-gray-700 dark:text-gray-300">Any person that was involved in publishing content (book,
            article, video, etc).</p>
        <div class="mt-6">
            <a
                href="{{ $contributor::getUrl('create') }}"
                style="--c-400:var(--primary-400);--c-500:var(--primary-500);--c-600:var(--primary-600);"
                class="fi-btn relative grid-flow-col items-center justify-center font-semibold outline-none transition duration-75 focus-visible:ring-2 rounded-lg fi-color-custom fi-btn-color-primary fi-color-primary fi-size-md fi-btn-size-md gap-1.5 px-3 py-2 text-sm inline-grid fi-btn-outlined ring-1 text-custom-600 ring-custom-600 hover:bg-custom-400/10 dark:text-custom-400 dark:ring-custom-500"
                id="btn-add-contributors"
            >
                <span class="fi-btn-label">
                    Add Contributor
                </span>
            </a>
        </div>
    </div>

    <div class="fi-dashboard-create md:col-span-2 rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:divide-white/10 dark:bg-gray-900 dark:ring-white/10 p-4 text-center">
        <x-filament::icon icon="heroicon-s-document-text" class="mx-auto h-12 w-12 text-primary-500"/>
        <h3 class="mt-2 text-md font-semibold text-gray-900 dark:text-white">Content</h3>
        <p class="mt-1 text-sm text-gray-700 dark:text-gray-300">Content (written or media) shown to users in the Apologist
            ecosystem. Promote your resources to the world! Get started by clicking on a button below.</p>
        <div class="mt-6 grid lg:grid-cols-3 gap-4">
            @foreach($sources as $source)
                <x-filament::button
                    icon="{{ $source::getNavigationIcon() }}"
                    tag="a"
                    href="{{ $source::getUrl('create') }}"
                    id="{{ btn_id('add-' . $source::getSlug()) }}"
                    outlined
                >
                    {{ $source::getPluralModelLabel() }}
                </x-filament::button>
            @endforeach
        </div>
    </div>

</div>
