@php
$logoLight = filament()->getBrandLogo();
$logoDark = filament()->getDarkModeBrandLogo();
$appName = filament()->getBrandName();
@endphp

<div id="welcome-modal" x-data="{ apgWelcomeSeen: $persist(false).as('apgWelcomeSeen') }">

    <x-filament::modal
        id="welcome-modal"
        width="xl"
        :close-button="false"
        :close-by-clicking-away="false"
    >

        <x-slot name="heading">
            <img src="{{$logoLight}}" class="flex dark:hidden w-1/3 mx-auto block">
            <img src="{{$logoDark}}" class="hidden dark:flex w-1/3 mx-auto block">
            <span class="block text-center mt-4">Evangelism for the Modern Era</span>
        </x-slot>

        <x-slot name="description">
            <p class="mt-6">
                Welcome to Apologist Ignite, the premier Christian evangelism platform for ministries, publishers,
                and independent evangelists. We've prepared a guided tour to show you how to navigate around the product.
            </p>
            <p class="mt-4">
                However, we recognize that some folks just like to explore on their own, so feel free to skip the tour.
                You can always launch the tour later by clicking on the "Product Tour" link in the User menu in the
                upper right of your screen.
            </p>
        </x-slot>

        <x-slot name="footerActions">
            <x-filament::button
                wire:click="$dispatch('skipTour')"
                x-on:click="apgWelcomeSeen = true"
                color="gray"
            >
                Skip the Tour
            </x-filament::button>

            <x-filament::button
                x-on:click="apgWelcomeSeen = true"
                wire:click="$dispatch('startTour')"
            >
                Start the Tour
            </x-filament::button>
        </x-slot>

    </x-filament::modal>

    <script>

        document.addEventListener('livewire:navigated', () => {
            if (localStorage.getItem('apgWelcomeSeen') === 'false') {
                Livewire.dispatch('open-modal', {id: 'welcome-modal'});
                Livewire.on('skipTour', () => {
                    Livewire.dispatch('close-modal', {id: 'welcome-modal'});
                });
                Livewire.on('startTour', () => {
                    Livewire.dispatch('close-modal', {id: 'welcome-modal'});
                    Livewire.dispatch('filament-tour::open-tour', 'tour_product');
                    simulateClick(document.querySelector('.fi-user-avatar'));
                });
            }

        }, {once: true});

        var simulateClick = function (elem) {
            var evt = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            var canceled = !elem.dispatchEvent(evt);
        };
    </script>

</div>
