@php
    $record = $modelId ?
        $modelClass::where('id', $modelId)->where('team_id', get_current_team_id())->first() :
        null
    ;
    $title = "{$modelLabel} Analytics";
    if ($record) {
        $title .= ": {$record->name}";
    }
@endphp

<div x-data="{ activeTab: 'impressions' }" class="listing-stats py-8">

    <div class="listings-stats-header mb-8 md:flex md:gap-x-4 items-center justify-center">

        <h1 class="fi-header-heading md:flex-1 text-2xl font-bold tracking-tight text-gray-950 dark:text-white sm:text-3xl mb-8 md:mb-0">
            {{$title}}
        </h1>

        <x-filament::tabs label="{{$title}}" class="flex justify-center items-center md:flex-none mx-0 justify-self-end">

            <x-filament::tabs.item
                alpine-active="activeTab === 'impressions'"
                x-on:click="activeTab = 'impressions'"
            >
                Impressions
            </x-filament::tabs.item>

            <x-filament::tabs.item
                alpine-active="activeTab === 'views'"
                x-on:click="activeTab = 'views'"
            >
                Views
            </x-filament::tabs.item>

            <x-filament::tabs.item
                alpine-active="activeTab === 'referrals'"
                x-on:click="activeTab = 'referrals'"
            >
                Referrals
            </x-filament::tabs.item>

        </x-filament::tabs>

    </div>

    <div x-show="activeTab === 'impressions'" class="listing-stats-impressions">
        @livewire(\App\Filament\Widgets\FrontendListingImpressionsChart::class, ['record'=>$record, 'model'=>$modelClass, 'modelLabel'=>$modelLabel])
    </div>

    <div x-show="activeTab === 'views'" class="listing-stats-views">
        @livewire(\App\Filament\Widgets\FrontendListingViewsChart::class, ['record'=>$record, 'model'=>$modelClass, 'modelLabel'=>$modelLabel])
    </div>

    <div x-show="activeTab === 'referrals'" class="listing-stats-referrals">
        @livewire(\App\Filament\Widgets\FrontendListingReferralsChart::class, ['record'=>$record, 'model'=>$modelClass, 'modelLabel'=>$modelLabel])
    </div>

</div>


