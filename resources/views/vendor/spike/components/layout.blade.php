@php
    $theme = $_GET['theme'] ?? 'light';
@endphp
<!doctype html>
<html lang="{{ app()->getLocale() }}" class="h-full template-iframe-content" style="--brand-color: {{ $themeColor }}">

    <head>

        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">

        @if($theme == 'dark')
            <meta name="color-scheme" content="light dark">
        @endif

        <title>@isset($title){{ $title }} - @endisset{{ config('app.name') }}</title>

        <style>[x-cloak] { display: none !important; }</style>
        @if($faviconUrl = config('spike.theme.favicon_url'))<link rel="icon" href="{{ $faviconUrl }}">@endif
        <link rel="stylesheet" href="{{ asset('vendor/spike/app.css') }}">

        <script>
            window.theme = localStorage.getItem('theme') ?? '{{ $theme }}';
            document.documentElement.classList.add(window.theme);
        </script>

        @filamentStyles

        {{ filament()->getTheme()->getHtml() }}
        {{ filament()->getFontHtml() }}

        <style>
            :root {
                --font-family: '{!! filament()->getFontFamily() !!}';
                --sidebar-width: {{ filament()->getSidebarWidth() }};
                --collapsed-sidebar-width: {{ filament()->getCollapsedSidebarWidth() }};
                --default-theme-mode: {{ filament()->getDefaultThemeMode()->value }};
            }
            [x-cloak=''],
            [x-cloak='x-cloak'],
            [x-cloak='1'] {
                display: none !important;
            }

            @media (max-width: 1023px) {
                [x-cloak='-lg'] {
                    display: none !important;
                }
            }

            @media (min-width: 1024px) {
                [x-cloak='lg'] {
                    display: none !important;
                }
            }
            html,
            body,
            .dark body
            {
                background: transparent none !important;
            }
        </style>

        @stack('styles')

        @livewireStyles
        @spikeJS

    </head>

    <body class="h-full px-0 py-8" style="--brand-color: {{ $themeColor }}; --background-image: none; ----background-image: none;">

        @if($hasIncompleteSubscriptionPayment)
            <div class="sm:fixed inset-x-0 top-0 px-5 py-2 bg-orange-300 text-orange-900 text-center text-sm sm:mb-9">
                {!! trans('spike::translations.incomplete_payment_banner', ['click_here' => '<a href="' . route('spike.subscribe.incomplete-payment') . '" class="font-bold hover:underline">Complete the payment</a>']) !!}
            </div>
        @endif

        <header class="fi-header mb-8">
            <h1 class="fi-header-heading text-2xl font-semibold tracking-tight text-gray-950 dark:text-white sm:text-3xl">
                {{ $title }}
            </h1>
        </header>

        <main class="w-full pb-8">
            {{ $slot }}
            <br><br>
        </main>

        @livewireScripts
        @livewire('livewire-ui-modal')
        @livewireChartsScripts
        @stack('scripts')

    </body>

</html>
