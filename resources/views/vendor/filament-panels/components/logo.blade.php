@php
    $brandName = filament()->getBrandName();
    $brandLogo = filament()->getBrandLogo();
    $brandLogoHeight = filament()->getBrandLogoHeight() ?? '1.5rem';
    $darkModeBrandLogo = filament()->getDarkModeBrandLogo();
    $hasDarkModeBrandLogo = filled($darkModeBrandLogo);

    $getLogoClasses = fn (bool $isDarkMode): string => \Illuminate\Support\Arr::toCssClasses([
        'fi-logo',
        'flex' => ! $hasDarkModeBrandLogo,
        'flex dark:hidden fi-logo-light' => $hasDarkModeBrandLogo && (!$isDarkMode),
        'hidden dark:flex fi-logo-dark' => $hasDarkModeBrandLogo && $isDarkMode,
    ]);
    $getTheme = fn (bool $isDarkMode) : string => $isDarkMode ? 'dark' : 'light';

    $logoStyles = "height: {$brandLogoHeight}";

    $products = config('products');
@endphp

@capture($content, $logo, $isDarkMode = false)

    <x-filament::dropdown
        placement="bottom-end"
        teleport
        :class="'-ml-2 fi-product-menu fi-product-menu-' . $getTheme($isDarkMode) .  ' ' . ($isDarkMode ? 'hidden dark:flex' : 'flex dark:hidden')"
    >
        <x-slot name="trigger">
            <button
                aria-label="Products"
                type="button"
                class="shrink-0 flex items-center gap-x-2 cursor-pointer pl-3 relative fi-product-menu-trigger"
            >
                <img
                    alt="{{ $brandName }}"
                    src="{{ asset($logo) }}"
                    {{
                        $attributes
                            ->class([$getLogoClasses($isDarkMode)])
                            ->style([$logoStyles])
                    }}
                />
                <x-filament::icon
                    icon="heroicon-s-chevron-up-down"
                    class="h-5 w-5 text-gray-300 dark:text-gray-600 hover:text-gray-500 hover:dark:text-gray-400"
                />
                @if (filament()->getCurrentPanel()->getId() == 'admin')
                    <x-filament::badge color="danger" class="absolute -right-16">
                        ADMIN
                    </x-filament::badge>
                @else
                    <x-filament::badge color="gray" class="absolute -right-14">
                        BETA
                    </x-filament::badge>
                @endif
            </button>
        </x-slot>

        <x-filament::dropdown.list>

            @foreach ($products as $name => $attrs)
                @if (!isset($attrs['panel_id']) || ($attrs['panel_id'] != filament()->getCurrentPanel()->getId()))
                    @php
                    $logo = str_replace('{mode}', $isDarkMode ? 'dark' : 'light', $attrs['img']);
                    $target = $attrs['target'] ?? '_self';
                    @endphp
                    <x-filament::dropdown.list.item
                        :href="$attrs['url']"
                        tag="a"
                        class="flex relative"
                        :target="$target"
                    >
                        <span class="flex-auto">
                            <img
                                alt="{{ $name }}"
                                src="{{ asset($logo) }}"
                                class="h-12"
                            />
                            <p class="mt-2 text-gray-500 dark:text-gray-400">{{ $attrs['description'] }}</p>
                        </span>
                        @if ($target == '_blank')
                            <x-filament::icon
                                icon="heroicon-s-arrow-top-right-on-square"
                                class="flex-none h-5 w-5 text-gray-500 dark:text-gray-400 absolute top-2 right-2"
                            />
                        @endif
                    </x-filament::dropdown.list.item>
                @endif
            @endforeach
        </x-filament::dropdown.list>

    </x-filament::dropdown>

@endcapture

{{ $content($brandLogo) }}

@if ($hasDarkModeBrandLogo)
    {{ $content($darkModeBrandLogo, isDarkMode: true) }}
@endif
