diff --git a/src/Tour/Traits/CanConstructRoute.php b/src/Tour/Traits/CanConstructRoute.php
index e290e76..2a03f03 100644
--- a/src/Tour/Traits/CanConstructRoute.php
+++ b/src/Tour/Traits/CanConstructRoute.php
@@ -22,10 +22,13 @@ trait CanConstructRoute
 
             $tenant = $tenants->first();
 
-            $slug = $tenant->slug;
-            if ($slug) {
-                $this->route = parse_url($instance->getUrl(['tenant' => $slug]))['path'];
+            if (isset($tentant->slug)) {
+                $slug = $tenant->slug;
+                if ($slug) {
+                    $this->route = parse_url($instance->getUrl(['tenant' => $slug]))['path'];
+                }
             }
+
         } else {
             if (method_exists($instance, 'getResource')) {
                 $resource = new ($instance->getResource());
