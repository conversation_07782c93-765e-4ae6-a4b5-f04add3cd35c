diff --git a/routes/api.php b/routes/api.php
index 7488aa4..bc5fab1 100644
--- a/routes/api.php
+++ b/routes/api.php
@@ -5,7 +5,7 @@ use Illuminate\Support\Facades\Route;
 use Rupadana\ApiService\ApiService;
 use Rupadana\ApiService\Exceptions\InvalidTenancyConfiguration;

-Route::prefix('api')
+Route::domain(app_domain(env('APP_SUBDOMAIN_API')))
     ->name('api.')
     ->group(function () {
         if (ApiService::tenancyAwareness() && (! ApiService::isRoutePrefixedByPanel() || ! ApiService::isTenancyEnabled())) {
