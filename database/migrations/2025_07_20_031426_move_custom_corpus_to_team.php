<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('teams', function (Blueprint $table) {
            $table->boolean('is_active')->default(true)->after('name');
            $table->boolean('has_custom_corpus')->default(false)->after('is_trial');
            $table->string('corpus_api_key', 500)->nullable()->after('has_custom_corpus');
        });
        Schema::table('agents', function (Blueprint $table) {
            $table->dropColumn('has_custom_corpus');
            $table->dropColumn('corpus_api_key');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agents', function (Blueprint $table) {
            $table->boolean('has_custom_corpus')->default('boolean')->after('supported_translations');
            $table->string('corpus_api_key', 500)->nullable()->after('has_custom_corpus');
        });
        Schema::table('teams', function (Blueprint $table) {
            $table->dropColumn('is_active');
            $table->dropColumn('has_custom_corpus');
            $table->dropColumn('corpus_api_key');
        });
    }
};
