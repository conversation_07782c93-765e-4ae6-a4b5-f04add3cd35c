<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agent_integration_platforms', function (Blueprint $table) {
            $table->string('key', 25)->nullable()->after('name');
            $table->string('description', 500)->nullable()->after('key');
            $table->jsonb('languages')->nullable()->after('endpoint_url');
            $table->dropColumn('is_public');
            $table->boolean('has_account')->default(false)->after('languages');
            $table->boolean('has_secret')->default(false)->after('has_account');
            $table->boolean('has_cue')->default(false)->after('has_secret');
            $table->boolean('has_welcome')->default(false)->after('has_cue');
            $table->boolean('has_cta')->default(false)->after('has_welcome');
            $table->boolean('can_auto_initialize')->default(false)->after('has_cta');
            $table->string('param_1_label', 50)->nullable()->after('can_auto_initialize');
            $table->string('param_2_label', 50)->nullable()->after('param_1_label');
            $table->string('param_3_label', 50)->nullable()->after('param_2_label');
        });
        Schema::table('agent_integrations', function (Blueprint $table) {
            $table->string('account', 250)->nullable()->change();
            $table->boolean('auto_initialize')->default(false)->after('languages');
            $table->string('param_1', 250)->nullable()->after('auto_initialize');
            $table->string('param_2', 250)->nullable()->after('param_1');
            $table->string('param_3', 250)->nullable()->after('param_2');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agent_integration_platforms', function (Blueprint $table) {
            $table->dropColumn('key');
            $table->dropColumn('description');
            $table->boolean('is_public')->default(false)->after('endpoint_url');
            $table->dropColumn('languages');
            $table->dropColumn('has_account');
            $table->dropColumn('has_secret');
            $table->dropColumn('has_cue');
            $table->dropColumn('has_welcome');
            $table->dropColumn('has_cta');
            $table->dropColumn('can_auto_initialize');
            $table->dropColumn('param_1_label');
            $table->dropColumn('param_2_label');
            $table->dropColumn('param_3_label');
        });
        Schema::table('agent_integrations', function (Blueprint $table) {
            $table->string('account', 250)->change();
            $table->dropColumn('auto_initialize');
            $table->dropColumn('param_1');
            $table->dropColumn('param_2');
            $table->dropColumn('param_3');
        });
    }
};
