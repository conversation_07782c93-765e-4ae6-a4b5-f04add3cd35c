<?php

use App\Enums\AgentReasoningEffort;
use App\Enums\AgentVerbosity;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agent_models', function (Blueprint $table) {
            $table->boolean('supports_verbosity')->default(false)->after('supports_reasoning_effort');
            $table->boolean('supports_temperature')->default(true)->after('supports_verbosity');
        });
        Schema::table('agents', function (Blueprint $table) {
            $table->enum('model_reasoning_effort', AgentReasoningEffort::values())->nullable()->change();
            $table->enum('model_verbosity', AgentVerbosity::values())->nullable()->after('model_reasoning_effort');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agent_models', function (Blueprint $table) {
            $table->dropColumn('supports_verbosity');
            $table->dropColumn('supports_temperature');
        });
        Schema::table('agents', function (Blueprint $table) {
            $table->dropColumn('model_verbosity');
        });
    }
};
