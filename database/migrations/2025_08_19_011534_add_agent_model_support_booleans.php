<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agent_models', function (Blueprint $table) {
            $table->boolean('supports_top_p')->default(true)->after('supports_temperature');
            $table->boolean('supports_frequency_penalty')->default(true)->after('supports_top_p');
            $table->boolean('supports_presence_penalty')->default(true)->after('supports_frequency_penalty');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agent_models', function (Blueprint $table) {
            $table->dropColumn('supports_top_p');
            $table->dropColumn('supports_frequency_penalty');
            $table->dropColumn('supports_presence_penalty');
        });
    }
};
