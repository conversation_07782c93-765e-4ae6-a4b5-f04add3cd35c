<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agents', function (Blueprint $table) {
            $table->boolean('has_basic_auth')->default(false)->after('api_response_format');
            $table->string('basic_auth_user', 50)->nullable()->after('has_basic_auth');
            $table->string('basic_auth_password', 500)->nullable()->after('basic_auth_user');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agents', function (Blueprint $table) {
            $table->dropColumn('has_basic_auth');
            $table->dropColumn('basic_auth_user');
            $table->dropColumn('basic_auth_password');
        });
    }
};
