<?php

use App\Enums\AgentApiResponseFormat;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agents', function (Blueprint $table) {
            $table->enum('api_response_format', AgentApiResponseFormat::values())
                ->default(AgentApiResponseFormat::RAW->value)
                ->after('is_template')
            ;
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agents', function (Blueprint $table) {
            $table->dropColumn('api_response_format');
        });
    }
};
