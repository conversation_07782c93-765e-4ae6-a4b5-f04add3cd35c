<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

if (!defined('MODEL_TABLES')) {
    define(
        'MODEL_TABLES',
        [
            'collections',
            'contributors',
            'organizations',
        ]
    );
}

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        foreach (MODEL_TABLES as $table) {
            Schema::table($table, function (Blueprint $table) {
                $table->timestamp('social_indexed_at')->nullable()->after('social_active')->index();
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        foreach (MODEL_TABLES as $table) {
            Schema::table($table, function (Blueprint $table) {
                $table->dropColumn('social_indexed_at');
            });
        }
    }
};
