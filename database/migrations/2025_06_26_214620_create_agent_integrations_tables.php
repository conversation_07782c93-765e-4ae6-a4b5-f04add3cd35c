<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agent_integration_platforms', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->boolean('is_active')->default(false);
            $table->string('image_path')->nullable();
            $table->string('endpoint_url')->nullable();
            $table->boolean('is_public')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
        Schema::create('agent_integrations', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->unsignedBigInteger('agent_id');
            $table->foreign('agent_id')->references('id')->on('agents');
            $table->unsignedBigInteger('platform_id');
            $table->foreign('platform_id')->references('id')->on('agent_integration_platforms');
            $table->boolean('is_active')->default(false);
            $table->string('account', 250);
            $table->string('secret', 500)->nullable();
            $table->string('token', 500)->nullable();
            $table->jsonb('cue')->nullable();
            $table->jsonb('welcome')->nullable();
            $table->jsonb('cta')->nullable();
            $table->jsonb('languages')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agent_integrations');
        Schema::dropIfExists('agent_integration_platforms');
    }
};
