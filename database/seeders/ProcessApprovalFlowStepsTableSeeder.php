<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ProcessApprovalFlowStepsTableSeeder extends Seeder
{
    /**
     * Auto generated seed file
     */
    public function run(): void
    {

        \DB::table('process_approval_flow_steps')->delete();

        \DB::table('process_approval_flow_steps')->insert([
            0 => [
                'id' => 1,
                'process_approval_flow_id' => 1,
                'role_id' => 1,
                'permissions' => null,
                'order' => 1,
                'action' => 'APPROVE',
                'active' => 1,
                'created_at' => get_now_timestamp(),
                'updated_at' => get_now_timestamp(),
            ],
            1 => [
                'id' => 2,
                'process_approval_flow_id' => 2,
                'role_id' => 1,
                'permissions' => null,
                'order' => null,
                'action' => 'APPROVE',
                'active' => 1,
                'created_at' => get_now_timestamp(),
                'updated_at' => get_now_timestamp(),
            ],
            2 => [
                'id' => 3,
                'process_approval_flow_id' => 3,
                'role_id' => 1,
                'permissions' => null,
                'order' => null,
                'action' => 'APPROVE',
                'active' => 1,
                'created_at' => get_now_timestamp(),
                'updated_at' => get_now_timestamp(),
            ],
        ]);

    }
}
