<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class RolesTableSeeder extends Seeder
{
    /**
     * Auto generated seed file
     */
    public function run(): void
    {

        \DB::table('roles')->delete();

        \DB::table('roles')->insert([
            0 => [
                'id' => 1,
                'name' => 'Super Admin',
                'guard_name' => 'web',
                'is_team' => 0,
                'icon' => null,
                'created_at' => get_now_timestamp(),
                'updated_at' => get_now_timestamp(),
                'description' => null,
            ],
            1 => [
                'id' => 2,
                'name' => 'Admin',
                'guard_name' => 'web',
                'is_team' => 1,
                'icon' => 'heroicon-s-user-circle',
                'created_at' => get_now_timestamp(),
                'updated_at' => get_now_timestamp(),
                'description' => '<PERSON><PERSON> can add other team members and manage all team-owned collateral.',
            ],
            2 => [
                'id' => 3,
                'name' => 'Member',
                'guard_name' => 'web',
                'is_team' => 1,
                'icon' => 'heroicon-s-user',
                'created_at' => get_now_timestamp(),
                'updated_at' => get_now_timestamp(),
                'description' => 'Members can view all team-owned collateral but cannot make changes.',
            ],
        ]);

    }
}
