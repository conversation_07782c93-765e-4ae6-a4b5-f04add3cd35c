<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class AgentModelsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('agent_models')->delete();
        
        \DB::table('agent_models')->insert(array (
            0 => 
            array (
                'id' => 1,
                'name' => 'Google Gemma 9B',
                'key' => 'google/gemma/9b',
                'provider_id' => 1,
                'provider_model' => 'gemma2-9b-it',
                'type' => 'limited',
                'is_recommended' => 0,
                'max_tokens' => 2048,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 1,
                'seq' => 1,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'name' => 'Mistral Mixtral 8x7B Instruct',
                'key' => 'mistral/mixtral/8x7b',
                'provider_id' => 1,
                'provider_model' => 'mixtral-8x7b-32768',
                'type' => 'limited',
                'is_recommended' => 0,
                'max_tokens' => 2048,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "es", "it", "de", "fr"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 1,
                'seq' => 4,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            2 => 
            array (
                'id' => 3,
                'name' => 'OpenAI GPT-4o mini',
                'key' => 'openai/gpt/4o-mini',
                'provider_id' => 2,
                'provider_model' => 'gpt-4o-mini',
                'type' => 'limited',
                'is_recommended' => 1,
                'max_tokens' => 16384,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "sq", "am", "ar", "hy", "bn", "bs", "bg", "ca", "zh", "zh-TW", "hr", "cs", "da", "nl", "et", "fi", "fr", "fr-CA", "ka", "de", "el", "gu", "hi", "hu", "is", "id", "it", "ja", "kn", "kk", "ko", "lv", "lt", "mk", "ms", "ml", "mr", "mn", "my", "no", "fa", "pl", "pt", "pt-BR", "pa", "ro", "ru", "sr", "sk", "sl", "so", "es", "sw", "sv", "tl", "tg", "ta", "te", "th", "tr", "uk", "ur", "vi"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 1,
                'seq' => 6,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            3 => 
            array (
                'id' => 4,
                'name' => 'Alibaba Qwen2.5 7B Instruct',
                'key' => 'alibaba/qwen2.5/7b',
                'provider_id' => 3,
                'provider_model' => 'Qwen/Qwen2.5-7B-Instruct-Turbo',
                'type' => 'limited',
                'is_recommended' => 0,
                'max_tokens' => 4096,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "bn", "ru", "pt", "id", "ur", "de", "ja", "tr", "ko", "vi", "it", "th", "fa", "pl", "ms", "my", "tl", "nl", "cs", "km", "he", "lo"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 1,
                'seq' => 7,
                'is_active' => 0,
                'created_at' => NULL,
                'updated_at' => '2025-04-29 03:17:38',
                'deleted_at' => '2025-04-29 03:17:38',
            ),
            4 => 
            array (
                'id' => 5,
                'name' => 'Anthropic Claude 3.5 Haiku',
                'key' => 'anthropic/claude3.5/haiku',
                'provider_id' => 4,
                'provider_model' => 'claude-3-5-haiku-latest',
                'type' => 'limited',
                'is_recommended' => 1,
                'max_tokens' => 8192,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "ru", "pt", "de", "ja", "ko", "it"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '1.**********',
                'num_credits' => 1,
                'seq' => 7,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            5 => 
            array (
                'id' => 6,
            'name' => 'Meta Llama 3.3 70B Instruct (SpecDec)',
                'key' => 'meta/llama3.3/70b-specdec',
                'provider_id' => 1,
                'provider_model' => 'llama-3.3-70b-specdec',
                'type' => 'limited',
                'is_recommended' => 0,
                'max_tokens' => 2048,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "ru", "pt", "id", "de", "ja", "tr", "it", "th", "nl"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 1,
                'seq' => 9,
                'is_active' => 0,
                'created_at' => NULL,
                'updated_at' => '2025-04-29 03:18:17',
                'deleted_at' => '2025-04-29 03:18:17',
            ),
            6 => 
            array (
                'id' => 7,
                'name' => 'Meta Llama 3.3 70B Instruct',
                'key' => 'meta/llama3.3/70b-versatile',
                'provider_id' => 1,
                'provider_model' => 'llama-3.3-70b-versatile',
                'type' => 'limited',
                'is_recommended' => 1,
                'max_tokens' => 2048,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "ru", "pt", "id", "de", "ja", "tr", "it", "th", "nl"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 1,
                'seq' => 8,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            7 => 
            array (
                'id' => 8,
                'name' => 'Alibaba Qwen 2.5 32B Instruct',
                'key' => 'alibaba/qwen2.5/32b',
                'provider_id' => 1,
                'provider_model' => 'qwen-2.5-32b',
                'type' => 'limited',
                'is_recommended' => 0,
                'max_tokens' => 4096,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "bn", "ru", "pt", "id", "ur", "de", "ja", "tr", "ko", "vi", "it", "th", "fa", "pl", "ms", "my", "tl", "nl", "cs", "km", "he", "lo"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 1,
                'seq' => 11,
                'is_active' => 0,
                'created_at' => NULL,
                'updated_at' => '2025-04-30 00:00:57',
                'deleted_at' => '2025-04-30 00:00:57',
            ),
            8 => 
            array (
                'id' => 9,
                'name' => 'Mistral Small 3',
                'key' => 'mistral/small/24b',
                'provider_id' => 3,
                'provider_model' => 'mistralai/Mistral-Small-24B-Instruct-2501',
                'type' => 'limited',
                'is_recommended' => 1,
                'max_tokens' => 4096,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "fr", "de", "es", "it", "zh", "ja", "ko", "pt", "nl", "po"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 1,
                'seq' => 9,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            9 => 
            array (
                'id' => 10,
                'name' => 'Apologist Aquinas v4',
                'key' => 'apologist/aquinas/v4',
                'provider_id' => 5,
                'provider_model' => 'accounts/jake-carlson-255cd3/models/aquinas-v4',
                'type' => 'standard',
                'is_recommended' => 0,
                'max_tokens' => 2048,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "ru", "pt", "id", "de", "ja", "tr", "it", "th", "nl"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 3,
                'seq' => 21,
                'is_active' => 0,
                'created_at' => NULL,
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            10 => 
            array (
                'id' => 11,
                'name' => 'Mistral Mixtral MoE 8x22B Instruct',
                'key' => 'mistral/mixtral/8x22b',
                'provider_id' => 5,
                'provider_model' => 'accounts/fireworks/models/mixtral-8x22b-instruct',
                'type' => 'standard',
                'is_recommended' => 1,
                'max_tokens' => 2048,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "es", "it", "de", "fr"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '1.**********',
                'output_cost' => '1.**********',
                'num_credits' => 2,
                'seq' => 14,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-05-30 18:49:33',
                'deleted_at' => NULL,
            ),
            11 => 
            array (
                'id' => 12,
                'name' => 'Alibaba Qwen2.5 72B Instruct',
                'key' => 'alibaba/qwen2.5/72b',
                'provider_id' => 3,
                'provider_model' => 'Qwen/Qwen2.5-72B-Instruct-Turbo',
                'type' => 'standard',
                'is_recommended' => 0,
                'max_tokens' => 4096,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "bn", "ru", "pt", "id", "ur", "de", "ja", "tr", "ko", "vi", "it", "th", "fa", "pl", "ms", "my", "tl", "nl", "cs", "km", "he", "lo"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '1.**********',
                'output_cost' => '1.**********',
                'num_credits' => 2,
                'seq' => 17,
                'is_active' => 0,
                'created_at' => NULL,
                'updated_at' => '2025-04-30 00:10:47',
                'deleted_at' => '2025-04-30 00:10:47',
            ),
            12 => 
            array (
                'id' => 13,
                'name' => 'Microsoft WizardLM-2 8x22B',
                'key' => 'microsoft/wizardlm/8x22b',
                'provider_id' => 3,
                'provider_model' => 'microsoft/WizardLM-2-8x22B',
                'type' => 'standard',
                'is_recommended' => 0,
                'max_tokens' => 2048,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "ar", "bn", "bg", "zh", "hr", "cs", "da", "nl", "et", "tl", "fi", "fr", "fr-CA", "de", "el", "gu", "he", "hi", "hu", "id", "it", "ja", "kn", "ko", "lv", "lt", "ms", "mr", "no", "fa", "pl", "pt", "pt-BR", "pa", "ro", "ru", "sr", "sk", "sl", "es", "sv", "ta", "te", "th", "tr", "uk", "ur", "vi"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '1.**********',
                'output_cost' => '1.**********',
                'num_credits' => 2,
                'seq' => 15,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            13 => 
            array (
                'id' => 14,
                'name' => 'DeepSeek v3',
                'key' => 'deepseek/deepseek/v3',
                'provider_id' => 3,
                'provider_model' => 'deepseek-ai/DeepSeek-V3',
                'type' => 'standard',
                'is_recommended' => 1,
                'max_tokens' => 16384,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "af", "sq", "am", "ar", "hy", "az", "eu", "bn", "bg", "ca", "zh", "zh-TW", "hr", "cs", "da", "nl", "eo", "tl", "fi", "fr", "fr-CA", "gl", "ka", "de", "el", "gu", "ha", "he", "hi", "hu", "is", "ig", "id", "ga", "it", "ja", "kn", "kk", "km", "ko", "lo", "ms", "ml", "mt", "mr", "mn", "my", "ne", "no", "ps", "pl", "pt", "pt-BR", "pa", "ro", "ru", "gd", "sr", "si", "sk", "sl", "so", "es", "sw", "sv", "tg", "ta", "te", "th", "tr", "uk", "ur", "uz", "vi", "cy", "yo", "zu"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '1.**********',
                'output_cost' => '1.**********',
                'num_credits' => 2,
                'seq' => 16,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            14 => 
            array (
                'id' => 15,
                'name' => 'OpenAI o1 mini',
                'key' => 'openai/gpt/o1-mini',
                'provider_id' => 2,
                'provider_model' => 'o1-mini',
                'type' => 'standard',
                'is_recommended' => 0,
                'max_tokens' => 16384,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "sq", "am", "ar", "hy", "bn", "bs", "bg", "ca", "zh", "zh-TW", "hr", "cs", "da", "nl", "et", "fi", "fr", "fr-CA", "ka", "de", "el", "gu", "hi", "hu", "is", "id", "it", "ja", "kn", "kk", "ko", "lv", "lt", "mk", "ms", "ml", "mr", "mn", "my", "no", "fa", "pl", "pt", "pt-BR", "pa", "ro", "ru", "sr", "sk", "sl", "so", "es", "sw", "sv", "tl", "tg", "ta", "te", "th", "tr", "uk", "ur", "vi"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '1.**********',
                'output_cost' => '4.**********',
                'num_credits' => 3,
                'seq' => 21,
                'is_active' => 0,
                'created_at' => NULL,
                'updated_at' => '2025-04-29 03:17:24',
                'deleted_at' => '2025-04-29 03:17:24',
            ),
            15 => 
            array (
                'id' => 16,
                'name' => 'OpenAI o3 mini',
                'key' => 'openai/gpt/o3-mini',
                'provider_id' => 2,
                'provider_model' => 'o3-mini',
                'type' => 'standard',
                'is_recommended' => 1,
                'max_tokens' => 16384,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "sq", "am", "ar", "hy", "bn", "bs", "bg", "ca", "zh", "zh-TW", "hr", "cs", "da", "nl", "et", "fi", "fr", "fr-CA", "ka", "de", "el", "gu", "hi", "hu", "is", "id", "it", "ja", "kn", "kk", "ko", "lv", "lt", "mk", "ms", "ml", "mr", "mn", "my", "no", "fa", "pl", "pt", "pt-BR", "pa", "ro", "ru", "sr", "sk", "sl", "so", "es", "sw", "sv", "tl", "tg", "ta", "te", "th", "tr", "uk", "ur", "vi"]',
                'supports_reasoning_effort' => 1,
                'fallback_model_id' => NULL,
                'input_cost' => '1.**********',
                'output_cost' => '4.**********',
                'num_credits' => 3,
                'seq' => 18,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-06-07 01:51:45',
                'deleted_at' => NULL,
            ),
            16 => 
            array (
                'id' => 17,
                'name' => 'Meta Llama 3.1 405B',
                'key' => 'meta/llama3.1/405b',
                'provider_id' => 5,
                'provider_model' => 'accounts/fireworks/models/llama-v3p1-405b-instruct',
                'type' => 'standard',
                'is_recommended' => 0,
                'max_tokens' => 2048,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "es", "fr", "ar", "ru", "pt", "id", "de", "ja", "tr", "it", "nl"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '3.**********',
                'output_cost' => '3.**********',
                'num_credits' => 3,
                'seq' => 20,
                'is_active' => 0,
                'created_at' => NULL,
                'updated_at' => '2025-04-30 00:13:08',
                'deleted_at' => NULL,
            ),
            17 => 
            array (
                'id' => 18,
                'name' => '01.AI Yi Large',
                'key' => '01ai/yi/large',
                'provider_id' => 5,
                'provider_model' => 'accounts/yi-01-ai/models/yi-large',
                'type' => 'standard',
                'is_recommended' => 0,
                'max_tokens' => 2048,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "es", "ja"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '3.**********',
                'output_cost' => '3.**********',
                'num_credits' => 3,
                'seq' => 19,
                'is_active' => 0,
                'created_at' => NULL,
                'updated_at' => '2025-04-30 00:12:53',
                'deleted_at' => NULL,
            ),
            18 => 
            array (
                'id' => 19,
                'name' => 'xAI Grok 2',
                'key' => 'xai/grok/2',
                'provider_id' => 6,
                'provider_model' => 'grok-2-latest',
                'type' => 'premium',
                'is_recommended' => 0,
                'max_tokens' => 4096,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "bn", "ru", "pt", "id", "ur", "de", "ja", "tr", "ko", "vi", "it", "th", "fa", "pl", "ms", "my", "tl", "nl", "cs", "km", "he", "lo"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '2.**********',
                'output_cost' => '10.**********',
                'num_credits' => 5,
                'seq' => 23,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            19 => 
            array (
                'id' => 20,
                'name' => 'OpenAI ChatGPT-4o',
                'key' => 'openai/gpt/4o',
                'provider_id' => 2,
                'provider_model' => 'chatgpt-4o-latest',
                'type' => 'premium',
                'is_recommended' => 1,
                'max_tokens' => 16384,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "sq", "am", "ar", "hy", "bn", "bs", "bg", "ca", "zh", "zh-TW", "hr", "cs", "da", "nl", "et", "fi", "fr", "fr-CA", "ka", "de", "el", "gu", "hi", "hu", "is", "id", "it", "ja", "kn", "kk", "ko", "lv", "lt", "mk", "ms", "ml", "mr", "mn", "my", "no", "fa", "pl", "pt", "pt-BR", "pa", "ro", "ru", "sr", "sk", "sl", "so", "es", "sw", "sv", "tl", "tg", "ta", "te", "th", "tr", "uk", "ur", "vi", "he"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '2.**********',
                'output_cost' => '10.**********',
                'num_credits' => 5,
                'seq' => 24,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-05-30 14:25:37',
                'deleted_at' => NULL,
            ),
            20 => 
            array (
                'id' => 21,
                'name' => 'Anthropic Claude 3.5 Sonnet',
                'key' => 'anthropic/claude3.5/sonnet',
                'provider_id' => 4,
                'provider_model' => 'claude-3-5-sonnet-latest',
                'type' => 'premium',
                'is_recommended' => 0,
                'max_tokens' => 8192,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "ru", "pt", "de", "ja", "ko", "it"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '3.**********',
                'output_cost' => '15.**********',
                'num_credits' => 7,
                'seq' => 25,
                'is_active' => 0,
                'created_at' => NULL,
                'updated_at' => '2025-06-15 01:42:17',
                'deleted_at' => NULL,
            ),
            21 => 
            array (
                'id' => 22,
                'name' => 'Anthropic Claude 4 Sonnet',
                'key' => 'anthropic/claude4.0/sonnet',
                'provider_id' => 4,
                'provider_model' => 'claude-sonnet-4-0',
                'type' => 'reasoning',
                'is_recommended' => 1,
                'max_tokens' => 8192,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "ru", "pt", "de", "ja", "ko", "it", "id", "bn", "sw", "yo"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '3.**********',
                'output_cost' => '15.**********',
                'num_credits' => 7,
                'seq' => 31,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-05-31 05:00:54',
                'deleted_at' => NULL,
            ),
            22 => 
            array (
                'id' => 23,
                'name' => 'DeepSeek R1',
                'key' => 'deepseek/deepseek/r1',
                'provider_id' => 3,
                'provider_model' => 'deepseek-ai/DeepSeek-R1',
                'type' => 'reasoning',
                'is_recommended' => 0,
                'max_tokens' => 16384,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "af", "sq", "am", "ar", "hy", "az", "eu", "bn", "bg", "ca", "zh", "zh-TW", "hr", "cs", "da", "nl", "eo", "tl", "fi", "fr", "fr-CA", "gl", "ka", "de", "el", "gu", "ha", "he", "hi", "hu", "is", "ig", "id", "ga", "it", "ja", "kn", "kk", "km", "ko", "lo", "ms", "ml", "mt", "mr", "mn", "my", "ne", "no", "ps", "pl", "pt", "pt-BR", "pa", "ro", "ru", "gd", "sr", "si", "sk", "sl", "so", "es", "sw", "sv", "tg", "ta", "te", "th", "tr", "uk", "ur", "uz", "vi", "cy", "yo", "zu"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '7.**********',
                'output_cost' => '7.**********',
                'num_credits' => 4,
                'seq' => 28,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            23 => 
            array (
                'id' => 24,
                'name' => 'OpenAI o1',
                'key' => 'openai/gpt/o1',
                'provider_id' => 2,
                'provider_model' => 'o1',
                'type' => 'admin',
                'is_recommended' => 0,
                'max_tokens' => 16384,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "sq", "am", "ar", "hy", "bn", "bs", "bg", "ca", "zh", "zh-TW", "hr", "cs", "da", "nl", "et", "fi", "fr", "fr-CA", "ka", "de", "el", "gu", "hi", "hu", "is", "id", "it", "ja", "kn", "kk", "ko", "lv", "lt", "mk", "ms", "ml", "mr", "mn", "my", "no", "fa", "pl", "pt", "pt-BR", "pa", "ro", "ru", "sr", "sk", "sl", "so", "es", "sw", "sv", "tl", "tg", "ta", "te", "th", "tr", "uk", "ur", "vi"]',
                'supports_reasoning_effort' => 1,
                'fallback_model_id' => NULL,
                'input_cost' => '15.**********',
                'output_cost' => '60.**********',
                'num_credits' => 28,
                'seq' => 35,
                'is_active' => 0,
                'created_at' => NULL,
                'updated_at' => '2025-06-07 01:52:21',
                'deleted_at' => NULL,
            ),
            24 => 
            array (
                'id' => 25,
                'name' => 'Anthropic Claude 4 Opus',
                'key' => 'anthropic/claude4.0/opus',
                'provider_id' => 4,
                'provider_model' => 'claude-opus-4-0',
                'type' => 'admin',
                'is_recommended' => 1,
                'max_tokens' => 8192,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "ru", "pt", "de", "ja", "ko", "it", "id", "bn", "sw", "yo"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '15.**********',
                'output_cost' => '75.**********',
                'num_credits' => 33,
                'seq' => 36,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-05-31 04:56:53',
                'deleted_at' => NULL,
            ),
            25 => 
            array (
                'id' => 26,
                'name' => 'OpenAI GPT-4.1',
                'key' => 'openai/gpt/4.1',
                'provider_id' => 2,
                'provider_model' => 'gpt-4.1-2025-04-14',
                'type' => 'premium',
                'is_recommended' => 1,
                'max_tokens' => 32768,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "sq", "am", "ar", "hy", "bn", "bs", "bg", "ca", "zh", "zh-TW", "hr", "cs", "da", "nl", "et", "fi", "fr", "fr-CA", "ka", "de", "el", "gu", "hi", "hu", "is", "id", "it", "ja", "kn", "kk", "ko", "lv", "lt", "mk", "ms", "ml", "mr", "mn", "my", "no", "fa", "pl", "pt", "pt-BR", "pa", "ro", "ru", "sr", "sk", "sl", "so", "es", "sw", "sv", "tl", "tg", "ta", "te", "th", "tr", "uk", "ur", "vi"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '2.**********',
                'output_cost' => '8.**********',
                'num_credits' => 4,
                'seq' => 22,
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-05-31 05:22:19',
                'deleted_at' => NULL,
            ),
            26 => 
            array (
                'id' => 32,
                'name' => 'Google Gemini 2.5 Pro',
                'key' => 'google/gemini/2.5-pro',
                'provider_id' => 7,
                'provider_model' => 'gemini-2.5-pro-preview-03-25',
                'type' => 'reasoning',
                'is_recommended' => 1,
                'max_tokens' => 4096,
                'stop_sequence' => '<|eot_id|>',
                'strip_sequence' => '<|end_header_id|>',
                'languages' => '["ar", "bn", "bg", "zh", "zh-TW", "hr", "cs", "da", "nl", "en", "et", "fi", "fr", "de", "el", "he", "hi", "hu", "id", "it", "ja", "ko", "lv", "lt", "no", "pl", "pt", "ro", "ru", "sr", "sk", "sl", "es", "sw", "sv", "th", "tr", "uk", "vi"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '2.**********',
                'output_cost' => '15.**********',
                'num_credits' => 7,
                'seq' => 30,
                'is_active' => 1,
                'created_at' => '2025-04-05 03:08:08',
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            27 => 
            array (
                'id' => 33,
                'name' => 'Gemini Flash 2.0',
                'key' => 'google/gemini/2.0-flash',
                'provider_id' => 7,
                'provider_model' => 'gemini-2.0-flash',
                'type' => 'limited',
                'is_recommended' => 1,
                'max_tokens' => 4096,
                'stop_sequence' => '<|eot_id|>',
                'strip_sequence' => '<|end_header_id|>',
                'languages' => '["ar", "bn", "bg", "zh", "zh-TW", "hr", "cs", "da", "nl", "en", "et", "fi", "fr", "de", "el", "he", "hi", "hu", "id", "it", "ja", "ko", "lv", "lt", "no", "pl", "pt", "ro", "ru", "sr", "sk", "sl", "es", "sw", "sv", "th", "tr", "uk", "vi"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 1,
                'seq' => 3,
                'is_active' => 1,
                'created_at' => '2025-04-05 03:57:41',
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            28 => 
            array (
                'id' => 34,
                'name' => 'Alibaba Qwen QwQ 32B',
                'key' => 'alibaba/qwq/32b',
                'provider_id' => 1,
                'provider_model' => 'qwen-qwq-32b',
                'type' => 'reasoning',
                'is_recommended' => 1,
                'max_tokens' => 4096,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "bn", "ru", "pt", "id", "ur", "de", "ja", "tr", "ko", "vi", "it", "th", "fa", "pl", "ms", "my", "tl", "nl", "cs", "km", "he", "lo"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.2*********',
                'output_cost' => '0.3*********',
                'num_credits' => 1,
                'seq' => 26,
                'is_active' => 1,
                'created_at' => '2025-04-05 05:07:11',
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            29 => 
            array (
                'id' => 35,
            'name' => 'DeepSeek R1 (Fast)',
                'key' => 'deepseek/deepseek/r1-fast',
                'provider_id' => 5,
                'provider_model' => 'accounts/fireworks/models/deepseek-r1',
                'type' => 'reasoning',
                'is_recommended' => 1,
                'max_tokens' => 16384,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "af", "sq", "am", "ar", "hy", "az", "eu", "bn", "bg", "ca", "zh", "zh-TW", "hr", "cs", "da", "nl", "eo", "tl", "fi", "fr", "fr-CA", "gl", "ka", "de", "el", "gu", "ha", "he", "hi", "hu", "is", "ig", "id", "ga", "it", "ja", "kn", "kk", "km", "ko", "lo", "ms", "ml", "mt", "mr", "mn", "my", "ne", "no", "ps", "pl", "pt", "pt-BR", "pa", "ro", "ru", "gd", "sr", "si", "sk", "sl", "so", "es", "sw", "sv", "tg", "ta", "te", "th", "tr", "uk", "ur", "uz", "vi", "cy", "yo", "zu"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '3.**********',
                'output_cost' => '8.**********',
                'num_credits' => 5,
                'seq' => 29,
                'is_active' => 1,
                'created_at' => '2025-04-05 05:27:59',
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            30 => 
            array (
                'id' => 36,
                'name' => 'Meta Llama 4 Scout',
                'key' => 'meta/llama4/scout',
                'provider_id' => 1,
                'provider_model' => 'meta-llama/llama-4-scout-17b-16e-instruct',
                'type' => 'limited',
                'is_recommended' => 1,
                'max_tokens' => 8192,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "ru", "pt", "id", "de", "ja", "tr", "it", "th", "nl"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 1,
                'seq' => 10,
                'is_active' => 1,
                'created_at' => '2025-04-05 23:18:59',
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            31 => 
            array (
                'id' => 38,
                'name' => 'Meta Llama 4 Maverick',
                'key' => 'meta/llama4/maverick',
                'provider_id' => 1,
                'provider_model' => 'meta-llama/llama-4-maverick-17b-128e-instruct',
                'type' => 'standard',
                'is_recommended' => 1,
                'max_tokens' => 8192,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "ru", "pt", "id", "de", "ja", "tr", "it", "th", "nl"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 2,
                'seq' => 13,
                'is_active' => 1,
                'created_at' => '2025-04-05 23:25:44',
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            32 => 
            array (
                'id' => 39,
                'name' => 'Got Questions v1',
                'key' => 'apologist/gotquestions/v1',
                'provider_id' => 3,
                'provider_model' => 'apologist/Qwen2.5-72B-Instruct-993e3198',
                'type' => 'admin',
                'is_recommended' => 0,
                'max_tokens' => 4096,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "bn", "ru", "pt", "id", "ur", "de", "ja", "tr", "ko", "vi", "it", "th", "fa", "pl", "ms", "my", "tl", "nl", "cs", "km", "he", "lo"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '1.**********',
                'output_cost' => '1.**********',
                'num_credits' => 2,
                'seq' => 37,
                'is_active' => 1,
                'created_at' => '2025-04-08 02:17:11',
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            33 => 
            array (
                'id' => 40,
                'name' => 'xAI Grok 3',
                'key' => 'xai/grok/3',
                'provider_id' => 6,
                'provider_model' => 'grok-3-latest',
                'type' => 'reasoning',
                'is_recommended' => 1,
                'max_tokens' => 4096,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "bn", "ru", "pt", "id", "ur", "de", "ja", "tr", "ko", "vi", "it", "th", "fa", "pl", "ms", "my", "tl", "nl", "cs", "km", "he", "lo"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '3.**********',
                'output_cost' => '15.**********',
                'num_credits' => 7,
                'seq' => 32,
                'is_active' => 1,
                'created_at' => '2025-04-17 04:16:33',
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            34 => 
            array (
                'id' => 41,
            'name' => 'xAI Grok 3 (Fast)',
                'key' => 'xai/grok/3-fast',
                'provider_id' => 6,
                'provider_model' => 'grok-3-fast-latest',
                'type' => 'reasoning',
                'is_recommended' => 1,
                'max_tokens' => 4096,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "bn", "ru", "pt", "id", "ur", "de", "ja", "tr", "ko", "vi", "it", "th", "fa", "pl", "ms", "my", "tl", "nl", "cs", "km", "he", "lo"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '3.**********',
                'output_cost' => '15.**********',
                'num_credits' => 11,
                'seq' => 33,
                'is_active' => 1,
                'created_at' => '2025-04-17 04:18:10',
                'updated_at' => '2025-05-06 04:33:35',
                'deleted_at' => NULL,
            ),
            35 => 
            array (
                'id' => 42,
                'name' => 'xAI Grok 3 Mini',
                'key' => 'xai/grok/3-mini',
                'provider_id' => 6,
                'provider_model' => 'grok-3-mini-latest',
                'type' => 'limited',
                'is_recommended' => 1,
                'max_tokens' => 4096,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "bn", "ru", "pt", "id", "ur", "de", "ja", "tr", "ko", "vi", "it", "th", "fa", "pl", "ms", "my", "tl", "nl", "cs", "km", "he", "lo"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 1,
                'seq' => 11,
                'is_active' => 1,
                'created_at' => '2025-04-17 04:19:17',
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            36 => 
            array (
                'id' => 43,
            'name' => 'xAI Grok 3 Mini (Fast)',
                'key' => 'xai/grok/3-mini-fast',
                'provider_id' => 6,
                'provider_model' => 'grok-3-mini-fast-latest',
                'type' => 'standard',
                'is_recommended' => 0,
                'max_tokens' => 4096,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "zh", "hi", "es", "fr", "ar", "bn", "ru", "pt", "id", "ur", "de", "ja", "tr", "ko", "vi", "it", "th", "fa", "pl", "ms", "my", "tl", "nl", "cs", "km", "he", "lo"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '4.**********',
                'num_credits' => 2,
                'seq' => 17,
                'is_active' => 1,
                'created_at' => '2025-04-17 04:20:21',
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            37 => 
            array (
                'id' => 44,
                'name' => 'OpenAI o4 mini',
                'key' => 'openai/gpt/o4-mini',
                'provider_id' => 2,
                'provider_model' => 'o4-mini',
                'type' => 'reasoning',
                'is_recommended' => 1,
                'max_tokens' => 16384,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "sq", "am", "ar", "hy", "bn", "bs", "bg", "ca", "zh", "zh-TW", "hr", "cs", "da", "nl", "et", "fi", "fr", "fr-CA", "ka", "de", "el", "gu", "hi", "hu", "is", "id", "it", "ja", "kn", "kk", "ko", "lv", "lt", "mk", "ms", "ml", "mr", "mn", "my", "no", "fa", "pl", "pt", "pt-BR", "pa", "ro", "ru", "sr", "sk", "sl", "so", "es", "sw", "sv", "tl", "tg", "ta", "te", "th", "tr", "uk", "ur", "vi"]',
                'supports_reasoning_effort' => 1,
                'fallback_model_id' => NULL,
                'input_cost' => '1.**********',
                'output_cost' => '4.**********',
                'num_credits' => 3,
                'seq' => 27,
                'is_active' => 1,
                'created_at' => '2025-04-18 01:51:25',
                'updated_at' => '2025-06-07 01:51:53',
                'deleted_at' => NULL,
            ),
            38 => 
            array (
                'id' => 45,
                'name' => 'OpenAI o3',
                'key' => 'openai/gpt/o3',
                'provider_id' => 2,
                'provider_model' => 'o3',
                'type' => 'reasoning',
                'is_recommended' => 1,
                'max_tokens' => 16384,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "sq", "am", "ar", "hy", "bn", "bs", "bg", "ca", "zh", "zh-TW", "hr", "cs", "da", "nl", "et", "fi", "fr", "fr-CA", "ka", "de", "el", "gu", "hi", "hu", "is", "id", "it", "ja", "kn", "kk", "ko", "lv", "lt", "mk", "ms", "ml", "mr", "mn", "my", "no", "fa", "pl", "pt", "pt-BR", "pa", "ro", "ru", "sr", "sk", "sl", "so", "es", "sw", "sv", "tl", "tg", "ta", "te", "th", "tr", "uk", "ur", "vi"]',
                'supports_reasoning_effort' => 1,
                'fallback_model_id' => NULL,
                'input_cost' => '10.**********',
                'output_cost' => '40.**********',
                'num_credits' => 19,
                'seq' => 34,
                'is_active' => 1,
                'created_at' => '2025-04-18 01:52:45',
                'updated_at' => '2025-06-07 01:52:04',
                'deleted_at' => NULL,
            ),
            39 => 
            array (
                'id' => 46,
                'name' => 'OpenAI GPT-4.1 mini',
                'key' => 'openai/gpt/4.1-mini',
                'provider_id' => 2,
                'provider_model' => 'gpt-4.1-mini',
                'type' => 'limited',
                'is_recommended' => 1,
                'max_tokens' => 16384,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "sq", "am", "ar", "hy", "bn", "bs", "bg", "ca", "zh", "zh-TW", "hr", "cs", "da", "nl", "et", "fi", "fr", "fr-CA", "ka", "de", "el", "gu", "hi", "hu", "is", "id", "it", "ja", "kn", "kk", "ko", "lv", "lt", "mk", "ms", "ml", "mr", "mn", "my", "no", "fa", "pl", "pt", "pt-BR", "pa", "ro", "ru", "sr", "sk", "sl", "so", "es", "sw", "sv", "tl", "tg", "ta", "te", "th", "tr", "uk", "ur", "vi"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '1.**********',
                'num_credits' => 1,
                'seq' => 5,
                'is_active' => 1,
                'created_at' => '2025-04-18 01:54:41',
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            40 => 
            array (
                'id' => 47,
                'name' => 'OpenAI GPT-4.1 nano',
                'key' => 'openai/gpt/4.1-nano',
                'provider_id' => 2,
                'provider_model' => 'gpt-4.1-nano',
                'type' => 'limited',
                'is_recommended' => 0,
                'max_tokens' => 16384,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "sq", "am", "ar", "hy", "bn", "bs", "bg", "ca", "zh", "zh-TW", "hr", "cs", "da", "nl", "et", "fi", "fr", "fr-CA", "ka", "de", "el", "gu", "hi", "hu", "is", "id", "it", "ja", "kn", "kk", "ko", "lv", "lt", "mk", "ms", "ml", "mr", "mn", "my", "no", "fa", "pl", "pt", "pt-BR", "pa", "ro", "ru", "sr", "sk", "sl", "so", "es", "sw", "sv", "tl", "tg", "ta", "te", "th", "tr", "uk", "ur", "vi"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 1,
                'seq' => 2,
                'is_active' => 1,
                'created_at' => '2025-04-18 01:56:17',
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            41 => 
            array (
                'id' => 48,
                'name' => 'Alibaba Qwen 3 235B',
                'key' => 'alibaba/qwen3/235b',
                'provider_id' => 3,
                'provider_model' => 'Qwen/Qwen3-235B-A22B-fp8',
                'type' => 'standard',
                'is_recommended' => 1,
                'max_tokens' => 32768,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "fr", "fr-CA", "pt", "pt-BR", "de", "ro", "sv", "da", "bg", "ru", "cs", "el", "uk", "es", "nl", "sk", "hr", "pl", "lt", "no", "fa", "sl", "gu", "lv", "it", "oc", "ne", "mr", "be", "sr", "lb", "as", "cy", "szl", "awa", "mai", "bho", "sd", "ga", "hi", "pa", "bn", "or", "tg", "yi", "lmo", "lij", "scn", "gl", "ca", "is", "sq", "li", "af", "mk", "si", "ur", "bs", "hy", "zh", "zh-TW", "yue", "my", "ar", "he", "mt", "id", "ms", "tl", "ceb", "jw", "su", "min", "ban", "pag", "ilo", "ta", "te", "kn", "ml", "tr", "az", "uz", "kk", "ba", "tt", "th", "lo", "fi", "et", "hu", "vi", "km", "ja", "ko", "ka", "eu", "ht", "pap", "sw"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 1,
                'seq' => 12,
                'is_active' => 1,
                'created_at' => '2025-04-29 23:22:09',
                'updated_at' => '2025-04-30 00:11:41',
                'deleted_at' => NULL,
            ),
            42 => 
            array (
                'id' => 49,
                'name' => 'Alibaba Qwen 3 32B',
                'key' => 'alibaba/qwen3/32b',
                'provider_id' => 3,
                'provider_model' => 'Qwen-3-32B',
                'type' => 'standard',
                'is_recommended' => 1,
                'max_tokens' => 32768,
                'stop_sequence' => NULL,
                'strip_sequence' => NULL,
                'languages' => '["en", "fr", "fr-CA", "pt", "pt-BR", "de", "ro", "sv", "da", "bg", "ru", "cs", "el", "uk", "es", "nl", "sk", "hr", "pl", "lt", "no", "fa", "sl", "gu", "lv", "it", "oc", "ne", "mr", "be", "sr", "lb", "as", "cy", "szl", "awa", "mai", "bho", "sd", "ga", "hi", "pa", "bn", "or", "tg", "yi", "lmo", "lij", "scn", "gl", "ca", "is", "sq", "li", "af", "mk", "si", "ur", "bs", "hy", "zh", "zh-TW", "yue", "my", "ar", "he", "mt", "id", "ms", "tl", "ceb", "jw", "su", "min", "ban", "pag", "ilo", "ta", "te", "kn", "ml", "tr", "az", "uz", "kk", "ba", "tt", "th", "lo", "fi", "et", "hu", "vi", "km", "ja", "ko", "ka", "eu", "ht", "pap", "sw"]',
                'supports_reasoning_effort' => 0,
                'fallback_model_id' => NULL,
                'input_cost' => '0.**********',
                'output_cost' => '0.**********',
                'num_credits' => 1,
                'seq' => 12,
                'is_active' => 0,
                'created_at' => '2025-05-23 04:04:58',
                'updated_at' => '2025-05-23 04:04:58',
                'deleted_at' => NULL,
            ),
        ));
        
        
    }
}