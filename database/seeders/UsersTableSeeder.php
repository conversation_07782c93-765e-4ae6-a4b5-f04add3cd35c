<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class UsersTableSeeder extends Seeder
{
    /**
     * Auto generated seed file
     */
    public function run(): void
    {

        \DB::table('users')->delete();

        \DB::table('users')->insert([
            0 => [
                'id' => 1,
                'name' => 'AP DEV',
                'email' => '<EMAIL>',
                'email_verified_at' => get_now_timestamp(),
                'password' => '$2y$12$XUUUSLJxEx6WcoDzyPOW8eJDmOxS852LjXZGBGpEYLHPvDFeixZ92',
                'two_factor_secret' => null,
                'two_factor_recovery_codes' => null,
                'two_factor_confirmed_at' => null,
                'remember_token' => 'OdyVWqLtjLAy9S2k6jPa6KsTaJMuCfcbrDy1DScdcIbzmvrZuMuO6NUGa5Kk',
                'current_team_id' => 1,
                'avatar_path' => null,
                'created_at' => get_now_timestamp(),
                'updated_at' => get_now_timestamp(),
                'deleted_at' => null,
            ],

        ]);

    }
}
