<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class MembershipsTableSeeder extends Seeder
{
    /**
     * Auto generated seed file
     */
    public function run(): void
    {

        \DB::table('memberships')->delete();

        \DB::table('memberships')->insert([
            0 => [
                'id' => 1,
                'model_type' => \App\Models\User::class,
                'model_id' => 1,
                'team_id' => null,
                'role_id' => 1,
                'created_at' => get_now_timestamp(),
                'updated_at' => get_now_timestamp(),
                'deleted_at' => null,
            ],
            1 => [
                'id' => 2,
                'model_type' => \App\Models\User::class,
                'model_id' => 1,
                'team_id' => 1,
                'role_id' => 2,
                'created_at' => get_now_timestamp(),
                'updated_at' => get_now_timestamp(),
                'deleted_at' => null,
            ],
        ]);

    }
}
