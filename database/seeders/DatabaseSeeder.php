<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {

        $this->call(RolesTableSeeder::class);
        $this->call(PermissionsTableSeeder::class);
        $this->call(RoleHasPermissionsTableSeeder::class);

        $this->call(UsersTableSeeder::class);
        $this->call(TeamsTableSeeder::class);
        $this->call(MembershipsTableSeeder::class);

        $this->call(ProcessApprovalFlowsTableSeeder::class);
        $this->call(ProcessApprovalFlowStepsTableSeeder::class);

        $this->call(AgentModelProvidersTableSeeder::class);
        $this->call(AgentModelsTableSeeder::class);
    }
}
