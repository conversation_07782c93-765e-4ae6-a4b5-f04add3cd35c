<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class AgentModelProvidersTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('agent_model_providers')->delete();
        
        \DB::table('agent_model_providers')->insert(array (
            0 => 
            array (
                'id' => 1,
                'name' => 'Groq',
                'key' => 'groq',
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-03-04 08:33:31',
                'deleted_at' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'name' => 'OpenAI',
                'key' => 'openai',
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-03-04 08:33:44',
                'deleted_at' => NULL,
            ),
            2 => 
            array (
                'id' => 3,
                'name' => 'Together.ai',
                'key' => 'together',
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-03-04 08:33:57',
                'deleted_at' => NULL,
            ),
            3 => 
            array (
                'id' => 4,
                'name' => 'Anthropic',
                'key' => 'anthropic',
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-03-04 08:34:09',
                'deleted_at' => NULL,
            ),
            4 => 
            array (
                'id' => 5,
                'name' => 'Fireworks AI',
                'key' => 'fireworks',
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-03-04 08:34:35',
                'deleted_at' => NULL,
            ),
            5 => 
            array (
                'id' => 6,
                'name' => 'xAI',
                'key' => 'xai',
                'is_active' => 1,
                'created_at' => NULL,
                'updated_at' => '2025-03-04 08:34:47',
                'deleted_at' => NULL,
            ),
            6 => 
            array (
                'id' => 7,
                'name' => 'Google',
                'key' => 'google',
                'is_active' => 1,
                'created_at' => '2025-04-05 01:59:36',
                'updated_at' => '2025-04-05 01:59:36',
                'deleted_at' => NULL,
            ),
        ));
        
        
    }
}