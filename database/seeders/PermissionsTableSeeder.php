<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class PermissionsTableSeeder extends Seeder
{
    /**
     * Auto generated seed file
     */
    public function run(): void
    {

        \DB::table('permissions')->delete();

        \DB::table('permissions')->insert([
            0 => [
                'id' => 1,
                'name' => 'view_approval::flow',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            1 => [
                'id' => 2,
                'name' => 'view_any_approval::flow',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            2 => [
                'id' => 3,
                'name' => 'create_approval::flow',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            3 => [
                'id' => 4,
                'name' => 'update_approval::flow',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            4 => [
                'id' => 5,
                'name' => 'restore_approval::flow',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            5 => [
                'id' => 6,
                'name' => 'restore_any_approval::flow',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            6 => [
                'id' => 7,
                'name' => 'replicate_approval::flow',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            7 => [
                'id' => 8,
                'name' => 'reorder_approval::flow',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            8 => [
                'id' => 9,
                'name' => 'delete_approval::flow',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            9 => [
                'id' => 10,
                'name' => 'delete_any_approval::flow',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            10 => [
                'id' => 11,
                'name' => 'force_delete_approval::flow',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            11 => [
                'id' => 12,
                'name' => 'force_delete_any_approval::flow',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            12 => [
                'id' => 13,
                'name' => 'administer_approval::flow',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            13 => [
                'id' => 14,
                'name' => 'view_authentication::log',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            14 => [
                'id' => 15,
                'name' => 'view_any_authentication::log',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            15 => [
                'id' => 16,
                'name' => 'create_authentication::log',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            16 => [
                'id' => 17,
                'name' => 'update_authentication::log',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            17 => [
                'id' => 18,
                'name' => 'restore_authentication::log',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            18 => [
                'id' => 19,
                'name' => 'restore_any_authentication::log',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            19 => [
                'id' => 20,
                'name' => 'replicate_authentication::log',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            20 => [
                'id' => 21,
                'name' => 'reorder_authentication::log',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            21 => [
                'id' => 22,
                'name' => 'delete_authentication::log',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            22 => [
                'id' => 23,
                'name' => 'delete_any_authentication::log',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            23 => [
                'id' => 24,
                'name' => 'force_delete_authentication::log',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            24 => [
                'id' => 25,
                'name' => 'force_delete_any_authentication::log',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            25 => [
                'id' => 26,
                'name' => 'administer_authentication::log',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            26 => [
                'id' => 27,
                'name' => 'view_category',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            27 => [
                'id' => 28,
                'name' => 'view_any_category',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            28 => [
                'id' => 29,
                'name' => 'create_category',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            29 => [
                'id' => 30,
                'name' => 'update_category',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            30 => [
                'id' => 31,
                'name' => 'restore_category',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            31 => [
                'id' => 32,
                'name' => 'restore_any_category',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            32 => [
                'id' => 33,
                'name' => 'replicate_category',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            33 => [
                'id' => 34,
                'name' => 'reorder_category',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            34 => [
                'id' => 35,
                'name' => 'delete_category',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            35 => [
                'id' => 36,
                'name' => 'delete_any_category',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            36 => [
                'id' => 37,
                'name' => 'force_delete_category',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            37 => [
                'id' => 38,
                'name' => 'force_delete_any_category',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            38 => [
                'id' => 39,
                'name' => 'administer_category',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            39 => [
                'id' => 40,
                'name' => 'view_collection',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            40 => [
                'id' => 41,
                'name' => 'view_any_collection',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            41 => [
                'id' => 42,
                'name' => 'create_collection',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            42 => [
                'id' => 43,
                'name' => 'update_collection',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            43 => [
                'id' => 44,
                'name' => 'restore_collection',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            44 => [
                'id' => 45,
                'name' => 'restore_any_collection',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            45 => [
                'id' => 46,
                'name' => 'replicate_collection',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            46 => [
                'id' => 47,
                'name' => 'reorder_collection',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            47 => [
                'id' => 48,
                'name' => 'delete_collection',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            48 => [
                'id' => 49,
                'name' => 'delete_any_collection',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            49 => [
                'id' => 50,
                'name' => 'force_delete_collection',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            50 => [
                'id' => 51,
                'name' => 'force_delete_any_collection',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            51 => [
                'id' => 52,
                'name' => 'administer_collection',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            52 => [
                'id' => 53,
                'name' => 'view_contributor',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            53 => [
                'id' => 54,
                'name' => 'view_any_contributor',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            54 => [
                'id' => 55,
                'name' => 'create_contributor',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            55 => [
                'id' => 56,
                'name' => 'update_contributor',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            56 => [
                'id' => 57,
                'name' => 'restore_contributor',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            57 => [
                'id' => 58,
                'name' => 'restore_any_contributor',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            58 => [
                'id' => 59,
                'name' => 'replicate_contributor',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            59 => [
                'id' => 60,
                'name' => 'reorder_contributor',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            60 => [
                'id' => 61,
                'name' => 'delete_contributor',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            61 => [
                'id' => 62,
                'name' => 'delete_any_contributor',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            62 => [
                'id' => 63,
                'name' => 'force_delete_contributor',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            63 => [
                'id' => 64,
                'name' => 'force_delete_any_contributor',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            64 => [
                'id' => 65,
                'name' => 'administer_contributor',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            65 => [
                'id' => 66,
                'name' => 'promote_contributor',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            66 => [
                'id' => 67,
                'name' => 'promote_any_contributor',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            67 => [
                'id' => 68,
                'name' => 'view_email',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            68 => [
                'id' => 69,
                'name' => 'view_any_email',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            69 => [
                'id' => 70,
                'name' => 'create_email',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            70 => [
                'id' => 71,
                'name' => 'update_email',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            71 => [
                'id' => 72,
                'name' => 'restore_email',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            72 => [
                'id' => 73,
                'name' => 'restore_any_email',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            73 => [
                'id' => 74,
                'name' => 'replicate_email',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            74 => [
                'id' => 75,
                'name' => 'reorder_email',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            75 => [
                'id' => 76,
                'name' => 'delete_email',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            76 => [
                'id' => 77,
                'name' => 'delete_any_email',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            77 => [
                'id' => 78,
                'name' => 'force_delete_email',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            78 => [
                'id' => 79,
                'name' => 'force_delete_any_email',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            79 => [
                'id' => 80,
                'name' => 'administer_email',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            80 => [
                'id' => 81,
                'name' => 'view_role',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            81 => [
                'id' => 82,
                'name' => 'view_any_role',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            82 => [
                'id' => 83,
                'name' => 'create_role',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            83 => [
                'id' => 84,
                'name' => 'update_role',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            84 => [
                'id' => 85,
                'name' => 'delete_role',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            85 => [
                'id' => 86,
                'name' => 'delete_any_role',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            86 => [
                'id' => 87,
                'name' => 'view_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            87 => [
                'id' => 88,
                'name' => 'view_any_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            88 => [
                'id' => 89,
                'name' => 'create_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            89 => [
                'id' => 90,
                'name' => 'update_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            90 => [
                'id' => 91,
                'name' => 'restore_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            91 => [
                'id' => 92,
                'name' => 'restore_any_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            92 => [
                'id' => 93,
                'name' => 'replicate_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            93 => [
                'id' => 94,
                'name' => 'reorder_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            94 => [
                'id' => 95,
                'name' => 'delete_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            95 => [
                'id' => 96,
                'name' => 'delete_any_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            96 => [
                'id' => 97,
                'name' => 'force_delete_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            97 => [
                'id' => 98,
                'name' => 'force_delete_any_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            98 => [
                'id' => 99,
                'name' => 'administer_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            99 => [
                'id' => 100,
                'name' => 'promote_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            100 => [
                'id' => 101,
                'name' => 'promote_any_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            101 => [
                'id' => 102,
                'name' => 'lock_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            102 => [
                'id' => 103,
                'name' => 'lock_any_source',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            103 => [
                'id' => 104,
                'name' => 'view_source::contribution',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            104 => [
                'id' => 105,
                'name' => 'view_any_source::contribution',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            105 => [
                'id' => 106,
                'name' => 'create_source::contribution',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            106 => [
                'id' => 107,
                'name' => 'update_source::contribution',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            107 => [
                'id' => 108,
                'name' => 'restore_source::contribution',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            108 => [
                'id' => 109,
                'name' => 'restore_any_source::contribution',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            109 => [
                'id' => 110,
                'name' => 'replicate_source::contribution',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            110 => [
                'id' => 111,
                'name' => 'reorder_source::contribution',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            111 => [
                'id' => 112,
                'name' => 'delete_source::contribution',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            112 => [
                'id' => 113,
                'name' => 'delete_any_source::contribution',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            113 => [
                'id' => 114,
                'name' => 'force_delete_source::contribution',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            114 => [
                'id' => 115,
                'name' => 'force_delete_any_source::contribution',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            115 => [
                'id' => 116,
                'name' => 'administer_source::contribution',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            116 => [
                'id' => 117,
                'name' => 'view_team',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            117 => [
                'id' => 118,
                'name' => 'view_any_team',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            118 => [
                'id' => 119,
                'name' => 'create_team',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            119 => [
                'id' => 120,
                'name' => 'update_team',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            120 => [
                'id' => 121,
                'name' => 'restore_team',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            121 => [
                'id' => 122,
                'name' => 'restore_any_team',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            122 => [
                'id' => 123,
                'name' => 'replicate_team',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            123 => [
                'id' => 124,
                'name' => 'reorder_team',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            124 => [
                'id' => 125,
                'name' => 'delete_team',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            125 => [
                'id' => 126,
                'name' => 'delete_any_team',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            126 => [
                'id' => 127,
                'name' => 'force_delete_team',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            127 => [
                'id' => 128,
                'name' => 'force_delete_any_team',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            128 => [
                'id' => 129,
                'name' => 'administer_team',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            129 => [
                'id' => 130,
                'name' => 'view_token',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            130 => [
                'id' => 131,
                'name' => 'view_any_token',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            131 => [
                'id' => 132,
                'name' => 'create_token',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            132 => [
                'id' => 133,
                'name' => 'update_token',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            133 => [
                'id' => 134,
                'name' => 'restore_token',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            134 => [
                'id' => 135,
                'name' => 'restore_any_token',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            135 => [
                'id' => 136,
                'name' => 'replicate_token',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            136 => [
                'id' => 137,
                'name' => 'reorder_token',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            137 => [
                'id' => 138,
                'name' => 'delete_token',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            138 => [
                'id' => 139,
                'name' => 'delete_any_token',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            139 => [
                'id' => 140,
                'name' => 'force_delete_token',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            140 => [
                'id' => 141,
                'name' => 'force_delete_any_token',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            141 => [
                'id' => 142,
                'name' => 'administer_token',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            142 => [
                'id' => 143,
                'name' => 'view_user',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            143 => [
                'id' => 144,
                'name' => 'view_any_user',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            144 => [
                'id' => 145,
                'name' => 'create_user',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            145 => [
                'id' => 146,
                'name' => 'update_user',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            146 => [
                'id' => 147,
                'name' => 'restore_user',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            147 => [
                'id' => 148,
                'name' => 'restore_any_user',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            148 => [
                'id' => 149,
                'name' => 'replicate_user',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            149 => [
                'id' => 150,
                'name' => 'reorder_user',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            150 => [
                'id' => 151,
                'name' => 'delete_user',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            151 => [
                'id' => 152,
                'name' => 'delete_any_user',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            152 => [
                'id' => 153,
                'name' => 'force_delete_user',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            153 => [
                'id' => 154,
                'name' => 'force_delete_any_user',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            154 => [
                'id' => 155,
                'name' => 'administer_user',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            155 => [
                'id' => 156,
                'name' => 'page_ListAuthenticationLogs',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            156 => [
                'id' => 157,
                'name' => 'page_Backups',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            157 => [
                'id' => 158,
                'name' => 'page_HealthCheckResults',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            158 => [
                'id' => 159,
                'name' => 'page_MyProfilePage',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            159 => [
                'id' => 160,
                'name' => 'widget_AgentSourceHitsChart',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            160 => [
                'id' => 161,
                'name' => 'widget_AgentSourceReferralsChart',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            161 => [
                'id' => 162,
                'name' => 'widget_PendingSourcesTable',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            162 => [
                'id' => 163,
                'name' => 'widget_PendingContributorsTable',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            163 => [
                'id' => 164,
                'name' => 'widget_PendingCollectionsTable',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
            164 => [
                'id' => 165,
                'name' => 'widget_RecentHistoryTable',
                'guard_name' => 'web',
                'created_at' => '2024-05-22 15:37:49',
                'updated_at' => '2024-05-22 15:37:49',
            ],
        ]);

    }
}
