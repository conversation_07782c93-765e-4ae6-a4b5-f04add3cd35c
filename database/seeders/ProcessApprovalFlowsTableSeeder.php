<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ProcessApprovalFlowsTableSeeder extends Seeder
{
    /**
     * Auto generated seed file
     */
    public function run(): void
    {

        \DB::table('process_approval_flows')->delete();

        \DB::table('process_approval_flows')->insert([
            0 => [
                'id' => 1,
                'name' => 'Source',
                'approvable_type' => \App\Models\Source::class,
                'created_at' => get_now_timestamp(),
                'updated_at' => get_now_timestamp(),
            ],
            1 => [
                'id' => 2,
                'name' => 'Collection',
                'approvable_type' => \App\Models\Collection::class,
                'created_at' => get_now_timestamp(),
                'updated_at' => get_now_timestamp(),
            ],
            2 => [
                'id' => 3,
                'name' => 'Contributor',
                'approvable_type' => \App\Models\Contributor::class,
                'created_at' => get_now_timestamp(),
                'updated_at' => get_now_timestamp(),
            ],
        ]);

    }
}
