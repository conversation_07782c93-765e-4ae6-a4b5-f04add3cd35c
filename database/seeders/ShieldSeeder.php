<?php

namespace Database\Seeders;

use <PERSON><PERSON>hanSalle<PERSON>\FilamentShield\Support\Utils;
use Illuminate\Database\Seeder;
use <PERSON><PERSON>\Permission\PermissionRegistrar;

class ShieldSeeder extends Seeder
{
    public function run(): void
    {
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        $rolesWithPermissions = '[{"name":"Super Admin","guard_name":"web","permissions":["view_agent","view_any_agent","create_agent","update_agent","delete_agent","delete_any_agent","restore_agent","restore_any_agent","force_delete_agent","force_delete_any_agent","promote_agent","reorder_agent","replicate_agent","administer_agent","lock_agent","view_authentication::log","view_any_authentication::log","create_authentication::log","update_authentication::log","delete_authentication::log","delete_any_authentication::log","restore_authentication::log","restore_any_authentication::log","force_delete_authentication::log","force_delete_any_authentication::log","view_category","view_any_category","create_category","update_category","delete_category","delete_any_category","restore_category","restore_any_category","force_delete_category","force_delete_any_category","view_collection","view_any_collection","create_collection","update_collection","delete_collection","delete_any_collection","restore_collection","restore_any_collection","force_delete_collection","force_delete_any_collection","promote_collection","moderate_collection","import_collection","administer_collection","lock_collection","view_contributor","view_any_contributor","create_contributor","update_contributor","delete_contributor","delete_any_contributor","restore_contributor","restore_any_contributor","force_delete_contributor","force_delete_any_contributor","promote_contributor","moderate_contributor","administer_contributor","lock_contributor","merge_contributor","view_discussion","view_any_discussion","create_discussion","update_discussion","delete_discussion","delete_any_discussion","restore_discussion","restore_any_discussion","force_delete_discussion","force_delete_any_discussion","moderate_discussion","administer_discussion","view_organization","view_any_organization","create_organization","update_organization","delete_organization","delete_any_organization","restore_organization","restore_any_organization","force_delete_organization","force_delete_any_organization","promote_organization","moderate_organization","administer_organization","lock_organization","view_role","view_any_role","create_role","update_role","delete_role","delete_any_role","view_source","view_any_source","create_source","update_source","delete_source","delete_any_source","restore_source","restore_any_source","force_delete_source","force_delete_any_source","promote_source","moderate_source","import_source","administer_source","lock_source","view_team","view_any_team","create_team","update_team","delete_team","delete_any_team","restore_team","restore_any_team","force_delete_team","force_delete_any_team","administer_team","lock_team","view_token","view_any_token","create_token","update_token","delete_token","delete_any_token","restore_token","restore_any_token","force_delete_token","force_delete_any_token","view_user","view_any_user","create_user","update_user","delete_user","delete_any_user","restore_user","restore_any_user","force_delete_user","force_delete_any_user","administer_user","lock_user","page_ListAuthenticationLogs","page_Backups","page_HealthCheckResults","page_MyProfilePage","widget_PendingSourcesTable","widget_PendingContributorsTable","widget_PendingCollectionsTable","widget_RecentHistoryTable","view_approval::flow","view_any_approval::flow","create_approval::flow","update_approval::flow","delete_approval::flow","delete_any_approval::flow","restore_approval::flow","restore_any_approval::flow","force_delete_approval::flow","force_delete_any_approval::flow","view_email","view_any_email","create_email","update_email","delete_email","delete_any_email","restore_email","restore_any_email","force_delete_email","force_delete_any_email","moderate_agent","widget_PendingAgentsTable","widget_PendingOrganizationsTable"]},{"name":"Manager","guard_name":"web","permissions":["view_agent","view_any_agent","create_agent","update_agent","delete_agent","delete_any_agent","restore_agent","restore_any_agent","promote_agent","view_collection","view_any_collection","create_collection","update_collection","delete_collection","delete_any_collection","restore_collection","restore_any_collection","promote_collection","view_contributor","view_any_contributor","create_contributor","update_contributor","delete_contributor","delete_any_contributor","restore_contributor","restore_any_contributor","promote_contributor","view_discussion","view_any_discussion","create_discussion","update_discussion","delete_discussion","delete_any_discussion","restore_discussion","restore_any_discussion","view_organization","view_any_organization","create_organization","update_organization","delete_organization","delete_any_organization","restore_organization","restore_any_organization","promote_organization","view_source","view_any_source","create_source","update_source","delete_source","delete_any_source","restore_source","restore_any_source","promote_source","import_source","view_team","view_any_team","create_team","update_team","delete_team","delete_any_team","restore_team","restore_any_team","page_MyProfilePage","widget_PendingSourcesTable","widget_PendingContributorsTable","widget_PendingCollectionsTable","widget_RecentHistoryTable","widget_PendingAgentsTable","widget_PendingOrganizationsTable"]},{"name":"Member","guard_name":"web","permissions":["view_agent","view_collection","view_any_collection","create_collection","update_collection","delete_collection","delete_any_collection","restore_collection","restore_any_collection","view_contributor","view_any_contributor","create_contributor","update_contributor","delete_contributor","delete_any_contributor","restore_contributor","restore_any_contributor","view_discussion","view_any_discussion","create_discussion","update_discussion","delete_discussion","delete_any_discussion","restore_discussion","restore_any_discussion","view_organization","view_source","view_any_source","create_source","update_source","delete_source","delete_any_source","restore_source","restore_any_source","view_team","view_any_team","page_MyProfilePage","widget_PendingSourcesTable","widget_PendingContributorsTable","widget_PendingCollectionsTable","widget_RecentHistoryTable","widget_PendingAgentsTable","widget_PendingOrganizationsTable"]},{"name":"Admin","guard_name":"web","permissions":["view_agent","view_any_agent","create_agent","update_agent","delete_agent","delete_any_agent","restore_agent","restore_any_agent","promote_agent","reorder_agent","replicate_agent","administer_agent","view_authentication::log","view_any_authentication::log","view_category","view_any_category","view_collection","view_any_collection","create_collection","update_collection","delete_collection","delete_any_collection","restore_collection","restore_any_collection","promote_collection","moderate_collection","import_collection","administer_collection","view_contributor","view_any_contributor","create_contributor","update_contributor","delete_contributor","delete_any_contributor","restore_contributor","restore_any_contributor","promote_contributor","moderate_contributor","administer_contributor","merge_contributor","view_discussion","view_any_discussion","create_discussion","update_discussion","delete_discussion","delete_any_discussion","restore_discussion","restore_any_discussion","moderate_discussion","administer_discussion","view_organization","view_any_organization","create_organization","update_organization","delete_organization","delete_any_organization","restore_organization","restore_any_organization","promote_organization","moderate_organization","administer_organization","view_role","view_any_role","view_source","view_any_source","create_source","update_source","delete_source","delete_any_source","restore_source","restore_any_source","promote_source","moderate_source","import_source","administer_source","view_team","view_any_team","create_team","update_team","delete_team","delete_any_team","restore_team","restore_any_team","administer_team","view_token","view_any_token","create_token","update_token","view_user","view_any_user","create_user","update_user","delete_user","delete_any_user","restore_user","restore_any_user","administer_user","page_MyProfilePage","widget_PendingSourcesTable","widget_PendingContributorsTable","widget_PendingCollectionsTable","widget_RecentHistoryTable","view_email","view_any_email","widget_PendingAgentsTable","widget_PendingOrganizationsTable"]},{"name":"Moderator","guard_name":"web","permissions":["view_agent","view_any_agent","create_agent","update_agent","delete_agent","delete_any_agent","restore_agent","restore_any_agent","promote_agent","view_collection","view_any_collection","create_collection","update_collection","delete_collection","delete_any_collection","restore_collection","restore_any_collection","promote_collection","moderate_collection","import_collection","view_contributor","view_any_contributor","create_contributor","update_contributor","delete_contributor","delete_any_contributor","restore_contributor","restore_any_contributor","promote_contributor","moderate_contributor","merge_contributor","view_discussion","view_any_discussion","create_discussion","update_discussion","delete_discussion","delete_any_discussion","restore_discussion","restore_any_discussion","moderate_discussion","view_organization","view_any_organization","create_organization","update_organization","delete_organization","delete_any_organization","restore_organization","restore_any_organization","promote_organization","moderate_organization","view_source","view_any_source","create_source","update_source","delete_source","delete_any_source","restore_source","restore_any_source","promote_source","moderate_source","import_source","view_team","view_any_team","create_team","update_team","delete_team","delete_any_team","restore_team","restore_any_team","page_MyProfilePage","widget_PendingSourcesTable","widget_PendingContributorsTable","widget_PendingCollectionsTable","widget_RecentHistoryTable","moderate_agent","widget_PendingAgentsTable","widget_PendingOrganizationsTable"]}]';
        $directPermissions = '{"174":{"name":"view_source::contribution","guard_name":"web"},"175":{"name":"view_any_source::contribution","guard_name":"web"},"176":{"name":"create_source::contribution","guard_name":"web"},"177":{"name":"update_source::contribution","guard_name":"web"},"178":{"name":"delete_source::contribution","guard_name":"web"},"179":{"name":"delete_any_source::contribution","guard_name":"web"},"180":{"name":"restore_source::contribution","guard_name":"web"},"181":{"name":"restore_any_source::contribution","guard_name":"web"},"182":{"name":"force_delete_source::contribution","guard_name":"web"},"183":{"name":"force_delete_any_source::contribution","guard_name":"web"}}';

        static::makeRolesWithPermissions($rolesWithPermissions);
        static::makeDirectPermissions($directPermissions);

        $this->command->info('Shield Seeding Completed.');
    }

    protected static function makeRolesWithPermissions(string $rolesWithPermissions): void
    {
        if (! blank($rolePlusPermissions = json_decode($rolesWithPermissions, true))) {
            /** @var Model $roleModel */
            $roleModel = Utils::getRoleModel();
            /** @var Model $permissionModel */
            $permissionModel = Utils::getPermissionModel();

            foreach ($rolePlusPermissions as $rolePlusPermission) {
                $role = $roleModel::firstOrCreate([
                    'name' => $rolePlusPermission['name'],
                    'guard_name' => $rolePlusPermission['guard_name'],
                ]);

                if (! blank($rolePlusPermission['permissions'])) {
                    $permissionModels = collect($rolePlusPermission['permissions'])
                        ->map(fn ($permission) => $permissionModel::firstOrCreate([
                            'name' => $permission,
                            'guard_name' => $rolePlusPermission['guard_name'],
                        ]))
                        ->all();

                    $role->syncPermissions($permissionModels);
                }
            }
        }
    }

    public static function makeDirectPermissions(string $directPermissions): void
    {
        if (! blank($permissions = json_decode($directPermissions, true))) {
            /** @var Model $permissionModel */
            $permissionModel = Utils::getPermissionModel();

            foreach ($permissions as $permission) {
                if ($permissionModel::whereName($permission)->doesntExist()) {
                    $permissionModel::create([
                        'name' => $permission['name'],
                        'guard_name' => $permission['guard_name'],
                    ]);
                }
            }
        }
    }
}
