/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
DROP TABLE IF EXISTS `agent_collection`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `agent_collection` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `agent_id` bigint unsigned NOT NULL,
  `collection_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `agent_collection_agent_id_foreign` (`agent_id`),
  KEY `agent_collection_collection_id_foreign` (`collection_id`),
  CONSTRAINT `agent_collection_agent_id_foreign` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`),
  CONSTRAINT `agent_collection_collection_id_foreign` FOREIGN KEY (`collection_id`) REFERENCES `collections` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `agent_contributor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `agent_contributor` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `agent_id` bigint unsigned NOT NULL,
  `contributor_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `agent_contributor_agent_id_foreign` (`agent_id`),
  KEY `agent_contributor_contributor_id_foreign` (`contributor_id`),
  CONSTRAINT `agent_contributor_agent_id_foreign` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`),
  CONSTRAINT `agent_contributor_contributor_id_foreign` FOREIGN KEY (`contributor_id`) REFERENCES `contributors` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `agent_model_providers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `agent_model_providers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `agent_model_providers_name_unique` (`name`),
  UNIQUE KEY `agent_model_providers_key_unique` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `agent_models`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `agent_models` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `provider_id` bigint unsigned NOT NULL,
  `provider_model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('limited','standard','premium','reasoning','admin') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_recommended` tinyint(1) NOT NULL DEFAULT '0',
  `max_tokens` int NOT NULL DEFAULT '4096',
  `stop_sequence` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `strip_sequence` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `languages` json DEFAULT NULL,
  `supports_reasoning_effort` tinyint(1) NOT NULL DEFAULT '0',
  `fallback_model_id` bigint unsigned DEFAULT NULL,
  `backup_model_id` bigint unsigned DEFAULT NULL,
  `input_cost` decimal(20,10) DEFAULT NULL,
  `output_cost` decimal(20,10) DEFAULT NULL,
  `num_credits` smallint unsigned NOT NULL,
  `seq` smallint unsigned NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `agent_models_name_unique` (`name`),
  UNIQUE KEY `agent_models_key_unique` (`key`),
  UNIQUE KEY `agent_models_provider_model_unique` (`provider_model`),
  KEY `agent_models_provider_id_foreign` (`provider_id`),
  KEY `agent_models_backup_model_id_foreign` (`backup_model_id`),
  KEY `agent_models_fallback_model_id_foreign` (`fallback_model_id`),
  CONSTRAINT `agent_models_backup_model_id_foreign` FOREIGN KEY (`backup_model_id`) REFERENCES `agent_models` (`id`),
  CONSTRAINT `agent_models_fallback_model_id_foreign` FOREIGN KEY (`fallback_model_id`) REFERENCES `agent_models` (`id`),
  CONSTRAINT `agent_models_provider_id_foreign` FOREIGN KEY (`provider_id`) REFERENCES `agent_model_providers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `agent_prompts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `agent_prompts` (
  `id` bigint NOT NULL,
  `conversation_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `language` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `country` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `region` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `timezone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `prompted_at` timestamp(6) NULL DEFAULT NULL,
  `response_started_at` timestamp(6) NULL DEFAULT NULL,
  `response_completed_at` timestamp(6) NULL DEFAULT NULL,
  `prompt_chars` int DEFAULT NULL,
  `response_chars` int DEFAULT NULL,
  `prompt_tokens` int DEFAULT NULL,
  `response_tokens` int DEFAULT NULL,
  `chat_tokens` int DEFAULT NULL,
  `agent_id` bigint unsigned NOT NULL,
  `agent_model_id` bigint unsigned DEFAULT NULL,
  `agent_token_id` bigint unsigned DEFAULT NULL,
  `client` enum('standalone','embedded','api') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_translated` tinyint(1) NOT NULL DEFAULT '0',
  `is_nonbillable` tinyint(1) NOT NULL DEFAULT '0',
  `is_billed` tinyint(1) NOT NULL DEFAULT '0',
  `synced_at` timestamp NOT NULL,
  UNIQUE KEY `agent_prompts_id_unique` (`id`),
  KEY `agent_prompts_agent_id_foreign` (`agent_id`),
  KEY `agent_prompts_agent_model_id_foreign` (`agent_model_id`),
  KEY `agent_prompts_agent_token_id_foreign` (`agent_token_id`),
  KEY `agent_prompts_conversation_id` (`conversation_id`) USING BTREE,
  KEY `agent_prompts_session_id` (`session_id`) USING BTREE,
  KEY `agent_prompts_user_id` (`user_id`) USING BTREE,
  KEY `agent_prompts_language` (`language`) USING BTREE,
  KEY `agent_prompts_country` (`country`) USING BTREE,
  KEY `agent_prompts_region` (`region`) USING BTREE,
  KEY `agent_prompts_city` (`city`) USING BTREE,
  KEY `agent_prompts_timezone` (`timezone`) USING BTREE,
  KEY `agent_prompts_is_billed_index` (`is_billed`),
  CONSTRAINT `agent_prompts_agent_id_foreign` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`),
  CONSTRAINT `agent_prompts_agent_model_id_foreign` FOREIGN KEY (`agent_model_id`) REFERENCES `agent_models` (`id`),
  CONSTRAINT `agent_prompts_agent_token_id_foreign` FOREIGN KEY (`agent_token_id`) REFERENCES `agent_tokens` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `agent_questions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `agent_questions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `question` json NOT NULL,
  `agent_id` bigint unsigned NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `seq` tinyint NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `agent_questions_agent_id_foreign` (`agent_id`),
  CONSTRAINT `agent_questions_agent_id_foreign` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `agent_source`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `agent_source` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `agent_id` bigint unsigned NOT NULL,
  `source_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `agent_source_agent_id_foreign` (`agent_id`),
  KEY `agent_source_source_id_foreign` (`source_id`),
  CONSTRAINT `agent_source_agent_id_foreign` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`),
  CONSTRAINT `agent_source_source_id_foreign` FOREIGN KEY (`source_id`) REFERENCES `sources` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `agent_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `agent_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `agent_id` bigint unsigned NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `agent_tokens_agent_id_foreign` (`agent_id`),
  CONSTRAINT `agent_tokens_agent_id_foreign` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `agents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `agents` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `root_domain` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` json DEFAULT NULL,
  `vanity_domain` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `meta_title` json DEFAULT NULL,
  `meta_description` json DEFAULT NULL,
  `meta_keywords` json DEFAULT NULL,
  `favicon_path` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `icon_path` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `icon_color` char(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `display_font_url` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `body_font_url` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `beacon_icon_path` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `target_worldview` json DEFAULT NULL,
  `creator_name` json DEFAULT NULL,
  `creator_url` json DEFAULT NULL,
  `color` char(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `theme` enum('dark','light') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'dark',
  `custom_styles` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `custom_scripts` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `image_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `background_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `background_color` char(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `hide_header` tinyint(1) NOT NULL DEFAULT '0',
  `questions_title` json DEFAULT NULL,
  `footer_text` json DEFAULT NULL,
  `hide_footer_cta` tinyint(1) NOT NULL DEFAULT '0',
  `footer_cta_label` json DEFAULT NULL,
  `footer_cta_url` json DEFAULT NULL,
  `parent_id` bigint unsigned DEFAULT NULL,
  `model_id` bigint unsigned NOT NULL,
  `model_system_prompt` varchar(10000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `model_context_prompt` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `model_max_tokens` int NOT NULL DEFAULT '4096',
  `model_frequency_penalty` decimal(8,2) NOT NULL DEFAULT '0.00',
  `model_presence_penalty` decimal(8,2) NOT NULL DEFAULT '0.00',
  `model_reasoning_effort` enum('low','medium','high') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `model_temperature` double(8,2) NOT NULL DEFAULT '0.50',
  `model_top_p` double(8,2) NOT NULL DEFAULT '0.90',
  `intro_preamble` json DEFAULT NULL,
  `intro_headline` json DEFAULT NULL,
  `intro_description` json DEFAULT NULL,
  `response_footer_content` json DEFAULT NULL,
  `response_footer_threshold` tinyint DEFAULT NULL,
  `max_memories` tinyint unsigned NOT NULL DEFAULT '3',
  `persona_name` json DEFAULT NULL,
  `persona_tagline` json DEFAULT NULL,
  `persona_description` json DEFAULT NULL,
  `persona_avatar_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `disable_community_corpus` tinyint(1) NOT NULL DEFAULT '0',
  `use_team_corpus` tinyint(1) NOT NULL DEFAULT '1',
  `default_language` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `default_translation` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `supported_languages` json DEFAULT NULL,
  `auto_translate` tinyint(1) NOT NULL DEFAULT '0',
  `auto_translate_languages` json DEFAULT NULL,
  `supported_translations` json DEFAULT NULL,
  `has_custom_corpus` tinyint(1) NOT NULL DEFAULT '0',
  `corpus_api_key` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `classification_id` bigint unsigned DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `is_open_source` tinyint(1) NOT NULL DEFAULT '0',
  `marketplace_active` tinyint(1) NOT NULL DEFAULT '0',
  `is_selectable` tinyint(1) NOT NULL DEFAULT '0',
  `is_extensible` tinyint(1) NOT NULL DEFAULT '0',
  `is_template` tinyint(1) NOT NULL DEFAULT '0',
  `user_id` bigint unsigned DEFAULT NULL,
  `team_id` bigint unsigned DEFAULT NULL,
  `is_approved` tinyint(1) NOT NULL DEFAULT '0',
  `is_locked` tinyint(1) NOT NULL DEFAULT '0',
  `is_nonbillable` tinyint(1) NOT NULL DEFAULT '0',
  `seq` tinyint NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `agents_user_id_foreign` (`user_id`),
  KEY `agents_team_id_foreign` (`team_id`),
  KEY `agents_parent_id` (`parent_id`),
  KEY `agents_classification_id_foreign` (`classification_id`),
  KEY `agents_model_id_foreign` (`model_id`),
  CONSTRAINT `agents_classification_id_foreign` FOREIGN KEY (`classification_id`) REFERENCES `classifications` (`id`),
  CONSTRAINT `agents_model_id_foreign` FOREIGN KEY (`model_id`) REFERENCES `agent_models` (`id`),
  CONSTRAINT `agents_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `agents` (`id`),
  CONSTRAINT `agents_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`),
  CONSTRAINT `agents_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `authentication_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `authentication_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `authenticatable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `authenticatable_id` bigint unsigned NOT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `login_at` timestamp NULL DEFAULT NULL,
  `login_successful` tinyint(1) NOT NULL DEFAULT '0',
  `logout_at` timestamp NULL DEFAULT NULL,
  `cleared_by_user` tinyint(1) NOT NULL DEFAULT '0',
  `location` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `authentication_log_authenticatable_type_authenticatable_id_index` (`authenticatable_type`,`authenticatable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `breezy_sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `breezy_sessions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `authenticatable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `authenticatable_id` bigint unsigned NOT NULL,
  `panel_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `guard` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `expires_at` timestamp NULL DEFAULT NULL,
  `two_factor_secret` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `two_factor_recovery_codes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `two_factor_confirmed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `parent_id` bigint unsigned DEFAULT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tagline` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `categories_parent_id` (`parent_id`),
  CONSTRAINT `categories_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `categorizables`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `categorizables` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `category_id` bigint unsigned NOT NULL,
  `categorizable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `categorizable_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `categorizables_category_id_foreign` (`category_id`),
  KEY `categorizables_categorizable_type_categorizable_id_index` (`categorizable_type`,`categorizable_id`),
  CONSTRAINT `categorizables_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `classifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `classifications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `parent_id` bigint unsigned DEFAULT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tagline` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `classifications_parent_id` (`parent_id`),
  CONSTRAINT `classifications_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `classifications` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `collection_source`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `collection_source` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `collection_id` bigint unsigned NOT NULL,
  `source_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `collection_source_collection_id_foreign` (`collection_id`),
  KEY `collection_source_source_id_foreign` (`source_id`),
  CONSTRAINT `collection_source_collection_id_foreign` FOREIGN KEY (`collection_id`) REFERENCES `collections` (`id`),
  CONSTRAINT `collection_source_source_id_foreign` FOREIGN KEY (`source_id`) REFERENCES `sources` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `collections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `collections` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('anthology','list','periodical','rss','website','channel','podcast','series') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source_url_content_selector` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source_url_content_strip` varchar(5000) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source_url_author_selector` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source_url_published_selector` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source_url_image_selector` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source_url_render_js` tinyint(1) NOT NULL DEFAULT '0',
  `parent_id` bigint unsigned DEFAULT NULL,
  `external_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `classification_id` bigint unsigned DEFAULT NULL,
  `referral_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_approved` tinyint(1) NOT NULL DEFAULT '0',
  `social_active` tinyint(1) NOT NULL DEFAULT '0',
  `image_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `language` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'en',
  `auto_import_sources` tinyint(1) NOT NULL DEFAULT '0',
  `auto_transcribe_sources` tinyint(1) NOT NULL DEFAULT '0',
  `auto_categorize_sources` tinyint(1) NOT NULL DEFAULT '0',
  `auto_approve_sources` tinyint(1) NOT NULL DEFAULT '0',
  `auto_index_sources` tinyint(1) NOT NULL DEFAULT '0',
  `auto_list_sources` tinyint(1) NOT NULL DEFAULT '0',
  `default_source_weight` tinyint NOT NULL DEFAULT '2',
  `source_image_handling` enum('self','inherit','override') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'inherit',
  `enforce_source_excerpts` tinyint(1) NOT NULL DEFAULT '0',
  `source_title_strip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source_url_restriction` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source_contributor_id` bigint unsigned DEFAULT NULL,
  `source_contributor_role` enum('author','commentator','editor','foreword','translator','host','speaker','producer') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `enforce_source_categories` json DEFAULT NULL,
  `rating` decimal(3,2) DEFAULT NULL,
  `featured_seq` tinyint DEFAULT NULL,
  `num_views` bigint unsigned NOT NULL DEFAULT '0',
  `user_id` bigint unsigned NOT NULL,
  `team_id` bigint unsigned DEFAULT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_locked` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `collections_user_id_foreign` (`user_id`),
  KEY `collections_team_id_foreign` (`team_id`),
  KEY `collections_parent_id` (`parent_id`),
  KEY `collections_name_index` (`name`),
  KEY `collections_rating_index` (`rating`),
  KEY `collections_num_views_index` (`num_views`),
  KEY `collections_source_contributor_id_foreign` (`source_contributor_id`),
  KEY `collections_classification_id_foreign` (`classification_id`),
  CONSTRAINT `collections_classification_id_foreign` FOREIGN KEY (`classification_id`) REFERENCES `classifications` (`id`),
  CONSTRAINT `collections_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `collections` (`id`),
  CONSTRAINT `collections_source_contributor_id_foreign` FOREIGN KEY (`source_contributor_id`) REFERENCES `contributors` (`id`),
  CONSTRAINT `collections_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`),
  CONSTRAINT `collections_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `connected_accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `connected_accounts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `provider_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `avatar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `token` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `refresh_token` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `connected_accounts_user_id_foreign` (`user_id`),
  CONSTRAINT `connected_accounts_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `contributors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `contributors` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `title` enum('Abp.','Bp.','Bl.','Br.','Dcn.','Dr.','Fr.','Lady','Lord','Patr.','Pp.','Pr.','Rev.','St.','Sdn.','Sir','Sr.') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `suffix` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bio` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `born_in` smallint DEFAULT NULL,
  `died_in` smallint DEFAULT NULL,
  `referral_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `avatar_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_controversial` tinyint(1) NOT NULL DEFAULT '0',
  `is_approved` tinyint(1) NOT NULL DEFAULT '0',
  `social_active` tinyint(1) NOT NULL DEFAULT '0',
  `rating` decimal(3,2) DEFAULT NULL,
  `featured_seq` tinyint DEFAULT NULL,
  `num_views` bigint unsigned NOT NULL DEFAULT '0',
  `user_id` bigint unsigned DEFAULT NULL,
  `team_id` bigint unsigned DEFAULT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_locked` tinyint(1) NOT NULL DEFAULT '0',
  `merged_contributor_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `contributors_user_id_foreign` (`user_id`),
  KEY `contributors_team_id_foreign` (`team_id`),
  KEY `contributors_name_index` (`name`),
  KEY `contributors_rating_index` (`rating`),
  KEY `contributors_num_views_index` (`num_views`),
  KEY `contributors_merged_contributor_id` (`merged_contributor_id`),
  CONSTRAINT `contributors_merged_contributor_id` FOREIGN KEY (`merged_contributor_id`) REFERENCES `contributors` (`id`),
  CONSTRAINT `contributors_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`),
  CONSTRAINT `contributors_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `discussions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `discussions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_approved` tinyint(1) NOT NULL DEFAULT '0',
  `is_locked` tinyint(1) NOT NULL DEFAULT '0',
  `user_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `discussions_user_id_foreign` (`user_id`),
  CONSTRAINT `discussions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `features`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `features` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `scope` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `features_name_scope_unique` (`name`,`scope`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `filament_email_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `filament_email_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `from` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `to` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `cc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bcc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `text_body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `html_body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `raw_body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `attachments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `sent_debug_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `user_id` bigint unsigned DEFAULT NULL,
  `team_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `filament_email_log_user_id_foreign` (`user_id`),
  KEY `filament_email_log_team_id_foreign` (`team_id`),
  CONSTRAINT `filament_email_log_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`),
  CONSTRAINT `filament_email_log_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `frontend_listing_impressions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `frontend_listing_impressions` (
  `id` bigint unsigned NOT NULL,
  `frontend` enum('agent','social') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'agent',
  `item_model` enum('collection','contributor','organization','source') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `item_id` bigint unsigned NOT NULL,
  `prompt_id` bigint unsigned DEFAULT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `viewed_at` timestamp NOT NULL,
  `synced_at` timestamp NOT NULL,
  PRIMARY KEY (`id`),
  KEY `frontend_item_impressions_user_id_foreign` (`user_id`),
  KEY `frontend_item_impressions_item_model_index` (`item_model`),
  KEY `frontend_item_impressions_item_id_index` (`item_id`),
  KEY `frontend_item_impressions_prompt_id_index` (`prompt_id`),
  KEY `frontend_item_impressions_viewed_at_index` (`viewed_at`),
  KEY `frontend_item_impressions_synced_at_index` (`synced_at`),
  CONSTRAINT `frontend_item_impressions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `frontend_listing_referrals`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `frontend_listing_referrals` (
  `id` bigint unsigned NOT NULL,
  `frontend` enum('agent','social') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'agent',
  `item_model` enum('collection','contributor','organization','source') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `item_id` bigint unsigned NOT NULL,
  `prompt_id` bigint unsigned DEFAULT NULL,
  `referred_at` timestamp NOT NULL,
  `synced_at` timestamp NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `agent_source_referrals_source_id_foreign` (`item_id`),
  KEY `agent_source_referrals_prompt_id_index` (`prompt_id`),
  KEY `agent_source_referrals_referred_at_index` (`referred_at`),
  KEY `agent_source_referrals_synced_at_index` (`synced_at`),
  KEY `frontend_item_referrals_user_id_foreign` (`user_id`),
  KEY `frontend_item_referrals_item_model_index` (`item_model`),
  KEY `frontend_item_referrals_item_id_index` (`item_id`),
  CONSTRAINT `frontend_item_referrals_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `frontend_listing_urls`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `frontend_listing_urls` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `listable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `listable_id` bigint unsigned NOT NULL,
  `url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `frontend_listing_urls_listable_type_listable_id_index` (`listable_type`,`listable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `frontend_listing_views`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `frontend_listing_views` (
  `id` bigint unsigned NOT NULL,
  `frontend` enum('agent','social') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'agent',
  `item_model` enum('collection','contributor','organization','source') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `item_id` bigint unsigned NOT NULL,
  `prompt_id` bigint unsigned DEFAULT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `viewed_at` timestamp NOT NULL,
  `synced_at` timestamp NOT NULL,
  PRIMARY KEY (`id`),
  KEY `frontend_item_views_user_id_foreign` (`user_id`),
  KEY `frontend_item_views_item_model_index` (`item_model`),
  KEY `frontend_item_views_item_id_index` (`item_id`),
  KEY `frontend_item_views_prompt_id_index` (`prompt_id`),
  KEY `frontend_item_views_viewed_at_index` (`viewed_at`),
  KEY `frontend_item_views_synced_at_index` (`synced_at`),
  CONSTRAINT `frontend_item_views_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `health_check_result_history_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `health_check_result_history_items` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `check_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `check_label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notification_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `short_summary` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `meta` json NOT NULL,
  `ended_at` timestamp NOT NULL,
  `batch` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `health_check_result_history_items_created_at_index` (`created_at`),
  KEY `health_check_result_history_items_batch_index` (`batch`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `ledgers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ledgers` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `recordable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `recordable_id` bigint unsigned NOT NULL,
  `context` tinyint unsigned NOT NULL,
  `event` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `properties` json NOT NULL,
  `modified` json NOT NULL,
  `pivot` json NOT NULL,
  `extra` json NOT NULL,
  `url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `signature` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ledgers_recordable_type_recordable_id_index` (`recordable_type`,`recordable_id`),
  KEY `ledgers_user_id_user_type_index` (`user_id`,`user_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `memberships`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `memberships` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  `team_id` bigint unsigned DEFAULT NULL,
  `role_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `memberships_team_id_model_type_model_id_unique` (`team_id`,`model_type`,`model_id`),
  KEY `memberships_model_type_model_id_index` (`model_type`,`model_id`),
  KEY `memberships_role_id_foreign` (`role_id`),
  CONSTRAINT `memberships_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`),
  CONSTRAINT `memberships_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `model_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `model_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `notification_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notification_settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `type` enum('changes','approvals','comments','reviews','messages') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `frequency` enum('never','weekly','daily','immediately') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'daily',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `notification_settings_user_id_type_unique` (`user_id`,`type`),
  CONSTRAINT `notification_settings_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `organizations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organizations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('ministry','publisher','other') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `image_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `classification_id` bigint unsigned DEFAULT NULL,
  `tax_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `referral_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `header_image_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `social_active` tinyint(1) NOT NULL DEFAULT '0',
  `is_approved` tinyint(1) NOT NULL DEFAULT '0',
  `rating` decimal(3,2) DEFAULT NULL,
  `featured_seq` tinyint DEFAULT NULL,
  `num_views` bigint unsigned NOT NULL DEFAULT '0',
  `user_id` bigint unsigned NOT NULL,
  `team_id` bigint unsigned NOT NULL,
  `is_locked` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `organizations_user_id_foreign` (`user_id`),
  KEY `organizations_team_id_foreign` (`team_id`),
  KEY `organizations_name_index` (`name`),
  KEY `organizations_rating_index` (`rating`),
  KEY `organizations_num_views_index` (`num_views`),
  KEY `organizations_classification_id_foreign` (`classification_id`),
  CONSTRAINT `organizations_classification_id_foreign` FOREIGN KEY (`classification_id`) REFERENCES `classifications` (`id`),
  CONSTRAINT `organizations_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`),
  CONSTRAINT `organizations_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `process_approval_flow_steps`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `process_approval_flow_steps` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `process_approval_flow_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  `permissions` json DEFAULT NULL,
  `order` int DEFAULT NULL,
  `action` enum('APPROVE','VERIFY','CHECK') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'APPROVE',
  `active` tinyint NOT NULL DEFAULT '1',
  `tenant_id` varchar(38) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `process_approval_flow_steps_process_approval_flow_id_foreign` (`process_approval_flow_id`),
  KEY `process_approval_flow_steps_role_id_index` (`role_id`),
  KEY `process_approval_flow_steps_order_index` (`order`),
  KEY `process_approval_flow_steps_tenant_id_index` (`tenant_id`),
  CONSTRAINT `process_approval_flow_steps_process_approval_flow_id_foreign` FOREIGN KEY (`process_approval_flow_id`) REFERENCES `process_approval_flows` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `process_approval_flows`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `process_approval_flows` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `approvable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `process_approval_statuses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `process_approval_statuses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `approvable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `approvable_id` bigint unsigned NOT NULL,
  `steps` json DEFAULT NULL,
  `status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Created',
  `creator_id` bigint unsigned DEFAULT NULL,
  `tenant_id` varchar(38) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `process_approval_statuses_approvable_type_approvable_id_index` (`approvable_type`,`approvable_id`),
  KEY `process_approval_statuses_tenant_id_index` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `process_approvals`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `process_approvals` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `approvable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `approvable_id` bigint unsigned NOT NULL,
  `process_approval_flow_step_id` bigint unsigned DEFAULT NULL,
  `approval_action` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Approved',
  `approver_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `user_id` bigint unsigned NOT NULL,
  `tenant_id` varchar(38) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `process_approvals_approvable_type_approvable_id_index` (`approvable_type`,`approvable_id`),
  KEY `process_approvals_process_approval_flow_step_id_foreign` (`process_approval_flow_step_id`),
  KEY `process_approvals_user_id_foreign` (`user_id`),
  KEY `process_approvals_tenant_id_index` (`tenant_id`),
  CONSTRAINT `process_approvals_process_approval_flow_step_id_foreign` FOREIGN KEY (`process_approval_flow_step_id`) REFERENCES `process_approval_flow_steps` (`id`) ON DELETE CASCADE,
  CONSTRAINT `process_approvals_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `ratings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ratings` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `rating` int NOT NULL,
  `comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `rateable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `rateable_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ratings_rateable_type_rateable_id_index` (`rateable_type`,`rateable_id`),
  KEY `ratings_rateable_id_index` (`rateable_id`),
  KEY `ratings_rateable_type_index` (`rateable_type`),
  KEY `ratings_user_id_foreign` (`user_id`),
  CONSTRAINT `ratings_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `role_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`role_id`),
  KEY `role_has_permissions_role_id_foreign` (`role_id`),
  CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_team` tinyint(1) NOT NULL DEFAULT '0',
  `has_admin_access` tinyint(1) NOT NULL DEFAULT '0',
  `seq` tinyint DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `icon` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sessions` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `source_contributions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `source_contributions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `source_id` bigint unsigned NOT NULL,
  `contributor_id` bigint unsigned NOT NULL,
  `role` enum('author','commentator','editor','foreword','translator','host','speaker','producer') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `seq` tinyint NOT NULL DEFAULT '0',
  `team_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `source_contributions_source_id_foreign` (`source_id`),
  KEY `source_contributions_contributor_id_foreign` (`contributor_id`),
  KEY `source_contributions_team_id_foreign` (`team_id`),
  CONSTRAINT `source_contributions_contributor_id_foreign` FOREIGN KEY (`contributor_id`) REFERENCES `contributors` (`id`),
  CONSTRAINT `source_contributions_source_id_foreign` FOREIGN KEY (`source_id`) REFERENCES `sources` (`id`),
  CONSTRAINT `source_contributions_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sources`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sources` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `subtitle` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `type` enum('article','book','url','media','youtube','episode') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `classification_id` bigint unsigned DEFAULT NULL,
  `url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url_content_selector` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url_content_strip` varchar(5000) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url_author_selector` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url_published_selector` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url_image_selector` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url_render_js` tinyint(1) NOT NULL DEFAULT '0',
  `published_on` date DEFAULT NULL,
  `written_on` date DEFAULT NULL,
  `language` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'en',
  `user_id` bigint unsigned NOT NULL,
  `team_id` bigint unsigned DEFAULT NULL,
  `external_id` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `image_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `media_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `media_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `media_process_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `media_status` enum('pending','processing','failed','complete') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `media_error` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `text_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `text_imported_at` datetime DEFAULT NULL,
  `referral_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `agent_terms_accepted` tinyint(1) NOT NULL DEFAULT '0',
  `agent_active` tinyint(1) NOT NULL DEFAULT '0',
  `agent_hidden` tinyint(1) NOT NULL DEFAULT '0',
  `agent_indexed_at` timestamp NULL DEFAULT NULL,
  `social_active` tinyint(1) NOT NULL DEFAULT '0',
  `weight` tinyint unsigned DEFAULT '2',
  `is_locked` tinyint(1) NOT NULL DEFAULT '0',
  `is_approved` tinyint(1) NOT NULL DEFAULT '0',
  `last_imported_at` timestamp NULL DEFAULT NULL,
  `importer_collection_id` bigint unsigned DEFAULT NULL,
  `inherit_importer_settings` tinyint(1) NOT NULL DEFAULT '0',
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `rating` decimal(3,2) DEFAULT NULL,
  `featured_seq` tinyint DEFAULT NULL,
  `num_views` bigint unsigned NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sources_user_id_foreign` (`user_id`),
  KEY `sources_team_id_foreign` (`team_id`),
  KEY `sources_name_index` (`name`),
  KEY `sources_rating_index` (`rating`),
  KEY `sources_num_views_index` (`num_views`),
  KEY `sources_imported_at_index` (`text_imported_at`),
  KEY `sources_agent_indexed_at_index` (`agent_indexed_at`),
  KEY `sources_classification_id_foreign` (`classification_id`),
  KEY `sources_importer_collection_id_foreign` (`importer_collection_id`),
  CONSTRAINT `sources_classification_id_foreign` FOREIGN KEY (`classification_id`) REFERENCES `classifications` (`id`),
  CONSTRAINT `sources_importer_collection_id_foreign` FOREIGN KEY (`importer_collection_id`) REFERENCES `collections` (`id`),
  CONSTRAINT `sources_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`),
  CONSTRAINT `sources_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `spike_cart_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `spike_cart_items` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `cart_id` bigint unsigned NOT NULL,
  `product_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `quantity` smallint NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `spike_carts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `spike_carts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `billable_type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `billable_id` bigint unsigned NOT NULL,
  `promotion_code_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `paid_at` timestamp NULL DEFAULT NULL,
  `stripe_checkout_session_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `paddle_transaction_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `spike_carts_billable_type_billable_id_index` (`billable_type`,`billable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `spike_credit_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `spike_credit_transactions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `billable_type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `billable_id` bigint unsigned NOT NULL,
  `type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'adjustment',
  `credit_type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'credits',
  `credits` bigint NOT NULL,
  `cart_id` bigint unsigned DEFAULT NULL,
  `cart_item_id` bigint unsigned DEFAULT NULL,
  `subscription_id` bigint unsigned DEFAULT NULL,
  `subscription_item_id` bigint unsigned DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `notes` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `credit_transactions_billable_type_billable_id_credit_type_index` (`billable_type`,`billable_id`,`credit_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `spike_provide_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `spike_provide_history` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `billable_type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `billable_id` bigint unsigned NOT NULL,
  `related_item_type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `related_item_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `providable_key` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `providable_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `provided_at` timestamp NULL DEFAULT NULL,
  `failed_at` timestamp NULL DEFAULT NULL,
  `exception` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `spike_provide_history_billable_type_billable_id_index` (`billable_type`,`billable_id`),
  KEY `related_item_index` (`related_item_id`,`related_item_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `stripe_subscription_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `stripe_subscription_items` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `stripe_subscription_id` bigint unsigned NOT NULL,
  `stripe_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `stripe_product` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `stripe_price` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `quantity` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ssi_subscription_id_stripe_price_unique` (`stripe_subscription_id`,`stripe_price`),
  KEY `stripe_subscription_items_stripe_id_index` (`stripe_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `stripe_subscriptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `stripe_subscriptions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint unsigned NOT NULL,
  `type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `stripe_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `stripe_status` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `stripe_price` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `promotion_code_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `quantity` int DEFAULT NULL,
  `trial_ends_at` timestamp NULL DEFAULT NULL,
  `renews_at` timestamp NULL DEFAULT NULL,
  `ends_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ss_billable_id_stripe_status_index` (`team_id`,`stripe_status`),
  KEY `stripe_subscriptions_stripe_id_index` (`stripe_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `team_invitations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `team_invitations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint unsigned NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  `body` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_sent_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `accepted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `team_invitations_team_id_email_unique` (`team_id`,`email`),
  KEY `team_invitations_role_id_foreign` (`role_id`),
  KEY `team_invitations_accepted_at_index` (`accepted_at`),
  CONSTRAINT `team_invitations_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`),
  CONSTRAINT `team_invitations_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `teams`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `teams` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `avatar_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` bigint unsigned NOT NULL,
  `is_locked` tinyint(1) NOT NULL DEFAULT '0',
  `stripe_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pm_type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pm_last_four` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `trial_ends_at` timestamp NULL DEFAULT NULL,
  `is_prepaid_only` tinyint(1) NOT NULL DEFAULT '0',
  `is_nonbillable` tinyint(1) NOT NULL DEFAULT '0',
  `is_partner` tinyint(1) NOT NULL DEFAULT '0',
  `is_trial` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `teams_user_id_foreign` (`user_id`),
  KEY `teams_stripe_id_index` (`stripe_id`),
  CONSTRAINT `teams_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_entries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `telescope_entries` (
  `sequence` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `family_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `should_display_on_index` tinyint(1) NOT NULL DEFAULT '1',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`sequence`),
  UNIQUE KEY `telescope_entries_uuid_unique` (`uuid`),
  KEY `telescope_entries_batch_id_index` (`batch_id`),
  KEY `telescope_entries_family_hash_index` (`family_hash`),
  KEY `telescope_entries_created_at_index` (`created_at`),
  KEY `telescope_entries_type_should_display_on_index_index` (`type`,`should_display_on_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_entries_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `telescope_entries_tags` (
  `entry_uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`entry_uuid`,`tag`),
  KEY `telescope_entries_tags_tag_index` (`tag`),
  CONSTRAINT `telescope_entries_tags_entry_uuid_foreign` FOREIGN KEY (`entry_uuid`) REFERENCES `telescope_entries` (`uuid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_monitoring`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `telescope_monitoring` (
  `tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`tag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `two_factor_secret` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `two_factor_recovery_codes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `two_factor_confirmed_at` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `api_token` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `current_team_id` bigint unsigned DEFAULT NULL,
  `avatar_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_locked` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `views`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `views` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `viewable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `viewable_id` bigint unsigned NOT NULL,
  `visitor` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `collection` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `viewed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `views_viewable_type_viewable_id_index` (`viewable_type`,`viewable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 DROP FUNCTION IF EXISTS `url_decode` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` FUNCTION `url_decode`(original_text TEXT) RETURNS text CHARSET utf8mb3
    DETERMINISTIC
BEGIN
     DECLARE new_text TEXT DEFAULT NULL;
     DECLARE pointer INT DEFAULT 1;
     DECLARE end_pointer INT DEFAULT 1;
     DECLARE encoded_text TEXT DEFAULT NULL;
     DECLARE result_text TEXT DEFAULT NULL;
 
     SET new_text = REPLACE(original_text,'+',' ');
 
     SET pointer = LOCATE("%", new_text);
     while pointer <> 0 && pointer < (CHAR_LENGTH(new_text) - 2) DO
          SET end_pointer = pointer + 3;
          while MID(new_text, end_pointer, 1) = "%" DO
               SET end_pointer = end_pointer+3;
          END while;
 
          SET encoded_text = MID(new_text, pointer, end_pointer - pointer);
          SET result_text = CONVERT(UNHEX(REPLACE(encoded_text, "%", "")) USING utf8);
          SET new_text = REPLACE(new_text, encoded_text, result_text);
          SET pointer = LOCATE("%", new_text, pointer + CHAR_LENGTH(result_text));
     END while;
 
     return new_text;
 
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (1,'1_create_process_approval_flows_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (2,'2014_10_12_000000_create_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (3,'2014_10_12_100000_create_password_reset_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (4,'2014_10_12_200000_add_two_factor_columns_to_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (5,'2018_08_08_100000_create_telescope_entries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (6,'2018_11_21_000001_create_ledgers_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (7,'2019_08_19_000000_create_failed_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (8,'2019_12_14_000001_create_personal_access_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (9,'2020_05_21_100000_create_teams_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (10,'2020_05_21_300000_create_team_invitations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (11,'2023_12_31_054620_create_sessions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (12,'2024_01_01_185327_create_sources_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (13,'2024_01_01_185338_create_collections_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (14,'2024_01_01_185833_create_categories_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (15,'2024_01_01_191428_create_contributors_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (16,'2024_01_01_194048_create_source_contribution_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (17,'2024_01_01_200555_create_collection_source_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (18,'2024_01_03_034635_create_category_source_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (19,'2024_01_05_014234_create_breezy_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (20,'2024_01_05_185050_create_filament_email_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (21,'2024_01_06_021929_create_authentication_log_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (22,'2024_01_09_152558_create_permission_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (23,'2024_01_09_200000_create_memberships_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (24,'2024_01_10_041921_add_fields_to_roles',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (25,'2024_01_10_234145_create_connected_accounts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (26,'2024_01_10_234146_modify_users_table_nullable_password',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (27,'2024_01_22_045152_create_health_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (28,'2024_01_28_054413_add_fields_to_team_invitations',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (29,'2_create_process_approval_flow_steps_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (30,'3_create_process_approvals_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (31,'4_create_process_approval_statuses_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (32,'2024_02_20_005710_add_title_to_contributors',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (33,'2024_02_21_040806_add_birth_and_death_to_contributors',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (34,'2024_03_02_065555_add_fields_to_team_invitations',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (35,'2024_03_04_032323_rename_photo_to_avatar_on_contributors',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (36,'2024_03_08_021051_create_agent_stats_tables',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (37,'2024_04_07_022840_add_statement_to_sources_types',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (38,'2024_04_07_170024_rename_sources_rating_to_weight',6);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (39,'2024_04_14_205007_add_agent_hidden_to_sources',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (40,'2024_05_04_021908_update_role_descriptions',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (41,'2024_05_10_030826_add_team_id_to_filament_email_log',9);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (42,'2024_05_24_025200_add_is_approved_to_approvable_models',10);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (43,'2024_05_25_180516_add_url_and_types_to_sources_and_collections',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (44,'2024_05_26_160018_rename_document_id_to_external_id_on_sources',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (45,'2024_05_26_163523_add_auto_approve_sources_to_collections',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (46,'2024_05_27_053127_add_youtube_to_source_types',12);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (47,'2024_05_27_221302_add_media_processing_fields_to_sources',13);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (48,'2024_05_29_034652_add_external_id_to_collections',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (49,'2024_05_29_202929_add_podcast_collection_and_source_types',15);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (50,'2024_06_01_141938_add_agent_terms_to_sources',16);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (51,'2024_06_05_142208_add_api_token_to_users',17);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (52,'2024_06_07_065045_sync_source_contribution_roles',18);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (53,'2024_06_12_183048_migrate_collection_types',18);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (54,'2024_06_13_052021_add_name_to_contributors',19);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (55,'2024_06_14_203128_add_slugs_to_everything',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (56,'2024_06_15_002252_add_image_path_to_collections',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (57,'2024_06_17_045516_make_description_fields_longer',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (58,'2024_06_19_024531_create_ratings_table',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (59,'2024_06_19_035443_create_views_table',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (60,'2024_06_20_012211_add_tagline_to_categories',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (61,'2024_06_20_045952_add_organizations_table',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (62,'2024_06_20_134752_add_discussions_table',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (63,'2024_06_21_020947_standardize_referral_url_field',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (64,'2024_06_21_050002_add_categories_to_models',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (65,'2024_07_02_021558_setup_frontend_stats_tables',21);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (66,'2024_07_06_155157_add_rating_to_profiles',22);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (67,'2024_07_07_175950_add_team_id_to_source_contributions',23);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (68,'2024_07_07_205423_sync_contributor_titles',24);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (69,'2024_07_08_013225_add_subtitle_to_sources',25);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (70,'2024_07_08_205423_sync_contributor_titles',26);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (71,'2024_07_16_042306_add_frontend_field_to_stats_tables',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (72,'5_add_tenant_ids_to_approval_tables',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (73,'2024_07_19_015144_rename_frontend_item_tables',28);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (74,'2024_07_20_001606_add_active_flag_to_categories',29);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (75,'2024_07_20_012646_add_social_active_to_collections_and_contributors',30);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (76,'2024_07_24_033639_add_num_views_to_listings',31);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (77,'2024_07_24_040611_add_indexes_to_sort_fields',32);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (78,'2024_08_20_031438_add_language_to_collections',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (82,'2024_08_24_202259_create_agents_tables',34);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (83,'2024_09_01_214944_add_auto_source_fields_to_collections',35);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (84,'2024_09_02_015233_create_frontend_listing_urls_table',36);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (85,'2024_09_05_132233_add_is_controversial_to_contributors',37);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (86,'2024_09_10_063830_add_primary_category_to_sources',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (87,'2024_09_12_060144_lengthen_url_fields',39);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (88,'2024_09_22_043243_remove_slug_from_teams',40);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (89,'2024_09_25_024549_create_notification_settings_table',41);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (90,'2024_10_15_200518_add_imported_at_to_sources',42);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (91,'2024_10_25_070256_add_override_source_images_to_collections',43);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (92,'2024_10_28_081218_increase_length_of_source_external_id',44);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (93,'2024_10_28_224444_add_enforce_excerpts_to_collections_table',45);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (94,'2024_10_29_004035_change_collection_override_source_images_to_enum',46);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (95,'2024_10_30_180812_add_source_url_restriction_to_collections',47);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (96,'2024_10_30_184644_add_source_url_selectors_to_collections',47);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (97,'2024_10_31_024339_add_source_contributor_fields_to_collections',48);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (98,'2024_11_03_200601_add_last_imported_at_to_sources',49);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (99,'2024_11_10_221757_migrate_youtube_to_multiple_video_platforms',50);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (100,'2024_11_11_230319_add_featured_to_models',51);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (101,'2024_11_12_015111_add_auto_index_sources_to_collections',52);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (102,'2024_11_12_052838_change_source_indexed_to_timestamp',53);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (103,'2024_11_13_025201_add_seq_to_roles',53);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (104,'2024_11_13_044955_add_locks_to_models',53);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (105,'2024_11_14_014915_add_has_admin_access_to_roles',53);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (106,'2024_11_16_213629_add_owner_fields_to_agents',53);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (107,'2024_11_18_031038_add_collections_and_sources_to_agents',54);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (108,'2024_11_18_063115_add_corpus_fields_to_agents',54);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (109,'2024_11_18_131053_add_more_corpus_fields_to_agents',55);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (110,'2022_11_01_000001_create_features_table',56);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (111,'2024_11_23_002517_add_even_more_fields_to_agents',56);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (112,'2024_12_06_022113_add_agent_configuration_fields',57);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (113,'2024_12_08_161116_add_model_context_prompt_to_agents',58);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (114,'2024_12_08_183121_add_auto_translate_and_use_team_corpus_to_agents',59);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (115,'2024_12_08_211640_add_creator_name_to_agents',59);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (116,'2024_12_10_043241_add_custom_css_to_agents',60);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (117,'2024_12_13_022055_create_classifications_table',61);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (118,'2024_12_20_060747_add_auto_categorize_sources_to_collections',62);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (119,'2024_12_25_163426_add_root_domain_to_agents',63);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (120,'2024_12_31_023528_add_footer_options_to_agents',64);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (121,'2025_01_04_050224_create_agent_tokens_table',65);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (122,'2025_01_13_045351_rename_disable_rag_field_on_agents',66);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (123,'2025_01_19_192809_add_merged_contributor_id_to_contributors',67);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (124,'2025_01_20_195757_remove_auto_increment_from_stats_tables',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (125,'2025_03_02_054302_create_agent_models',69);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (126,'2019_05_03_000001_add_spark_columns_to_teams_table',70);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (127,'2019_05_03_000002_create_subscriptions_table',70);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (128,'2019_05_03_000003_create_subscription_items_table',70);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (129,'2019_06_03_000003_create_tax_rates_table',70);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (130,'2025_03_15_030216_create_agent_prompts_table',70);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (131,'2025_03_18_031820_add_details_to_agent_prompts',71);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (132,'2022_05_01_000001_create_customer_columns',72);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (133,'2022_06_01_000001_create_spike_carts_table',72);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (134,'2022_06_01_000002_create_spike_cart_items_table',72);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (135,'2022_06_01_000003_create_credit_transactions_table',72);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (136,'2024_01_01_000002_create_stripe_subscriptions_table',72);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (137,'2024_01_01_000003_create_stripe_subscription_items_table',72);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (138,'2024_01_02_000001_add_credit_type_to_spike_credit_transactions_table',72);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (139,'2024_01_02_000002_add_paddle_transaction_id_to_spike_carts_table',72);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (140,'2024_01_02_000003_create_spike_provide_history_table',72);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (141,'2024_01_02_000004_mark_existing_transactions_as_already_provided',72);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (142,'2025_03_22_042926_add_credit_usage_fields',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (143,'2025_03_26_004755_add_hide_header_to_agents',74);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (144,'2025_03_29_045302_add_meta_fields_to_agents',75);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (145,'2025_04_06_211104_add_url_content_strip_to_sources_and_collections',76);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (146,'2025_04_07_021353_rename_is_payg_to_is_prepaid_only_on_teams',76);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (147,'2025_04_14_013207_add_auto_translate_languages_to_agents',77);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (148,'2025_04_17_132055_add_is_trial_to_teams',78);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (151,'2025_04_20_162154_add_original_collection_id_to_sources',79);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (152,'2025_04_20_222712_add_inherit_importer_settings_to_sources',80);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (153,'2025_05_01_043135_add_questions_title_to_agents',81);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (155,'2025_05_03_033322_add_head_html_to_agents',82);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (156,'2025_05_11_025859_add_auto_transcribe_to_collections',83);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (157,'2025_06_06_224802_add_reasoning_effort_to_agents',84);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (159,'2025_06_13_031827_add_source_url_render_js_to_collections',85);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (160,'2025_06_15_015048_add_backup_model_id_to_agent_models',86);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (161,'2025_06_15_015048_add_fallback_model_id_to_agent_models',87);
