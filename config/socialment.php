<?php

// config for ChrisReedIO/Socialment
return [
    // The list of providers to display on the login page
    // You must install the appropriate Socialite provider package for each provider you want to use
    // if it isn't one supported by the core Laravel Socialite package.
    'providers' => [
        // Use the key based on the provider's documentation
        // 'azure' => [
        // 	'icon' => 'fab-microsoft', // Font Awesome icon class
        // 	'label' => 'Azure Active Directory', // The label to display for the provider
        // ]

        'apple' => [
            'icon' => 'fab-apple',
            'label' => 'Apple',
        ],

        'google' => [
            'icon' => 'fab-google',
            'label' => 'Google',
        ],

        'microsoft' => [
            'icon' => 'fab-microsoft',
            'label' => 'Microsoft',
        ],

        'facebook' => [
            'icon' => 'fab-facebook',
            'label' => 'Facebook',
        ],

        'linkedin' => [
            'icon' => 'fab-linkedin',
            'label' => 'LinkedIn',
        ],

        'twitter' => [
            'icon' => 'fab-x-twitter',
            'label' => 'X (Twitter)',
        ],

    ],

    'view' => [
        // Set the text above the provider list
        'prompt' => 'Sign In Using an Identity Provider',
        // Or change out the view completely with your own
        'providers-list' => 'socialment::providers-list',
    ],

    'routes' => [
        // The route to redirect to after a successful login
        'home' => 'filament.app.pages.dashboard',
    ],

    'models' => [
        // If you want to use a custom user model, you can specify it here.
        'user' => \App\Models\User::class,
    ],

];
