<?php

return [

    'connections' => [
        'sqs' => [
            'driver' => 'sqs-disk',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'prefix' => env('SQS_PREFIX', 'https://sqs.us-east-1.amazonaws.com/your-account-id'),
            'queue' => env('SQS_QUEUE', 'default'),
            'suffix' => '-'.env('APP_ENV'),
            'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
            'after_commit' => false,
            'retry_after' => 90,
            'disk_options' => [
                'always_store' => false,
                'cleanup' => true,
                'disk' => env('SQS_DISK', 'local'),
                'prefix' => 'jobs',
            ],
        ],
    ],

];
