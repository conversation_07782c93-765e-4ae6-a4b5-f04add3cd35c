<?php

$isLocal = (env('FILESYSTEM_DRIVER') == 'local');
$localRoot = $isLocal ? storage_path('app') : env('FILESYSTEM_LOCAL_ROOT');
$publicRoot = $isLocal ? storage_path('app/public') : env('FILESYSTEM_PUBLIC_ROOT');
$publicUrl = $isLocal ? env('APP_URL') . '/storage' : env('AWS_URL');

return [

    'disks' => [

        'local' => [
            'driver' => env('FILESYSTEM_DRIVER', 's3'),
            'root' => $localRoot,
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BUCKET'),
            'url' => env('AWS_URL'),
            'endpoint' => env('AWS_ENDPOINT'),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
            'throw' => false,
            'report' => false,
        ],

        'public' => [
            'driver' => env('FILESYSTEM_DRIVER', 's3'),
            'root' => $publicRoot,
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BUCKET'),
            'url' => $publicUrl,
            'endpoint' => env('AWS_ENDPOINT'),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
            'visibility' => 'public',
            'throw' => false,
            'report' => false,
        ],

        'backups' => [
            'driver' => env('FILESYSTEM_DRIVER', 's3'),
            'root' => env('APP_ENV').'/',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BUCKET_BACKUPS'),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
            'throw' => false,
            'report' => false,
        ],

    ],

];
