<?php

use Illuminate\Support\Str;

return [

    'connections' => [
        'backend' => [
            'driver' => 'mysql',
            'url' => env('DB_URL'),
            'host' => env('BACKEND_DB_HOST', '127.0.0.1'),
            'port' => env('BACKEND_DB_PORT', '3306'),
            'database' => env('BACKEND_DB_DATABASE', 'forge'),
            'username' => env('BACKEND_DB_USERNAME', 'forge'),
            'password' => env('BACKEND_DB_PASSWORD', ''),
            'unix_socket' => env('BACKEND_DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'sslmode' => env('BACKEND_DB_SSLMODE', 'prefer'),
            'options' => [
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
                PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
            ],
            'dump' => [
                'exclude_tables' => [
                    'failed_jobs',
                    'filament_email_log',
                    'telescope_entries',
                    'telescope_entries_tags',
                    'telescope_monitoring',
                ],
            ],
            'modes' => [
                'ALLOW_INVALID_DATES',
            ],
        ],

        'frontend' => [
            'driver' => 'pgsql',
            //            'url' => env('AGENT_DATABASE_URL'),
            'read' => [
                'host' => env('FRONTEND_DB_READ_HOST', env('FRONTEND_DB_HOST', '127.0.0.1')),
            ],
            'write' => [
                'host' => env('FRONTEND_DB_WRITE_HOST', env('FRONTEND_DB_HOST', '127.0.0.1')),
            ],
            //            'host' => env('FRONTEND_DB_HOST', '127.0.0.1'),
            'port' => env('FRONTEND_DB_PORT', '5432'),
            'database' => env('FRONTEND_DB_DATABASE', 'apg_agent'),
            'username' => env('FRONTEND_DB_USERNAME', 'postgres'),
            'password' => env('FRONTEND_DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => env('FRONTEND_DB_SSLMODE', 'prefer'),
            'sticky' => true,
            'dump' => [
                'add_extra_option' => '--format=c',
            ],
        ],
    ],

    'migrations' => [
        'table' => 'migrations',
        'update_date_on_publish' => false, // disable to preserve original behavior for existing applications
    ],

    'redis' => [

        'client' => env('REDIS_CLIENT', 'phpredis'),

        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'redis'),
            'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_ENV').'_'.env('APP_NAME', 'laravel'), '_').'_database_'),
        ],

        'default' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_DB', '0'),
            'scheme' => env('REDIS_SCHEME', 'tcp'),
            'ssl' => ['verify_peer' => false],
        ],

        'cache' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_CACHE_DB', '1'),
            'scheme' => env('REDIS_SCHEME', 'tcp'),
            'ssl' => ['verify_peer' => false],
        ],

        'clusters' => [
            'default' => [
                [
                    'url' => env('REDIS_URL'),
                    'host' => env('REDIS_HOST', '127.0.0.1'),
                    'username' => env('REDIS_USERNAME'),
                    'password' => env('REDIS_PASSWORD'),
                    'port' => env('REDIS_PORT', '6379'),
                    'database' => env('REDIS_DB', '0'),
                ],
            ],
        ],

    ],

];
