<?php

use App\Filament\Pages\App\Invitation;
use App\Http\Controllers\ApiAuth;
use Filament\Http\Middleware\Authenticate;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::domain(app_domain(env('APP_SUBDOMAIN_APP')))
    ->group(function () {

        Route::get('{tenant}/switch', function (int $tenant) {
            $tenant = auth()->user()->switchTenant($tenant);

            return redirect()->route('filament.app.pages.dashboard', ['tenant' => $tenant]);
        })
            ->name('filament.app.tenant.switch')
            ->middleware(Authenticate::class);

        Route::get('{tenant}/invitation', Invitation::class)
            ->name('filament.app.pages.invitation')
            ->middleware(Authenticate::class);

    });

Route::domain(app_domain(env('APP_SUBDOMAIN_API')))
    ->group(function () {

        $apiVersion = env('APP_API_VERSION');

        Route::get("/{$apiVersion}/token", [ApiAuth::class, 'getToken'])
            ->middleware('auth:sanctum')
            ->name("api.{$apiVersion}.token.get");

        Route::get("/{$apiVersion}/logout", [ApiAuth::class, 'logout'])
            ->middleware('auth:sanctum')
            ->name("api.{$apiVersion}.logout");

    });
