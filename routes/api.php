<?php

use App\Http\Controllers\ApiAuth;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::domain(app_domain(env('APP_SUBDOMAIN_API')))
    ->group(function () {

        $apiVersion = env('APP_API_VERSION');
        Route::get("/{$apiVersion}/user", [ApiAuth::class, 'getUser'])
            ->middleware('auth:sanctum')
            ->name("api.{$apiVersion}.user");

    });

