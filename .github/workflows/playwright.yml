name: API Automation Testing
on:
  pull_request:
    branches: [ main ]


jobs:
  Staging_Automation:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    environment: Staging
    env:
      API_STAGE: ${{ secrets.API_STAGE }}
      STAGE_TOKEN: ${{ secrets.STAGE_TOKEN }}
      STAGE_EMAIL: ${{ secrets.STAGE_EMAIL }}
      STAGE_PASSWORD: ${{ secrets.STAGE_PASSWORD }}
      STAGE_VERCEL_SHARE: ${{ secrets.STAGE_VERCEL_SHARE }}
      AGENT_STAGE_API_KEY: ${{ secrets.AGENT_STAGE_API_KEY }}
      AGENT_STAGING_PROMPT_URL: ${{ secrets.AGENT_STAGE_PROMPT_URL }}

    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: lts/*
    - name: Install dependencies
      working-directory: tests/Integration
      run: npm ci
    - name: Install Playwright Browsers
      working-directory: tests/Integration
      run: npx playwright install --with-deps
    - name: Run API Tests
      working-directory: tests/Integration
      run: npm run api:staging
    - uses: actions/upload-artifact@v4
      if: ${{ !cancelled() }}
      with:
        name: playwright-report
        path: tests/Integration/playwright-report/
        retention-days: 30
