<p align="center"><a href="https://apologistproject.org" target="_blank"><img src="https://ignite.apologist.com/img/apologist-ignite-dark.svg" width="300" alt="Apologist Ignite logo"></a></p>

## Introduction

**Apologist Ignite** is a monolithic <a href="https://php.net" target="_blank">PHP</a> / <a href="https://laravel.com/" target="_blank"><PERSON><PERSON></a> / <a href="https://filamentphp.com/" target="_blank">Filament</a> application that runs on the <a href="https://tallstack.dev/" target="_blank">TALL stack</a>.  

It's actually 4 applications in 1 that share common code and software design patterns:

### Customer App (_ignite_)
Interface to manage Contributors, Sources, and Collections. Eventually this will also be where organization create promotions for their resources, etc.

### Identity (_id_)
Centralized authentication, registration, and user profile. This solves the federated auth problem, and the entire Apologist ecosystem is tied together through it. 

### Admin (_admin_)
An interface for superadmins only. This includes application metrics and the ability to manage all application data.

### API (_api_)
The API for other applications (e.g., Apologist Agent and Apologist Social) to connect to and manipulate data in the Apologist ecosystem. We'll cover how to use the API from other applications below.

## Environment Setup

### Bundled Web Server (<a href="https://nginx.org/en/" target="_blank">nginx</a>)
You will need the ability to host the application via a web server on your local machine. There are a few options suggested below, in preference order. Take extra care to understand the requirements if diverging from what's recommended.

- **<a href="https://herd.laravel.com/" target="_blank">Laravel Herd</a>**
- **<a href="https://laravel.com/docs/10.x/valet" target="_blank">Laravel Valet</a>** (MacOS only)
- **<a href="https://laravel.com/docs/10.x/sail" target="_blank">Laravel Sail</a>** (Docker)  
- **<a href="https://laravel.com/docs/10.x/homestead" target="_blank">Laravel Homestead</a>** (Vagrant)  

Follow the instructions for your preferred web server.

### PHP 8.3+
To avoid any hiccups, ensure that your local machine is running the most recent version of PHP. 8.3+ is a hard requirement. You may need to look at the docs of your chosen web server to determine how best to see what version of PHP you are running and how to upgrade it to 8.3+.

### <a href="https://www.mysql.com/" target="_blank">Database (MySQL)</a>
The application will need to connect to a MySQL or MySQL-compatible (e.g., MariaDB) database. Most of the options above include a MySQL database, but you can also install one separately if you want setup to be more painful. Just kidding, it's pretty easy on Macs, at least.

### <a href="https://redis.io/" target="_blank">Redis</a>
Redis is an in-memory data store, often used as a cache, queued job broker, and session store. Ignite uses it as all 3. Technically, this is not a hard requirement as you can swap Redis out for other mechanisms. But it should be bundled with most of the options above, so you might as well use it.

### Mail Provider
Ignite sends emails triggered by specific events. For local development, using something like <a href="https://mailpit.axllent.org/" target="_blank">Mailpit</a> is recommended as it "traps" the email locally without being sent over the internet. You can see the emails in its local interface, but you don't have to worry about spam filters and delivery issues when developing locally.

### Composer
You will need to install <a href="https://getcomposer.org/" target="_blank">Composer</a>, PHP's package manager. It will probably be installed in some of the bundled packages above. But if not, install it now.

### NPM (Node Package Manager)
You will need to install <a href="https://www.npmjs.com/" target="_blank">NPM</a> to build frontend assets. If you don't already have it and it wasn't bundled with your base environment, install it now.

## Application Setup

### Clone this repository to your local machine
```
git clone https://github.com/apologist-project/apg-ignite.git
```  

### Set the application environment variables
In the cloned directory, copy the `.env.example` file to `.env`.  
**IMPORTANT:** You must set all the environment variables correctly before proceeding. Ask Jake for all the sensitive credentials.  

### Install PHP packages
We are utilizing several paid packages that will require credentials to install. Check with Jake to get the credentials before attempting to install PHP packages.

Once you have the credentials in place, run the following to install all the application's PHP packages:
```
composer install
```

### Install Javascript packages
Run the following to install the application's Javascript packages:
```
npm install
```

### Generate a new application key
From Terminal / Command Prompt in the root folder of the project: 
```
php artisan key:generate
```  

### Create the database schema
First, ensure your database server is running and configured correctly. Then, run: 
```
php artisan migrate
```

### Seed the database
If the previous step succeeded, it's time to populate the database with data you'll need. Run: 
```
php artisan db:seed
```

### Set up the domains on the web server
You must set up the application to be hosted on 4 separate subdomains on a common root domain. The application routes depend on these subdomains to be set up for it to determine what to present the user. 

For example, if your root domain is `apologist.test`, then configure your web server to route traffic to the public application folder for the following subdomains:

- ignite.apologist.test
- id.apologist.test
- admin.apologist.test
- api.apologist.test

**NOTES**: 
- The subdomains must be set up to be served over HTTPS for the secure session cookies to work. Check the docs of the bundled web server you chose for specific instructions on how to secure each subdomain.
- Each bundled web server has a different mechanism for configuring hosts. Some may ask for the domain w/o the top level domain name, and some may ask for the entire domain to be entered. e.g., Valet asks for `api.apologist` and not the full `api.apologist.test` when "linking" a new host as it automatically appends the `.test` at the end. Read the docs of the bundled web server you chose carefully.

### Update .env with the domain configuration
There are 2 environment variables you will need to ensure match the domains you set up in the previous step:

```
APP_URL=https://api.apologist.test # The full URL of the 'api' subdomain
APP_BASE_DOMAIN=apologist.test # The common root domain
```

### Set yourself as a superadmin user
A superadmin user was created as part of the database seeding operation. You can either modify that user, or create a brand new one for yourself.

#### Modify the existing superadmin
Tinker is an interactive application command prompt. Enter the following command: 
```
php artisan tinker
```
Copy the code below and modify the values in quotes on lines 2-4:

```php
$user = User::find(1);
$user->password = Hash::make('your-new-password');
$user->email = '<EMAIL>';
$user->name = 'Your Name';
$user->save();
```

Now paste the modified code into the Tinker terminal and hit Enter. You should now be able to login as a superadmin user with those credentials. Assuming that was successful, exit Tinker:
```
exit
```

#### Or, create a new superadmin
You may also simply go to the application once it's up and running and manually register a user. You'll need to verify the user's email by clicking on the verification email that is sent. Now enter the following command: 
```
php artisan shield:super-admin
```

You will be prompted for the ID of the user you want to convert to a superadmin. Enter it and hit Enter. The user is now a superadmin!

## Running the Environment

### Ensure the web server, database, and Redis are running
Before you try to access your application, make sure the services it depends on are installed and started.

### Start the asset bundler
```
npm run dev
```
This is a continuously running process, and you'll need to keep it running in the current Terminal / Command Prompt window and open a new one to proceed. While running, the asset bundler should automatically refresh your browser window when you make a change **most of the time**.

### Start the queue manager
```
php artisan horizon:watch
```
This is a continuously running process, and you'll need to keep it running in the current Terminal / Command Prompt window and open a new one to proceed.

### Tail the error logs (optional)
It's probably a good idea to see errors as they come through. On MacOS or Linux, run: 
```
tail -f storage/logs/laravel.log
```  

On Windows ... I have no idea, figure it out. There are also more sophisticated debugging tools in most IDEs, but you're on your own for that.

## Using the API

Ignite is the central hub for the Apologist ecosystem. While it serves 4 applications, it also can drive separate applications via its API.

### Token-Based Authentication

The API uses API tokens to authenticate to it. Long-lived tokens can be generated by superadmins for use in backend or frontend applications as needed. These tokens must manually be revoked as necessary.  

Once generated, the new API token is immediately shown to the user, but never shown again for security purposes. Make sure to copy the generated API token and keep it safe. To call to an API endpoint, you must include an API token as the `Bearer` in the `Authorization` request header. You should also include the `Accept` header to ensure the response is in JSON format.

```
Authorization: Bearer 4|asdfelwkdjfhdlshdlsl
Accept: application/json
```

### Personal Access Tokens
However, the use of personal access tokens specific to the logged in user is preferred for clientside applications (e.g., Apologist Agent and Apologist Social) as well as mobile apps. Since the token is tied to the end user, their token can be revoked without impacting other users. 

This mechanism uses a combination of cookie-based (session-based) authentication before presenting the user's decrypted personal access token, which the application can then use to make API calls on the user's behalf. 

The proper authentication sequence is outlined below. For the purposes of this scenario, we'll assume the root domain is _apologist.test_.

1. The application attempts to retrieve the current user's token at `https://api.apologist.test/v1/token`.
2. Since the user is not yet logged in, this will result in an HTTP error `401 Unauthorized`.
3. Upon receiving the response error, the application redirects the user to the federated login screen with a query parameter indicating where to redirect back to upon successful authentication. For example, redirect to: `https://id.apologist.com/login?redirect&redirect_url=https%3A%2F%2Fagent.apologist.test%2Fen`
4. Upon successful authentication, the user is redirected back to the URL specified in the `redirect_url` query parameter (most likely the originating application the user was redirected from).
5. The application again attempts to retrieve the current user's token at `https://api.apologist.test/v1/token`; only this time there is a JSON payload which includes the user's token. e.g., `{"token":"4|asdfelwkdjfhdlshdlsl"}`
6. That token can then be used to make authenticated calls to the API. For example, to retrieve information about the currently logged in user, make a GET call to `https://api.apologist.test/v1/user`.

Remember, all authenticated calls to the API should include the `Authorization` and `Accept` headers, as follows:

```
Authorization: Bearer 4|asdfelwkdjfhdlshdlsl
Accept: application/json
```

Import the <a href="https://drive.google.com/file/d/1qTqqqojY73keW68pKHMQVluuW8haM9ZL/view?usp=sharing" target="_blank">Postman workspace</a> to see all the endpoints and try them out.

### Deploying to Laravel Vapor
Make sure to encrypt the environment files whenever they change:

```
php artisan env:encrypt --env=development --key="{app_key}" --force
```
